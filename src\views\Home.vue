<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { gsap } from 'gsap';
import { useAuthStore } from '../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// References for animations
const heroSection = ref(null);
const heroTitle = ref(null);
const heroSubtitle = ref(null);
const heroCta = ref(null);

// Initialize arrays for refs - using proper array initialization
const featureRefs = ref([]);
const testimonialRefs = ref([]);
const pricingRefs = ref([]);

// Navigation functions
const navigateToAuth = () => {
  router.push('/auth');
};

const navigateToFeed = () => {
  if (authStore.isAuthenticated) {
    router.push('/feed');
  } else {
    router.push('/auth');
  }
};

const navigateToSubscription = () => {
  if (authStore.isAuthenticated) {
    router.push('/subscription');
  } else {
    router.push('/login');
  }
};

// Features data
const features = [
  {
    title: 'AI-Driven Feed',
    description: 'Personalized content powered by Graph Neural Networks and Transformers.',
    icon: 'M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5'
  },
  {
    title: 'Private Messaging',
    description: 'End-to-end encrypted messaging with edit/delete support and formal mode.',
    icon: 'M8.625 9.75a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375m-13.5 3.01c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.184-4.183a1.14 1.14 0 01.778-.332 48.294 48.294 0 005.83-.498c1.585-.233 2.708-1.626 2.708-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z'
  },
  {
    title: 'Emoji Studio',
    description: 'Create custom emojis with layers, filters, and animations for unique expressions.',
    icon: 'M15.182 15.182a4.5 4.5 0 01-6.364 0M21 12a9 9 0 11-18 0 9 9 0 0118 0zM9.75 9.75c0 .414-.168.75-.375.75S9 10.164 9 9.75 9.168 9 9.375 9s.375.336.375.75zm-.375 0h.008v.015h-.008V9.75zm5.625 0c0 .414-.168.75-.375.75s-.375-.336-.375-.75.168-.75.375-.75.375.336.375.75zm-.375 0h.008v.015h-.008V9.75z'
  },
  {
    title: 'Premium (NTA+)',
    description: 'Ad-free experience with extended stories, GIF avatars, and unlimited emoji creation.',
    icon: 'M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z'
  }
];

// Testimonials data
const testimonials = [
  {
    name: 'Alex Johnson',
    role: 'Content Creator',
    image: 'https://randomuser.me/api/portraits/men/32.jpg',
    quote: 'NeTuArk has completely transformed how I connect with my audience. The AI feed ensures my content reaches the right people.'
  },
  {
    name: 'Samantha Lee',
    role: 'Photographer',
    image: 'https://randomuser.me/api/portraits/women/44.jpg',
    quote: 'The Emoji Studio is a game-changer! I can create custom reactions that perfectly match my photography brand.'
  },
  {
    name: 'Marcus Chen',
    role: 'NTA+ Subscriber',
    image: 'https://randomuser.me/api/portraits/men/22.jpg',
    quote: 'Going premium was the best decision. No ads, extended stories, and the AI assistant makes posting so much easier.'
  }
];

// Pricing data
const pricingPlans = [
  {
    name: 'Free',
    price: '$0',
    period: 'forever',
    features: [
      'AI-driven content feed',
      '24h Stories',
      '10 custom emojis',
      'Basic messaging',
      'Limited AI assistant (3/hr)'
    ],
    cta: 'Join Now',
    highlighted: false
  },
  {
    name: 'NTA+',
    price: '$3',
    period: 'per month',
    features: [
      'Ad-free experience',
      '48h Stories',
      'Unlimited custom emojis',
      'GIF/MP4 profile pictures',
      'Unlimited AI assistant',
      'Priority in search results',
      'Exclusive seasonal content'
    ],
    cta: 'Go Premium',
    highlighted: true
  }
];

// Intersection Observer for scroll animations
const setupScrollAnimations = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Add animation class when element is in view
        entry.target.classList.add('animate-in');
        observer.unobserve(entry.target);
      }
    });
  }, { threshold: 0.1 });

  // Observe all elements with the 'animate-on-scroll' class
  document.querySelectorAll('.animate-on-scroll').forEach(el => {
    observer.observe(el);
  });
};

// Initialize animations
onMounted(() => {
  // Hero section animations
  gsap.fromTo(heroTitle.value,
    { opacity: 0, y: 30 },
    { opacity: 1, y: 0, duration: 1, ease: 'power3.out' }
  );

  gsap.fromTo(heroSubtitle.value,
    { opacity: 0, y: 20 },
    { opacity: 1, y: 0, duration: 1, delay: 0.3, ease: 'power3.out' }
  );

  gsap.fromTo(heroCta.value,
    { opacity: 0, y: 20 },
    { opacity: 1, y: 0, duration: 1, delay: 0.6, ease: 'power3.out' }
  );

  // Setup scroll animations for other sections
  setupScrollAnimations();
});
</script>

<template>
  <div class="bg-dark-bg text-white">
    <!-- Navigation/Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-7xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <!-- Logo - Short version for mobile, long version for desktop -->
          <img src="/logo1.png" alt="NeTuArk Logo" class="h-10 md:hidden" />
          <img src="/logo2.png" alt="NeTuArk Logo" class="hidden md:block h-10" />
        </div>
        <div class="flex items-center gap-4">
          <button @click="navigateToLogin" class="text-gray-300 hover:text-white transition-colors">Login</button>
          <button @click="navigateToRegister" class="neon-button py-2 px-4">Sign Up</button>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section ref="heroSection" class="relative min-h-screen flex flex-col items-center justify-center text-center px-4 py-20 pt-24">
      <!-- Background gradient -->
      <div class="absolute inset-0 bg-gradient-to-b from-darker-bg via-dark-bg to-darker-bg"></div>

      <!-- Animated background elements -->
      <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-neon-purple/10 blur-3xl"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-neon-blue/10 blur-3xl"></div>
        <div class="absolute top-1/2 left-1/2 w-72 h-72 rounded-full bg-neon-pink/10 blur-3xl"></div>
      </div>

      <!-- Content -->
      <div class="relative z-10 max-w-5xl mx-auto">
        <h1 ref="heroTitle" class="text-5xl md:text-7xl font-bold mb-6">
          <span class="neon-text-blue">NeTuArk</span> —
          <span class="neon-text-purple">Social Reimagined</span>
        </h1>

        <p ref="heroSubtitle" class="text-xl md:text-2xl mb-12 text-gray-300 max-w-3xl mx-auto">
          <span class="text-neon-blue font-bold">The World Is Opening</span> - Experience the next generation of social networking with AI-driven discovery,
          real-time interaction, and unparalleled privacy controls.
        </p>

        <div ref="heroCta" class="flex flex-col sm:flex-row gap-6 justify-center">
          <button @click="navigateToAuth" class="neon-button">
            Join Now
          </button>
          <button @click="navigateToFeed" class="px-6 py-3 rounded-md font-bold transition-all duration-300 border-2 border-neon-purple bg-transparent text-white hover:bg-neon-purple/10">
            See How It Works
          </button>
        </div>
      </div>

      <!-- Scroll indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 px-4 bg-darker-bg">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-4xl font-bold text-center mb-16 animate-on-scroll">
          <span class="neon-text-pink">Powerful</span> Features
        </h2>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="bg-dark-bg p-6 rounded-lg border border-gray-800 animate-on-scroll"
            :ref="el => { if (el) featureRefs.value.push(el) }"
          >
            <div class="w-12 h-12 mb-4 text-neon-blue">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" :d="feature.icon" />
              </svg>
            </div>
            <h3 class="text-xl font-bold mb-2">{{ feature.title }}</h3>
            <p class="text-gray-400">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- App Screenshots/Mockups -->
    <section class="py-20 px-4">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-4xl font-bold text-center mb-16 animate-on-scroll">
          <span class="neon-text-blue">Experience</span> the Platform
        </h2>

        <div class="relative h-[500px] md:h-[600px] animate-on-scroll">
          <!-- This would be replaced with actual mockup images -->
          <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-3xl aspect-[16/9] rounded-xl bg-gradient-to-r from-neon-blue/20 via-neon-purple/20 to-neon-pink/20 border border-gray-700 shadow-lg overflow-hidden">
            <div class="absolute inset-0 flex items-center justify-center">
              <p class="text-xl text-gray-400">App Interface Mockup</p>
            </div>
          </div>

          <!-- Floating UI elements for visual effect -->
          <div class="absolute top-[20%] left-[15%] w-48 h-24 rounded-lg bg-dark-bg border border-gray-700 shadow-neon-blue animate-float">
            <div class="h-full flex items-center justify-center">
              <p class="text-sm text-gray-400">Feed View</p>
            </div>
          </div>

          <div class="absolute bottom-[25%] right-[18%] w-40 h-40 rounded-lg bg-dark-bg border border-gray-700 shadow-neon-purple animate-float" style="animation-delay: -2s;">
            <div class="h-full flex items-center justify-center">
              <p class="text-sm text-gray-400">Messaging</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 px-4 bg-darker-bg">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-4xl font-bold text-center mb-16 animate-on-scroll">
          <span class="neon-text-purple">User</span> Highlights
        </h2>

        <div class="grid md:grid-cols-3 gap-8">
          <div
            v-for="(testimonial, index) in testimonials"
            :key="index"
            class="bg-dark-bg p-6 rounded-lg border border-gray-800 animate-on-scroll"
            :ref="el => { if (el) testimonialRefs.value.push(el) }"
          >
            <div class="flex items-center mb-4">
              <img :src="testimonial.image" :alt="testimonial.name" class="w-12 h-12 rounded-full mr-4">
              <div>
                <h3 class="font-bold">{{ testimonial.name }}</h3>
                <p class="text-sm text-gray-400">{{ testimonial.role }}</p>
              </div>
            </div>
            <p class="text-gray-300 italic">"{{ testimonial.quote }}"</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-20 px-4">
      <div class="max-w-7xl mx-auto">
        <h2 class="text-4xl font-bold text-center mb-16 animate-on-scroll">
          <span class="neon-text-pink">Choose</span> Your Plan
        </h2>

        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          <div
            v-for="(plan, index) in pricingPlans"
            :key="index"
            class="animate-on-scroll rounded-xl overflow-hidden"
            :class="plan.highlighted ? 'border-2 border-neon-blue' : 'border border-gray-700'"
            :ref="el => { if (el) pricingRefs.value.push(el) }"
          >
            <div
              class="p-6"
              :class="plan.highlighted ? 'bg-gradient-to-br from-dark-bg to-neon-blue/10' : 'bg-dark-bg'"
            >
              <h3 class="text-2xl font-bold mb-2">{{ plan.name }}</h3>
              <div class="flex items-end mb-6">
                <span class="text-4xl font-bold">{{ plan.price }}</span>
                <span class="text-gray-400 ml-2">{{ plan.period }}</span>
              </div>

              <ul class="mb-8 space-y-3">
                <li
                  v-for="(feature, featureIndex) in plan.features"
                  :key="featureIndex"
                  class="flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-neon-blue" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  <span>{{ feature }}</span>
                </li>
              </ul>

              <button
                @click="navigateToSubscription"
                class="w-full py-3 rounded-md font-bold transition-all duration-300"
                :class="plan.highlighted ? 'neon-button' : 'border border-gray-600 hover:border-neon-purple hover:bg-neon-purple/10'"
              >
                {{ plan.cta }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="py-12 px-4 bg-darker-bg border-t border-gray-800">
      <div class="max-w-7xl mx-auto">
        <div class="grid md:grid-cols-4 gap-8 mb-12">
          <div>
            <div class="flex flex-col items-start">
              <img src="/logo2.png" alt="NeTuArk Logo" class="h-8 mb-4" />
              <p class="text-gray-400 mb-4">The World Is Opening</p>
              <div class="flex space-x-4">
                <!-- Social icons would go here -->
                <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
                  <span class="sr-only">Twitter</span>
                  <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          <div>
            <h4 class="font-bold mb-4">Platform</h4>
            <ul class="space-y-2">
              <li><router-link to="/feed" class="text-gray-400 hover:text-white transition-colors">Features</router-link></li>
              <li><router-link to="/subscription" class="text-gray-400 hover:text-white transition-colors">Pricing</router-link></li>
              <li><router-link to="/feed" class="text-gray-400 hover:text-white transition-colors">FAQ</router-link></li>
            </ul>
          </div>

          <div>
            <h4 class="font-bold mb-4">Company</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Blog</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Careers</a></li>
            </ul>
          </div>

          <div>
            <h4 class="font-bold mb-4">Legal</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Cookie Policy</a></li>
            </ul>
          </div>
        </div>

        <div class="pt-8 border-t border-gray-800 text-center text-gray-500">
          <p>&copy; {{ new Date().getFullYear() }} NeTuArk. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
/* Animation classes for scroll animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-on-scroll.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animation delay for grid items */
.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }
</style>
