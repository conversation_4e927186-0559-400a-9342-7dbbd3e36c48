<template>
  <div class="create-content-view">
    <!-- Header -->
    <div class="page-header">
      <div class="container mx-auto px-4">
        <div class="header-content">
          <div class="header-text">
            <h1 class="page-title">Create Content</h1>
            <p class="page-subtitle">Share your thoughts, moments, and creativity with the world</p>
          </div>
          <div class="header-decoration">
            <div class="floating-icon">
              <i class="fas fa-magic"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <div class="container mx-auto px-4">
        <div class="content-wrapper">
          <!-- Unified Creator Component -->
          <div class="creator-container">
            <UnifiedContentCreator @content-created="handleContentCreated" />
          </div>

          <!-- Tips Section -->
          <div class="tips-section">
            <div class="tips-card">
              <div class="tips-header">
                <i class="fas fa-lightbulb"></i>
                <h3>Content Tips</h3>
              </div>
              <div class="tips-list">
                <div class="tip-item">
                  <i class="fas fa-image text-blue-500"></i>
                  <div>
                    <strong>Visual Content:</strong>
                    <span>Images and videos get 3x more engagement</span>
                  </div>
                </div>
                <div class="tip-item">
                  <i class="fas fa-hashtag text-green-500"></i>
                  <div>
                    <strong>Use Hashtags:</strong>
                    <span>AI will automatically tag your content for better discovery</span>
                  </div>
                </div>
                <div class="tip-item">
                  <i class="fas fa-clock text-purple-500"></i>
                  <div>
                    <strong>Stories:</strong>
                    <span>Perfect for sharing moments that disappear after 24-48 hours</span>
                  </div>
                </div>
                <div class="tip-item">
                  <i class="fas fa-users text-orange-500"></i>
                  <div>
                    <strong>Audience:</strong>
                    <span>Choose your visibility settings to control who sees your content</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Modal -->
    <div v-if="showSuccessModal" class="success-modal-overlay" @click="closeSuccessModal">
      <div class="success-modal" @click.stop>
        <div class="success-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <h3 class="success-title">{{ successMessage.title }}</h3>
        <p class="success-text">{{ successMessage.text }}</p>
        <div class="success-actions">
          <button @click="closeSuccessModal" class="success-btn">
            <i class="fas fa-arrow-left"></i>
            Create Another
          </button>
          <button @click="goToFeed" class="success-btn primary">
            <i class="fas fa-home"></i>
            View Feed
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import UnifiedContentCreator from '@/components/create/UnifiedContentCreator.vue';

// Router
const router = useRouter();

// State
const showSuccessModal = ref(false);
const lastCreatedType = ref('post');

// Computed
const successMessage = computed(() => {
  if (lastCreatedType.value === 'story') {
    return {
      title: '🎉 Story Shared!',
      text: 'Your story has been shared and will be visible to your audience for 24-48 hours.'
    };
  } else {
    return {
      title: '🎉 Post Created!',
      text: 'Your post has been published and is now visible in the feed.'
    };
  }
});

// Methods
function handleContentCreated(data) {
  lastCreatedType.value = data.type;
  showSuccessModal.value = true;
}

function closeSuccessModal() {
  showSuccessModal.value = false;
}

function goToFeed() {
  showSuccessModal.value = false;
  router.push('/feed');
}
</script>

<style scoped>
.create-content-view {
  @apply min-h-screen;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8fafc 100%);
}

/* Header Styles */
.page-header {
  @apply relative py-12;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  overflow: hidden;
}

.page-header::before {
  content: '';
  @apply absolute inset-0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  @apply flex items-center justify-between relative z-10;
}

.header-text {
  @apply text-white;
}

.page-title {
  @apply text-4xl md:text-5xl font-bold mb-2;
  background: linear-gradient(135deg, #ffffff 0%, #e0f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  @apply text-lg md:text-xl opacity-90;
}

.header-decoration {
  @apply hidden md:block;
}

.floating-icon {
  @apply w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-2xl text-white;
  backdrop-filter: blur(10px);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Main Content */
.main-content {
  @apply py-12;
}

.content-wrapper {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-8;
}

.creator-container {
  @apply lg:col-span-2;
}

/* Tips Section */
.tips-section {
  @apply lg:col-span-1;
}

.tips-card {
  @apply bg-white rounded-2xl shadow-xl p-6;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(0, 212, 255, 0.1);
}

.tips-header {
  @apply flex items-center space-x-3 mb-6;
}

.tips-header i {
  @apply text-2xl text-yellow-500;
}

.tips-header h3 {
  @apply text-xl font-bold text-gray-800;
}

.tips-list {
  @apply space-y-4;
}

.tip-item {
  @apply flex items-start space-x-3 p-3 rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.tip-item:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.1);
}

.tip-item i {
  @apply text-lg mt-1;
}

.tip-item div {
  @apply flex-1;
}

.tip-item strong {
  @apply block text-sm font-semibold text-gray-800 mb-1;
}

.tip-item span {
  @apply text-sm text-gray-600;
}

/* Success Modal */
.success-modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
  backdrop-filter: blur(5px);
}

.success-modal {
  @apply bg-white rounded-2xl p-8 max-w-md mx-4 text-center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-icon {
  @apply text-6xl text-green-500 mb-4;
}

.success-title {
  @apply text-2xl font-bold text-gray-800 mb-2;
}

.success-text {
  @apply text-gray-600 mb-6;
}

.success-actions {
  @apply flex space-x-3;
}

.success-btn {
  @apply flex-1 px-4 py-3 rounded-xl font-semibold transition-all duration-300;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
}

.success-btn:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-2px);
}

.success-btn.primary {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: white;
}

.success-btn.primary:hover {
  background: linear-gradient(135deg, #00b8e6 0%, #0088bb 100%);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .content-wrapper {
    @apply grid-cols-1;
  }
  
  .tips-section {
    @apply order-first;
  }
  
  .tips-card {
    @apply p-4;
  }
  
  .tips-list {
    @apply grid grid-cols-1 md:grid-cols-2 gap-3;
  }
}

@media (max-width: 768px) {
  .page-header {
    @apply py-8;
  }
  
  .page-title {
    @apply text-3xl;
  }
  
  .page-subtitle {
    @apply text-base;
  }
  
  .main-content {
    @apply py-6;
  }
  
  .tips-list {
    @apply grid-cols-1;
  }
  
  .success-modal {
    @apply p-6;
  }
  
  .success-actions {
    @apply flex-col space-x-0 space-y-3;
  }
}
</style>
