<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// Check if user is admin
if (!authStore.user?.isAdmin) {
  router.push('/');
}

// State
const activeTab = ref('overview');
const loading = ref(false);
const error = ref(null);

// Dashboard data
const stats = ref({
  totalUsers: 0,
  totalPosts: 0,
  totalReports: 0,
  activeUsers: 0,
  premiumUsers: 0,
  totalRevenue: 0
});

const recentReports = ref([]);
const recentUsers = ref([]);
const systemHealth = ref({
  database: 'healthy',
  storage: 'healthy',
  api: 'healthy',
  cdn: 'healthy'
});

// Computed
const totalRevenueFormatted = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(stats.value.totalRevenue);
});

// Fetch dashboard data
const fetchDashboardData = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await fetch('/.netlify/functions/admin/dashboard', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch dashboard data');
    }

    stats.value = data.data.stats;
    recentReports.value = data.data.recentReports || [];
    recentUsers.value = data.data.recentUsers || [];
    systemHealth.value = data.data.systemHealth || systemHealth.value;

  } catch (err) {
    error.value = err.message;
    console.error('Error fetching dashboard data:', err);
  } finally {
    loading.value = false;
  }
};

// Handle report action
const handleReport = async (reportId, action) => {
  try {
    const response = await fetch(`/.netlify/functions/admin/reports/${reportId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ action })
    });

    if (response.ok) {
      // Refresh reports
      fetchDashboardData();
    }
  } catch (err) {
    console.error('Error handling report:', err);
  }
};

// Format date
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Get health status color
const getHealthColor = (status) => {
  switch (status) {
    case 'healthy': return 'text-green-400';
    case 'warning': return 'text-yellow-400';
    case 'error': return 'text-red-400';
    default: return 'text-gray-400';
  }
};

onMounted(() => {
  fetchDashboardData();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-7xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Admin Dashboard</h1>
        </div>
        
        <div class="flex items-center gap-4">
          <span class="text-sm text-gray-400">Welcome, {{ authStore.user?.username }}</span>
          <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
            A
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 pt-20 pb-16">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-neon-blue"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500 rounded-xl p-6 text-center">
        <p class="text-red-400">{{ error }}</p>
        <button @click="fetchDashboardData" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
          Try Again
        </button>
      </div>

      <!-- Dashboard Content -->
      <div v-else class="space-y-6">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">Total Users</p>
                <p class="text-2xl font-bold">{{ stats.totalUsers.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">Total Posts</p>
                <p class="text-2xl font-bold">{{ stats.totalPosts.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">Active Users</p>
                <p class="text-2xl font-bold">{{ stats.activeUsers.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">NTA+ Users</p>
                <p class="text-2xl font-bold">{{ stats.premiumUsers.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">Reports</p>
                <p class="text-2xl font-bold">{{ stats.totalReports.toLocaleString() }}</p>
              </div>
              <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
          </div>

          <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-gray-400">Revenue</p>
                <p class="text-2xl font-bold">{{ totalRevenueFormatted }}</p>
              </div>
              <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- System Health -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">System Health</h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="flex items-center gap-3">
              <div :class="['w-3 h-3 rounded-full', systemHealth.database === 'healthy' ? 'bg-green-400' : 'bg-red-400']"></div>
              <span class="text-sm">Database</span>
              <span :class="['text-sm font-medium', getHealthColor(systemHealth.database)]">{{ systemHealth.database }}</span>
            </div>
            <div class="flex items-center gap-3">
              <div :class="['w-3 h-3 rounded-full', systemHealth.storage === 'healthy' ? 'bg-green-400' : 'bg-red-400']"></div>
              <span class="text-sm">Storage</span>
              <span :class="['text-sm font-medium', getHealthColor(systemHealth.storage)]">{{ systemHealth.storage }}</span>
            </div>
            <div class="flex items-center gap-3">
              <div :class="['w-3 h-3 rounded-full', systemHealth.api === 'healthy' ? 'bg-green-400' : 'bg-red-400']"></div>
              <span class="text-sm">API</span>
              <span :class="['text-sm font-medium', getHealthColor(systemHealth.api)]">{{ systemHealth.api }}</span>
            </div>
            <div class="flex items-center gap-3">
              <div :class="['w-3 h-3 rounded-full', systemHealth.cdn === 'healthy' ? 'bg-green-400' : 'bg-red-400']"></div>
              <span class="text-sm">CDN</span>
              <span :class="['text-sm font-medium', getHealthColor(systemHealth.cdn)]">{{ systemHealth.cdn }}</span>
            </div>
          </div>
        </div>

        <!-- Recent Reports -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Recent Reports</h2>
          
          <div v-if="recentReports.length === 0" class="text-center py-8 text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p>No recent reports</p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="report in recentReports"
              :key="report._id"
              class="border border-gray-700 rounded-lg p-4"
            >
              <div class="flex justify-between items-start mb-3">
                <div>
                  <div class="flex items-center gap-2 mb-1">
                    <span class="font-medium">{{ report.reason }}</span>
                    <span class="px-2 py-1 bg-red-500/20 text-red-400 text-xs rounded">{{ report.targetType }}</span>
                  </div>
                  <p class="text-sm text-gray-400">
                    Reported by @{{ report.reportedBy?.username }} • {{ formatDate(report.createdAt) }}
                  </p>
                </div>
                <div class="flex gap-2">
                  <button
                    @click="handleReport(report._id, 'approve')"
                    class="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                  >
                    Approve
                  </button>
                  <button
                    @click="handleReport(report._id, 'dismiss')"
                    class="px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
              
              <p v-if="report.additionalInfo" class="text-sm text-gray-300 bg-dark-bg p-3 rounded">
                {{ report.additionalInfo }}
              </p>
            </div>
          </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Recent Users</h2>
          
          <div v-if="recentUsers.length === 0" class="text-center py-8 text-gray-400">
            <p>No recent users</p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="user in recentUsers"
              :key="user._id"
              class="border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-colors"
            >
              <div class="flex items-center gap-3 mb-3">
                <img
                  :src="user.profilePicture || 'https://via.placeholder.com/40'"
                  :alt="user.username"
                  class="w-10 h-10 rounded-full object-cover"
                />
                <div>
                  <div class="font-medium flex items-center gap-1">
                    {{ user.username }}
                    <span v-if="user.isPremium" class="text-xs text-neon-blue">NTA+</span>
                  </div>
                  <div class="text-sm text-gray-400">{{ formatDate(user.createdAt) }}</div>
                </div>
              </div>
              
              <div class="text-sm text-gray-400">
                <div>{{ user.postsCount || 0 }} posts</div>
                <div>{{ user.followersCount || 0 }} followers</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
