const fs = require('fs');
const { createCanvas } = require('canvas');

// Icon sizes needed for PWA
const iconSizes = [
  { size: 48, filename: 'icon-48.png' },
  { size: 72, filename: 'icon-72.png' },
  { size: 96, filename: 'icon-96.png' },
  { size: 144, filename: 'icon-144.png' },
  { size: 192, filename: 'icon-192.png' },
  { size: 512, filename: 'icon-512.png' },
  { size: 192, filename: 'icon-192-maskable.png', maskable: true },
  { size: 512, filename: 'icon-512-maskable.png', maskable: true }
];

function createIcon(size, filename, maskable = false) {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');

  if (maskable) {
    // Maskable icons need safe area (80% of icon)
    ctx.fillStyle = '#00d4ff';
    ctx.fillRect(0, 0, size, size);
    
    const safeSize = size * 0.8;
    const offset = (size - safeSize) / 2;
    ctx.fillStyle = '#0a0a0a';
    ctx.fillRect(offset, offset, safeSize, safeSize);
    
    // Logo in safe area
    ctx.fillStyle = '#00d4ff';
    ctx.font = `bold ${safeSize * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('NT', size / 2, size / 2);
  } else {
    // Regular icons
    ctx.fillStyle = '#00d4ff';
    ctx.fillRect(0, 0, size, size);
    
    // Logo
    ctx.fillStyle = '#0a0a0a';
    ctx.font = `bold ${size * 0.4}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('NT', size / 2, size / 2);
  }

  // Save to public folder
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(`public/${filename}`, buffer);
  console.log(`Created: public/${filename}`);
}

// Create all icons
iconSizes.forEach(icon => {
  createIcon(icon.size, icon.filename, icon.maskable);
});

// Create screenshots
function createScreenshot(width, height, filename, type) {
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Background
  ctx.fillStyle = '#0a0a0a';
  ctx.fillRect(0, 0, width, height);

  // Header
  ctx.fillStyle = '#1a1a1a';
  ctx.fillRect(0, 0, width, 60);

  // Logo
  ctx.fillStyle = '#00d4ff';
  ctx.font = 'bold 24px Arial';
  ctx.textAlign = 'left';
  ctx.textBaseline = 'middle';
  ctx.fillText('NeTuArk', 20, 30);

  // Subtitle
  ctx.fillStyle = '#ccc';
  ctx.font = '14px Arial';
  ctx.fillText('The World Is Opening', 120, 30);

  // Content area
  ctx.fillStyle = '#1a1a1a';
  ctx.fillRect(20, 80, width - 40, height - 120);

  // Sample content
  ctx.fillStyle = '#00d4ff';
  ctx.font = 'bold 18px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('Welcome to NeTuArk', width / 2, height / 2 - 40);

  ctx.fillStyle = '#ccc';
  ctx.font = '14px Arial';
  ctx.fillText('A modern social platform where the world is opening', width / 2, height / 2);

  if (type === 'mobile') {
    // Mobile bottom navigation
    ctx.fillStyle = '#333';
    ctx.fillRect(0, height - 60, width, 60);
    
    const navItems = ['Home', 'Discover', 'Messages', 'Profile'];
    const itemWidth = width / navItems.length;
    
    navItems.forEach((item, index) => {
      ctx.fillStyle = index === 0 ? '#00d4ff' : '#ccc';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(item, (index + 0.5) * itemWidth, height - 20);
    });
  }

  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(`public/${filename}`, buffer);
  console.log(`Created: public/${filename}`);
}

// Create screenshots
createScreenshot(1280, 720, 'screenshot-desktop.png', 'desktop');
createScreenshot(750, 1334, 'screenshot-mobile.png', 'mobile');

console.log('All PWA icons and screenshots created successfully!');
