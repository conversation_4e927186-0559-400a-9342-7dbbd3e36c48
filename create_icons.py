#!/usr/bin/env python3
"""
Simple icon generator for NeTuArk PWA
Creates all required icon sizes for the manifest.json
"""

from PIL import Image, ImageDraw, ImageFont
import os

# Icon sizes needed for PWA
ICON_SIZES = [
    (48, 'icon-48.png'),
    (72, 'icon-72.png'),
    (96, 'icon-96.png'),
    (144, 'icon-144.png'),
    (192, 'icon-192.png'),
    (512, 'icon-512.png'),
    (192, 'icon-192-maskable.png', True),
    (512, 'icon-512-maskable.png', True)
]

# Colors
BG_COLOR = '#00d4ff'  # NeTuArk blue
TEXT_COLOR = '#0a0a0a'  # Dark text

def create_icon(size, filename, maskable=False):
    """Create a single icon with the specified size"""
    
    # Create image
    img = Image.new('RGB', (size, size), BG_COLOR)
    draw = ImageDraw.Draw(img)
    
    if maskable:
        # Maskable icons need safe area (80% of icon)
        safe_size = int(size * 0.8)
        offset = (size - safe_size) // 2
        
        # Draw safe area background
        draw.rectangle([offset, offset, offset + safe_size, offset + safe_size], fill=TEXT_COLOR)
        
        # Calculate text size for safe area
        text_size = int(safe_size * 0.4)
        text_x = size // 2
        text_y = size // 2
    else:
        # Regular icons use full area
        text_size = int(size * 0.4)
        text_x = size // 2
        text_y = size // 2
    
    # Try to use a font, fallback to default if not available
    try:
        font = ImageFont.truetype("arial.ttf", text_size)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", text_size)
        except:
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", text_size)
            except:
                font = ImageFont.load_default()
    
    # Draw text
    text = "NT"
    if maskable:
        text_color = BG_COLOR
    else:
        text_color = TEXT_COLOR
    
    # Get text bounding box for centering
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Center the text
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Save the image
    output_path = os.path.join('public', filename)
    img.save(output_path, 'PNG')
    print(f"Created: {output_path}")

def create_screenshots():
    """Create placeholder screenshots for the manifest"""
    
    # Desktop screenshot
    desktop_img = Image.new('RGB', (1280, 720), '#0a0a0a')
    desktop_draw = ImageDraw.Draw(desktop_img)
    
    # Header
    desktop_draw.rectangle([0, 0, 1280, 60], fill='#1a1a1a')
    
    # Try to get a font for text
    try:
        title_font = ImageFont.truetype("arial.ttf", 24)
        subtitle_font = ImageFont.truetype("arial.ttf", 14)
        content_font = ImageFont.truetype("arial.ttf", 18)
    except:
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        content_font = ImageFont.load_default()
    
    # Logo and title
    desktop_draw.text((20, 18), "NeTuArk", fill=BG_COLOR, font=title_font)
    desktop_draw.text((120, 23), "The World Is Opening", fill='#ccc', font=subtitle_font)
    
    # Content area
    desktop_draw.rectangle([20, 80, 1260, 680], fill='#1a1a1a')
    desktop_draw.text((640, 320), "Welcome to NeTuArk", fill=BG_COLOR, font=content_font, anchor="mm")
    desktop_draw.text((640, 360), "A modern social platform where the world is opening", fill='#ccc', font=subtitle_font, anchor="mm")
    
    desktop_img.save('public/screenshot-desktop.png', 'PNG')
    print("Created: public/screenshot-desktop.png")
    
    # Mobile screenshot
    mobile_img = Image.new('RGB', (750, 1334), '#0a0a0a')
    mobile_draw = ImageDraw.Draw(mobile_img)
    
    # Header
    mobile_draw.rectangle([0, 0, 750, 60], fill='#1a1a1a')
    mobile_draw.text((20, 18), "NeTuArk", fill=BG_COLOR, font=title_font)
    mobile_draw.text((120, 23), "The World Is Opening", fill='#ccc', font=subtitle_font)
    
    # Content area
    mobile_draw.rectangle([20, 80, 730, 1274], fill='#1a1a1a')
    mobile_draw.text((375, 600), "Welcome to NeTuArk", fill=BG_COLOR, font=content_font, anchor="mm")
    mobile_draw.text((375, 640), "A modern social platform", fill='#ccc', font=subtitle_font, anchor="mm")
    mobile_draw.text((375, 660), "where the world is opening", fill='#ccc', font=subtitle_font, anchor="mm")
    
    # Bottom navigation
    mobile_draw.rectangle([0, 1274, 750, 1334], fill='#333')
    nav_items = ['Home', 'Discover', 'Messages', 'Profile']
    item_width = 750 // 4
    
    for i, item in enumerate(nav_items):
        x = (i + 0.5) * item_width
        color = BG_COLOR if i == 0 else '#ccc'
        mobile_draw.text((x, 1304), item, fill=color, font=subtitle_font, anchor="mm")
    
    mobile_img.save('public/screenshot-mobile.png', 'PNG')
    print("Created: public/screenshot-mobile.png")

def main():
    """Main function to create all icons and screenshots"""
    
    # Create public directory if it doesn't exist
    os.makedirs('public', exist_ok=True)
    
    print("Creating NeTuArk PWA icons...")
    
    # Create all icons
    for icon_data in ICON_SIZES:
        if len(icon_data) == 3:
            size, filename, maskable = icon_data
            create_icon(size, filename, maskable)
        else:
            size, filename = icon_data
            create_icon(size, filename, False)
    
    # Create screenshots
    print("\nCreating screenshots...")
    create_screenshots()
    
    print("\n✅ All PWA icons and screenshots created successfully!")
    print("\nNext steps:")
    print("1. Copy all generated files to your public/ directory")
    print("2. Deploy to Netlify")
    print("3. Test PWA installation on different devices")

if __name__ == "__main__":
    main()
