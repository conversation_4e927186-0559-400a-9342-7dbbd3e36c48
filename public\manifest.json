{"name": "NeTuArk - The World Is Opening", "short_name": "NeTuArk", "description": "A modern social platform where the world is opening", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#00d4ff", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["social", "entertainment", "lifestyle"], "icons": [{"src": "/icon-48.png", "sizes": "48x48", "type": "image/png", "purpose": "any"}, {"src": "/icon-72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icon-96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icon-144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "shortcuts": [{"name": "Feed", "short_name": "Feed", "description": "View your social feed", "url": "/feed", "icons": [{"src": "/icon-96.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Create Post", "short_name": "Post", "description": "Create a new post", "url": "/create-post", "icons": [{"src": "/icon-96.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Messages", "short_name": "Messages", "description": "View your messages", "url": "/messages", "icons": [{"src": "/icon-96.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Stories", "short_name": "Stories", "description": "View stories", "url": "/stories", "icons": [{"src": "/icon-96.png", "sizes": "96x96", "type": "image/png"}]}], "screenshots": [{"src": "/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "NeTuArk Social Platform Desktop"}, {"src": "/screenshot-mobile.png", "sizes": "750x1334", "type": "image/png", "platform": "narrow", "label": "NeTuArk Mobile View"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "protocol_handlers": [{"protocol": "web+netuark", "url": "/share?url=%s"}], "share_target": {"action": "/share", "method": "GET", "params": {"title": "title", "text": "text", "url": "url"}}}