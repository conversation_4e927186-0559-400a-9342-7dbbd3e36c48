<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute, RouterLink } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// Form data
const username = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const agreeTerms = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// Initialize form
onMounted(() => {
  // Set focus to the first field
  const emailInput = document.getElementById('email');
  if (emailInput) {
    emailInput.focus();
  }
});

// Computed properties
const isFormValid = computed(() => {
  return username.value &&
         email.value &&
         password.value &&
         confirmPassword.value &&
         password.value === confirmPassword.value &&
         agreeTerms.value;
});

const passwordsMatch = computed(() => {
  if (!confirmPassword.value) return true;
  return password.value === confirmPassword.value;
});

// Handle registration
const handleRegister = async () => {
  if (!isFormValid.value || isSubmitting.value) return;

  errorMessage.value = '';
  successMessage.value = '';
  isSubmitting.value = true;

  try {
    console.log('Registering user with:', {
      username: username.value,
      email: email.value,
      password: '********' // Don't log actual password
    });

    // Call the real backend API
    const result = await authStore.register({
      username: username.value,
      email: email.value,
      password: password.value
    });

    console.log('Registration result:', result);

    // Check if registration is being processed in the background
    if (result.status === 'processing') {
      successMessage.value = 'Your account is being created. Please try logging in after a few moments.';

      // Redirect to login page after a delay
      setTimeout(() => {
        router.push('/login');
      }, 3000);
      return;
    }

    // Normal registration flow
    successMessage.value = 'Registration successful! Redirecting to feed...';

    // Redirect to feed after successful registration with a short delay
    setTimeout(() => {
      router.push('/feed');
    }, 1500);
  } catch (error) {
    console.error('Registration error:', error);
    if (error && typeof error.message === 'string' && error.message.trim() !== '') {
      errorMessage.value = error.message;
    } else if (typeof error === 'string' && error.trim() !== '') {
      errorMessage.value = error;
    } else {
      errorMessage.value = 'Registration failed. Please try again.';
    }
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="min-h-screen flex flex-col justify-center items-center px-4 py-12 bg-dark-bg">
    <div class="w-full max-w-md">
      <!-- Logo -->
      <div class="flex justify-center mb-8">
        <img src="/logo2.png" alt="NeTuArk Logo" class="h-12" />
      </div>

      <!-- Register Form -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 shadow-lg p-8">
        <h2 class="text-2xl font-bold mb-6 text-center">Create Your Account</h2>

        <!-- Error message -->
        <div v-if="errorMessage" class="mb-6 p-3 bg-red-900/30 border border-red-800 rounded-md text-red-200 text-sm">
          {{ errorMessage }}
        </div>

        <!-- Success message -->
        <div v-if="successMessage" class="mb-6 p-3 bg-green-900/30 border border-green-800 rounded-md text-green-200 text-sm">
          {{ successMessage }}
        </div>

        <form @submit.prevent="handleRegister" class="space-y-6">
          <!-- Username -->
          <div>
            <label for="username" class="block text-sm font-medium text-gray-300 mb-1">Username</label>
            <input
              id="username"
              v-model="username"
              type="text"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="Choose a username"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Password -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Password</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="••••••••"
            />
          </div>

          <!-- Confirm Password -->
          <div>
            <label for="confirm-password" class="block text-sm font-medium text-gray-300 mb-1">Confirm Password</label>
            <input
              id="confirm-password"
              v-model="confirmPassword"
              type="password"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              :class="{ 'border-red-500 focus:border-red-500 focus:ring-red-500': !passwordsMatch }"
              placeholder="••••••••"
            />
            <p v-if="!passwordsMatch" class="mt-1 text-sm text-red-400">Passwords do not match</p>
          </div>

          <!-- Terms and conditions -->
          <div class="flex items-center">
            <input
              id="agree-terms"
              v-model="agreeTerms"
              type="checkbox"
              required
              class="h-4 w-4 rounded border-gray-700 bg-dark-bg text-neon-blue focus:ring-neon-blue"
            />
            <label for="agree-terms" class="ml-2 block text-sm text-gray-300">
              I agree to the <router-link to="/terms-of-service" class="text-neon-blue hover:text-neon-blue/80">Terms of Service</router-link> and <router-link to="/privacy-policy" class="text-neon-blue hover:text-neon-blue/80">Privacy Policy</router-link>
            </label>
          </div>

          <!-- Submit button -->
          <div>
            <button
              type="submit"
              :disabled="!isFormValid || isSubmitting"
              class="w-full neon-button py-3 px-4 flex justify-center rounded-full transition-all duration-200 transform hover:scale-105"
              :class="{ 'opacity-70 cursor-not-allowed': !isFormValid || isSubmitting }"
            >
              <span v-if="isSubmitting">Creating account...</span>
              <span v-else>Create Account</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Login link -->
      <div class="mt-6 text-center">
        <p class="text-gray-400">
          Already have an account?
        </p>
        <router-link
          to="/login"
          class="mt-3 inline-block px-6 py-2 rounded-full bg-gray-700 hover:bg-gray-600 text-white font-medium transition-all duration-200"
        >
          Log In
        </router-link>
      </div>
    </div>
  </div>
</template>
