<script setup>
import { ref, computed } from 'vue';
import { useAuthStore } from '../../stores/auth';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  targetType: {
    type: String,
    required: true,
    validator: (value) => ['post', 'user', 'comment', 'story'].includes(value)
  },
  targetId: {
    type: String,
    required: true
  },
  targetInfo: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['close', 'reported']);

const authStore = useAuthStore();

// State
const selectedReason = ref('');
const customReason = ref('');
const additionalInfo = ref('');
const submitting = ref(false);
const error = ref(null);
const success = ref(false);

// Report reasons
const reportReasons = {
  post: [
    { value: 'spam', label: 'Spam or misleading content' },
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'violence', label: 'Violence or threats' },
    { value: 'nudity', label: 'Nudity or sexual content' },
    { value: 'copyright', label: 'Copyright infringement' },
    { value: 'misinformation', label: 'False or misleading information' },
    { value: 'other', label: 'Other (please specify)' }
  ],
  user: [
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'impersonation', label: 'Impersonation or fake account' },
    { value: 'spam', label: 'Spam or fake engagement' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'inappropriate_content', label: 'Inappropriate content' },
    { value: 'underage', label: 'Underage user' },
    { value: 'other', label: 'Other (please specify)' }
  ],
  comment: [
    { value: 'spam', label: 'Spam or irrelevant' },
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'inappropriate', label: 'Inappropriate content' },
    { value: 'other', label: 'Other (please specify)' }
  ],
  story: [
    { value: 'inappropriate', label: 'Inappropriate content' },
    { value: 'harassment', label: 'Harassment or bullying' },
    { value: 'hate_speech', label: 'Hate speech or discrimination' },
    { value: 'spam', label: 'Spam or misleading' },
    { value: 'other', label: 'Other (please specify)' }
  ]
};

// Computed
const currentReasons = computed(() => reportReasons[props.targetType] || []);
const isFormValid = computed(() => {
  if (!selectedReason.value) return false;
  if (selectedReason.value === 'other' && !customReason.value.trim()) return false;
  return true;
});

// Submit report
const submitReport = async () => {
  if (!isFormValid.value) return;

  try {
    submitting.value = true;
    error.value = null;

    const reportData = {
      targetType: props.targetType,
      targetId: props.targetId,
      reason: selectedReason.value === 'other' ? customReason.value : selectedReason.value,
      additionalInfo: additionalInfo.value.trim(),
      targetInfo: props.targetInfo
    };

    const response = await fetch('/.netlify/functions/reports', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify(reportData)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to submit report');
    }

    success.value = true;
    emit('reported', data.data.report);

    // Auto-close after success
    setTimeout(() => {
      closeModal();
    }, 2000);

  } catch (err) {
    error.value = err.message;
    console.error('Error submitting report:', err);
  } finally {
    submitting.value = false;
  }
};

// Close modal
const closeModal = () => {
  // Reset form
  selectedReason.value = '';
  customReason.value = '';
  additionalInfo.value = '';
  error.value = null;
  success.value = false;
  submitting.value = false;
  
  emit('close');
};

// Get target display name
const getTargetDisplayName = () => {
  switch (props.targetType) {
    case 'post':
      return 'post';
    case 'user':
      return `user @${props.targetInfo.username || 'unknown'}`;
    case 'comment':
      return 'comment';
    case 'story':
      return 'story';
    default:
      return 'content';
  }
};
</script>

<template>
  <div
    v-if="show"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="closeModal"
  >
    <div
      class="bg-darker-bg rounded-xl border border-gray-800 w-full max-w-md max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- Header -->
      <div class="p-6 border-b border-gray-800 flex justify-between items-center">
        <h2 class="text-xl font-semibold text-red-400">Report {{ getTargetDisplayName() }}</h2>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 overflow-y-auto max-h-[60vh]">
        <!-- Success State -->
        <div v-if="success" class="text-center py-8">
          <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 class="text-lg font-semibold text-green-400 mb-2">Report Submitted</h3>
          <p class="text-gray-400">Thank you for helping keep NeTuArk safe. We'll review your report shortly.</p>
        </div>

        <!-- Report Form -->
        <div v-else class="space-y-6">
          <!-- Warning -->
          <div class="bg-yellow-900/20 border border-yellow-500 rounded-lg p-4">
            <div class="flex items-start gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
              <div>
                <p class="text-sm text-yellow-300 font-medium">Important</p>
                <p class="text-sm text-yellow-200 mt-1">
                  False reports may result in action against your account. Please only report content that violates our community guidelines.
                </p>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-900/20 border border-red-500 rounded-lg p-4">
            <p class="text-red-400">{{ error }}</p>
          </div>

          <!-- Reason Selection -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-3">
              Why are you reporting this {{ props.targetType }}?
            </label>
            <div class="space-y-2">
              <label
                v-for="reason in currentReasons"
                :key="reason.value"
                class="flex items-center p-3 border border-gray-700 rounded-lg hover:border-gray-600 cursor-pointer transition-colors"
                :class="{ 'border-neon-blue bg-neon-blue/10': selectedReason === reason.value }"
              >
                <input
                  v-model="selectedReason"
                  :value="reason.value"
                  type="radio"
                  class="sr-only"
                />
                <div class="flex items-center">
                  <div
                    class="w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center"
                    :class="selectedReason === reason.value ? 'border-neon-blue' : 'border-gray-500'"
                  >
                    <div
                      v-if="selectedReason === reason.value"
                      class="w-2 h-2 rounded-full bg-neon-blue"
                    ></div>
                  </div>
                  <span class="text-sm">{{ reason.label }}</span>
                </div>
              </label>
            </div>
          </div>

          <!-- Custom Reason (if "Other" is selected) -->
          <div v-if="selectedReason === 'other'">
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Please specify the reason
            </label>
            <input
              v-model="customReason"
              type="text"
              placeholder="Enter your reason..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-neon-blue focus:outline-none"
              maxlength="100"
            />
          </div>

          <!-- Additional Information -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Additional information (optional)
            </label>
            <textarea
              v-model="additionalInfo"
              placeholder="Provide any additional context that might help our review..."
              class="w-full h-24 bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-neon-blue focus:outline-none resize-none"
              maxlength="500"
            ></textarea>
            <p class="text-xs text-gray-500 mt-1">{{ additionalInfo.length }}/500</p>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div v-if="!success" class="p-6 border-t border-gray-800 flex justify-between items-center">
        <button
          @click="closeModal"
          class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Cancel
        </button>
        
        <button
          @click="submitReport"
          :disabled="!isFormValid || submitting"
          class="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <div v-if="submitting" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          {{ submitting ? 'Submitting...' : 'Submit Report' }}
        </button>
      </div>
    </div>
  </div>
</template>
