<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient144" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow144" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="72" cy="72" r="68" fill="url(#bgGradient144)" filter="url(#shadow144)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(72, 72)">
    <!-- Central N -->
    <text x="0" y="14.4" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="57.6" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-28.8" y1="-14.4" x2="28.8" y2="14.4" 
          stroke="#0a0a0a" stroke-width="2.88" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-21.599999999999998" cy="21.599999999999998" r="2.88" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="21.599999999999998" cy="21.599999999999998" r="2.88" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="28.8" r="2.16" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>