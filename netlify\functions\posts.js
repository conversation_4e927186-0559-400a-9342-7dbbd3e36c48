const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { uploadFile } = require('./utils/storage');
const { RecommendationEngine } = require('./utils/recommendation-engine');
const { InteractionTracker } = require('./utils/interaction-tracker');

// Initialize recommendation engine and interaction tracker
const recommendationEngine = new RecommendationEngine();
const interactionTracker = new InteractionTracker();

// Create a new post with proper validation and error handling
exports.createPost = handleAsync(requireAuth(async (event) => {
  try {
    // Get user ID from token
    const userId = event.user._id;

    if (!userId) {
      return error('Invalid user ID in token', 401);
    }

    // Parse request body with error handling
    let body;
    try {
      body = JSON.parse(event.body);
    } catch (e) {
      return error('Invalid request body format', 400);
    }

    const { content, media, visibility } = body;

    // Validate input with detailed error messages
    if (!content && (!media || media.length === 0)) {
      return error('Post must contain content or media', 400);
    }

    // Validate content length
    if (content && content.length > 5000) {
      return error('Content exceeds maximum length of 5000 characters', 400);
    }

    // Validate media count
    if (media && media.length > 10) {
      return error('Maximum of 10 media items allowed per post', 400);
    }

    // Validate visibility
    const allowedVisibilities = ['public', 'followers', 'private'];
    if (visibility && !allowedVisibilities.includes(visibility)) {
      return error('Invalid visibility option. Must be public, followers, or private', 400);
    }

    // Get user to check if they're active
    const user = await findOne('users', { _id: new ObjectId(userId) });

    if (!user) {
      return error('User not found', 404);
    }

    if (user.isActive === false) {
      return error('Account is disabled. Please contact support.', 403);
    }

    // Process media files if any
    let mediaUrls = [];

    if (media && media.length > 0) {
      // Validate media array length
      if (media.length > 10) {
        return error('Maximum 10 media files allowed per post', 400);
      }

      for (const item of media) {
        // Validate required fields
        if (!item.data || !item.type) {
          return error('Invalid media data: missing data or type field', 400);
        }

        // Validate media type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/webm'];
        if (!allowedTypes.includes(item.type)) {
          return error(`Unsupported media type: ${item.type}. Allowed types: JPEG, PNG, GIF, WebP, MP4, QuickTime, WebM`, 400);
        }

        // Validate base64 data format
        if (!item.data.startsWith('data:')) {
          return error('Invalid media data format: must be base64 data URL', 400);
        }

        // Extract base64 data (remove data URL prefix)
        const base64Data = item.data.split(',')[1];
        if (!base64Data) {
          return error('Invalid media data: missing base64 content', 400);
        }

        // Validate media size (10MB limit)
        const sizeInMB = (base64Data.length * 0.75) / 1024 / 1024; // Approximate size of base64 data
        if (sizeInMB > 10) {
          return error(`Media file exceeds 10MB size limit (current: ${sizeInMB.toFixed(2)}MB)`, 400);
        }

        try {
          // Decode base64 image/video (use the extracted base64 data)
          const fileBuffer = Buffer.from(base64Data, 'base64');

          // Generate unique filename with proper sanitization
          const fileExtension = item.type.split('/')[1].replace(/[^a-zA-Z0-9]/g, '');
          const timestamp = Date.now();
          const randomString = Math.random().toString(36).substring(2, 10);
          const fileName = `posts/${userId}_${timestamp}_${randomString}.${fileExtension}`;

          console.log(`Attempting to upload file: ${fileName}, size: ${fileBuffer.length} bytes`);

          // Check if B2 environment variables are configured
          if (!process.env.B2_KEY_ID || !process.env.B2_APP_KEY || !process.env.B2_BUCKET_ID || !process.env.B2_BUCKET_NAME) {
            console.error('B2 configuration missing:', {
              B2_KEY_ID: !!process.env.B2_KEY_ID,
              B2_APP_KEY: !!process.env.B2_APP_KEY,
              B2_BUCKET_ID: !!process.env.B2_BUCKET_ID,
              B2_BUCKET_NAME: !!process.env.B2_BUCKET_NAME
            });
            throw new Error('Storage configuration is incomplete. Please contact support.');
          }

          // Upload to B2 with error handling
          console.log('Starting file upload to B2...');
          const uploadResult = await uploadFile(fileBuffer, fileName, item.type);

          if (!uploadResult || !uploadResult.fileUrl) {
            console.error('Upload result invalid:', uploadResult);
            throw new Error('File upload failed - invalid response from storage service');
          }

          console.log(`File uploaded successfully: ${uploadResult.fileUrl}`);

          // Add to media URLs
          mediaUrls.push({
            url: uploadResult.fileUrl,
            type: item.type.startsWith('image') ? 'image' : 'video',
            width: item.width || null,
            height: item.height || null,
            size: fileBuffer.length,
            fileName: fileName
          });
        } catch (uploadError) {
          console.error('Media upload error details:', {
            error: uploadError.message,
            stack: uploadError.stack,
            fileName: fileName || 'unknown',
            fileSize: fileBuffer?.length || 'unknown'
          });

          // Return more specific error messages
          if (uploadError.message.includes('configuration')) {
            return error('Storage service is not properly configured. Please contact support.', 503);
          } else if (uploadError.message.includes('authorization') || uploadError.message.includes('auth')) {
            return error('Storage service authorization failed. Please try again later.', 503);
          } else if (uploadError.message.includes('network') || uploadError.message.includes('timeout')) {
            return error('Network error during upload. Please check your connection and try again.', 503);
          } else {
            return error(`Failed to upload media: ${uploadError.message}. Please try again.`, 500);
          }
        }
      }
    }

    // Create post object with proper validation
    const post = {
      author: new ObjectId(userId),
      content: content || '',
      media: mediaUrls,
      visibility: visibility || 'public',
      likes: [],
      comments: [],
      viewTime: 0,
      viewCount: 0,
      shareCount: 0,
      aiTags: [], // Will be populated by AI tagging function
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      reportCount: 0
    };

    // Insert post into database with error handling
    let result;
    try {
      result = await insertOne('posts', post);
    } catch (dbError) {
      console.error('Database error during post creation:', dbError);
      return error('Failed to create post. Please try again later.', 500);
    }

    if (!result || !result.insertedId) {
      return error('Failed to create post. Please try again.', 500);
    }

    // Generate AI tags for the post
    try {
      // Call the Kraizer API to tag the post
      const tagResponse = await fetch(`${process.env.URL || 'http://localhost:8888'}/.netlify/functions/kraizer/tag-post`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': event.headers.authorization
        },
        body: JSON.stringify({
          postId: result.insertedId.toString(),
          content: content || '',
          media: mediaUrls
        })
      });

      // Log tagging response for debugging
      console.log('AI tagging response status:', tagResponse.status);

      // Don't fail the post creation if tagging fails
      if (!tagResponse.ok) {
        console.error('AI tagging failed:', await tagResponse.text());
      }
    } catch (tagError) {
      console.error('Error during AI tagging:', tagError);
      // Don't fail the post creation if tagging fails
    }

    // Get the inserted post with author details
    const newPost = await aggregate('posts', [
      { $match: { _id: result.insertedId } },
      { $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorDetails'
        }
      },
      { $unwind: '$authorDetails' },
      { $project: {
          _id: 1,
          content: 1,
          media: 1,
          visibility: 1,
          likes: { $size: '$likes' },
          comments: { $size: '$comments' },
          viewTime: 1,
          viewCount: 1,
          shareCount: 1,
          aiTags: 1,
          createdAt: 1,
          updatedAt: 1,
          author: {
            _id: '$authorDetails._id',
            username: '$authorDetails.username',
            profilePicture: '$authorDetails.profilePicture',
            isPremium: '$authorDetails.isPremium'
          }
        }
      }
    ]);

    if (!newPost || newPost.length === 0) {
      return error('Post created but could not be retrieved', 500);
    }

    // Return the new post
    return success({ post: newPost[0] });
  } catch (err) {
    console.error('Unexpected error during post creation:', err);
    return error('An unexpected error occurred. Please try again later.', 500);
  }
}));

// Get posts for feed with advanced filtering and personalization
exports.getFeed = handleAsync(requireAuth(async (event) => {
  try {
    // Get user ID from token
    const userId = event.user._id;

    if (!userId) {
      return error('Invalid user ID in token', 401);
    }

    // Get pagination and filter parameters with validation
    const page = Math.max(parseInt(event.queryStringParameters?.page) || 1, 1); // Ensure page is at least 1
    const limit = Math.min(Math.max(parseInt(event.queryStringParameters?.limit) || 10, 1), 50); // Between 1 and 50
    const skip = (page - 1) * limit;

    // Get optional filter parameters
    const tagFilter = event.queryStringParameters?.tag;
    const sortBy = event.queryStringParameters?.sortBy || 'hybrid'; // 'recent', 'popular', 'trending', 'hybrid'
    const useML = event.queryStringParameters?.ml !== 'false'; // Enable ML by default

    // Use ML-powered recommendation engine for hybrid sorting
    if (useML && sortBy === 'hybrid') {
      try {
        // Get posts user has already seen (for exclusion)
        const seenPosts = await find('user_interactions',
          {
            userId: new ObjectId(userId),
            type: 'view'
          },
          {
            sort: { createdAt: -1 },
            limit: 500,
            projection: { postId: 1 }
          }
        );

        const excludePostIds = seenPosts.map(interaction => interaction.postId.toString());

        // Generate ML recommendations
        const recommendations = await recommendationEngine.generateRecommendations(userId, {
          limit,
          page,
          excludePostIds,
          includeExploration: true,
          sortBy: 'hybrid'
        });

        // Track feed generation for analytics
        await interactionTracker.trackInteraction(userId, null, 'feed_generation', {
          algorithm: recommendations.metadata.algorithm,
          totalCandidates: recommendations.metadata.totalCandidates,
          sortBy,
          page
        });

        return success({
          posts: recommendations.posts,
          page,
          limit,
          total: recommendations.posts.length,
          hasMore: recommendations.posts.length === limit,
          filter: tagFilter ? { tag: tagFilter } : null,
          sortBy,
          algorithm: 'ml_hybrid',
          metadata: recommendations.metadata
        });

      } catch (mlError) {
        console.error('ML recommendation failed, falling back to traditional algorithm:', mlError);
        // Fall through to traditional algorithm
      }
    }

    // Traditional algorithm fallback
    // Get user to check blacklisted users and following
    let user;
    try {
      user = await findOne('users', { _id: new ObjectId(userId) });

      if (!user) {
        return error('User not found', 404);
      }

      if (user.isActive === false) {
        return error('Account is disabled. Please contact support.', 403);
      }
    } catch (dbError) {
      console.error('Database error fetching user:', dbError);
      return error('Failed to fetch user data. Please try again later.', 500);
    }

    // Convert blacklisted user IDs to ObjectId with error handling
    const blacklistedUserIds = [];
    if (user.blacklistedUsers && Array.isArray(user.blacklistedUsers)) {
      for (const id of user.blacklistedUsers) {
        try {
          blacklistedUserIds.push(new ObjectId(id));
        } catch (e) {
          console.warn(`Invalid ObjectId in blacklistedUsers: ${id}`);
          // Skip invalid IDs
        }
      }
    }

    // Build match criteria
    const matchCriteria = {
      $and: [
        { author: { $nin: blacklistedUserIds } },
        { isDeleted: { $ne: true } }
      ]
    };

    // Add visibility filter
    // Public posts OR posts from users the current user follows
    const followingIds = (user.following || []).map(id => {
      try {
        return new ObjectId(id);
      } catch (e) {
        console.warn(`Invalid ObjectId in following: ${id}`);
        return null;
      }
    }).filter(id => id !== null);

    matchCriteria.$and.push({
      $or: [
        { visibility: 'public' },
        {
          $and: [
            { visibility: 'followers' },
            { author: { $in: followingIds } }
          ]
        }
      ]
    });

    // Add tag filter if provided
    if (tagFilter) {
      matchCriteria.$and.push({ aiTags: tagFilter });
    }

    // Determine sort order based on sortBy parameter
    let sortCriteria = {};
    switch (sortBy) {
      case 'popular':
        sortCriteria = { viewCount: -1, createdAt: -1 };
        break;
      case 'trending':
        // For trending, we'll use a combination of recent activity and popularity
        sortCriteria = {
          // Calculate a trending score based on views, likes, and recency
          trendingScore: -1,
          createdAt: -1
        };
        break;
      case 'recent':
      default:
        sortCriteria = { createdAt: -1 };
    }

    // Get posts with advanced filtering and personalization
    const posts = await aggregate('posts', [
      { $match: matchCriteria },
      // For trending posts, calculate a trending score
      ...(sortBy === 'trending' ? [
        {
          $addFields: {
            // Calculate a trending score based on recency, likes, comments, and views
            // This formula can be adjusted based on business requirements
            trendingScore: {
              $add: [
                // More weight to recent posts (7 day window)
                {
                  $multiply: [
                    {
                      $divide: [
                        1,
                        {
                          $add: [
                            1,
                            {
                              $divide: [
                                { $subtract: [new Date(), '$createdAt'] },
                                86400000 // milliseconds in a day
                              ]
                            }
                          ]
                        }
                      ]
                    },
                    10 // Weight factor
                  ]
                },
                // Add weight for likes
                { $multiply: [{ $size: '$likes' }, 0.5] },
                // Add weight for comments
                { $multiply: [{ $size: '$comments' }, 0.3] },
                // Add weight for views
                { $multiply: ['$viewCount', 0.1] }
              ]
            }
          }
        }
      ] : []),
      { $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorDetails'
        }
      },
      { $unwind: '$authorDetails' },
      // Check if current user has liked each post
      { $addFields: {
          isLiked: { $in: [new ObjectId(userId), '$likes'] }
        }
      },
      { $project: {
          _id: 1,
          content: 1,
          media: 1,
          visibility: 1,
          likes: { $size: '$likes' },
          comments: { $size: '$comments' },
          viewTime: 1,
          viewCount: 1,
          shareCount: 1,
          aiTags: 1,
          createdAt: 1,
          updatedAt: 1,
          isLiked: 1,
          author: {
            _id: '$authorDetails._id',
            username: '$authorDetails.username',
            profilePicture: '$authorDetails.profilePicture',
            isPremium: '$authorDetails.isPremium'
          }
        }
      },
      { $sort: sortCriteria },
      { $skip: skip },
      { $limit: limit }
    ]);

    // Calculate if there are more posts available
    const totalCount = await aggregate('posts', [
      { $match: matchCriteria },
      { $count: 'total' }
    ]);

    const total = totalCount.length > 0 ? totalCount[0].total : 0;
    const hasMore = total > skip + posts.length;

    // Track user activity for feed personalization (in a real system)
    // This would update user preferences based on viewed content

    // Return posts with pagination info
    return success({
      posts,
      page,
      limit,
      total,
      hasMore,
      filter: tagFilter ? { tag: tagFilter } : null,
      sortBy
    });
  } catch (err) {
    console.error('Unexpected error fetching feed:', err);
    return error('An unexpected error occurred. Please try again later.', 500);
  }
}));

// Get post by ID
exports.getPost = handleAsync(async (event) => {
  // Get post ID from path parameter
  const postId = event.queryStringParameters.id;

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  // Get post with author details
  const posts = await aggregate('posts', [
    { $match: { _id: new ObjectId(postId) } },
    { $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'authorDetails'
      }
    },
    { $unwind: '$authorDetails' },
    { $project: {
        _id: 1,
        content: 1,
        media: 1,
        visibility: 1,
        likes: 1,
        comments: 1,
        viewTime: 1,
        aiTags: 1,
        createdAt: 1,
        updatedAt: 1,
        author: {
          _id: '$authorDetails._id',
          username: '$authorDetails.username',
          profilePicture: '$authorDetails.profilePicture',
          isPremium: '$authorDetails.isPremium'
        }
      }
    }
  ]);

  // Check if post exists
  if (posts.length === 0) {
    return error('Post not found', 404);
  }

  // Return post
  return success({ post: posts[0] });
});

// Like a post
exports.likePost = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Add user to likes array if not already liked
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $addToSet: { likes: new ObjectId(userId) } }
  );

  // Track interaction for ML recommendations
  await interactionTracker.trackLike(userId, postId, {
    authorId: post.author.toString(),
    postTags: post.aiTags || [],
    timestamp: new Date()
  });

  // Return success
  return success({ message: 'Post liked successfully' });
}));

// Unlike a post
exports.unlikePost = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Remove user from likes array
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $pull: { likes: new ObjectId(userId) } }
  );

  // Track interaction for ML recommendations
  await interactionTracker.trackUnlike(userId, postId, {
    authorId: post.author.toString(),
    postTags: post.aiTags || [],
    timestamp: new Date()
  });

  // Return success
  return success({ message: 'Post unliked successfully' });
}));

// Track post view
exports.trackView = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Increment view count
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $inc: { viewCount: 1 } }
  );

  // Track interaction for ML recommendations
  await interactionTracker.trackView(userId, postId, null, {
    authorId: post.author.toString(),
    postTags: post.aiTags || [],
    timestamp: new Date()
  });

  // Return success
  return success({ message: 'View tracked successfully' });
}));

// Track post view time
exports.trackViewTime = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId, seconds } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  if (!seconds || typeof seconds !== 'number' || seconds <= 0) {
    return error('Valid view time in seconds is required');
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Increment view time
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $inc: { viewTime: seconds } }
  );

  // Return success
  return success({ message: 'View time tracked successfully' });
}));

// Track post share
exports.trackShare = handleAsync(async (event) => {
  // Parse request body
  const { postId, userId, shareMethod = 'unknown' } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Increment share count
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $inc: { shareCount: 1 } }
  );

  // Track interaction for ML recommendations (if user is provided)
  if (userId) {
    await interactionTracker.trackShare(userId, postId, shareMethod, {
      authorId: post.author.toString(),
      postTags: post.aiTags || [],
      timestamp: new Date()
    });
  }

  // Return success
  return success({ message: 'Share tracked successfully' });
});

// Add a comment to a post
exports.addComment = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId, content, mentions = [] } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required', 400);
  }

  if (!content || content.trim() === '') {
    return error('Comment content is required', 400);
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Check if user is blocked by post author
  const postAuthor = await findOne('users', { _id: post.author });
  if (postAuthor && postAuthor.blacklistedUsers && postAuthor.blacklistedUsers.includes(userId.toString())) {
    return error('You cannot comment on this post', 403);
  }

  // Create comment object
  const comment = {
    _id: new ObjectId(),
    author: new ObjectId(userId),
    content,
    mentions,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false
  };

  // Add comment to post
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $push: { comments: comment } }
  );

  // Track interaction for ML recommendations
  await interactionTracker.trackComment(userId, postId, comment._id.toString(), {
    authorId: post.author.toString(),
    postTags: post.aiTags || [],
    mentions,
    timestamp: new Date()
  });

  // Process @mentions and create notifications
  if (mentions && mentions.length > 0) {
    try {
      // Find mentioned users
      const mentionedUsers = await find('users', { username: { $in: mentions } });

      // Create notifications for each mentioned user
      for (const user of mentionedUsers) {
        // Skip if the mentioned user is the comment author
        if (user._id.toString() === userId.toString()) continue;

        // Skip if the mentioned user has blacklisted the comment author
        if (user.blacklistedUsers && user.blacklistedUsers.includes(userId.toString())) continue;

        // Create notification
        await insertOne('notifications', {
          recipient: user._id,
          type: 'mention',
          sender: new ObjectId(userId),
          postId: new ObjectId(postId),
          commentId: comment._id,
          content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
          isRead: false,
          createdAt: new Date()
        });

        // If the mentioned user is @kraizer or @netuark, trigger AI response
        if (user.username.toLowerCase() === 'kraizer' || user.username.toLowerCase() === 'netuark') {
          try {
            // Call Kraizer API to generate response
            const aiResponse = await fetch(`${process.env.URL || 'http://localhost:8888'}/.netlify/functions/kraizer/ask`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': event.headers.authorization
              },
              body: JSON.stringify({
                query: content,
                context: {
                  postId,
                  commentId: comment._id.toString()
                }
              })
            });

            // If AI response is successful, add it as a reply comment
            if (aiResponse.ok) {
              const aiData = await aiResponse.json();

              // Create AI comment
              const aiComment = {
                _id: new ObjectId(),
                author: user._id, // Use the AI user's ID
                content: aiData.data.response,
                mentions: [],
                createdAt: new Date(),
                updatedAt: new Date(),
                isDeleted: false,
                isAIGenerated: true
              };

              // Add AI comment to post
              await updateOne(
                'posts',
                { _id: new ObjectId(postId) },
                { $push: { comments: aiComment } }
              );
            }
          } catch (aiError) {
            console.error('Error generating AI response:', aiError);
            // Don't fail the comment creation if AI response fails
          }
        }
      }
    } catch (mentionError) {
      console.error('Error processing mentions:', mentionError);
      // Don't fail the comment creation if mention processing fails
    }
  }

  // Get author details for the response
  const author = await findOne('users', { _id: new ObjectId(userId) });

  // Return the comment with author details
  return success({
    comment: {
      ...comment,
      author: {
        _id: author._id,
        username: author.username,
        profilePicture: author.profilePicture,
        isPremium: author.isPremium
      }
    }
  });
}));

// Delete a comment
exports.deleteComment = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { postId, commentId } = JSON.parse(event.body);

  // Validate input
  if (!postId) {
    return error('Post ID is required', 400);
  }

  if (!commentId) {
    return error('Comment ID is required', 400);
  }

  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }

  // Find the comment
  const comment = post.comments && post.comments.find(c => c._id.toString() === commentId);

  if (!comment) {
    return error('Comment not found', 404);
  }

  // Check if user is the comment author or post author
  if (comment.author.toString() !== userId.toString() && post.author.toString() !== userId.toString()) {
    return error('You are not authorized to delete this comment', 403);
  }

  // Mark comment as deleted (soft delete)
  await updateOne(
    'posts',
    {
      _id: new ObjectId(postId),
      'comments._id': new ObjectId(commentId)
    },
    {
      $set: {
        'comments.$.isDeleted': true,
        'comments.$.updatedAt': new Date(),
        'comments.$.content': '[deleted]'
      }
    }
  );

  // Return success
  return success({ message: 'Comment deleted successfully' });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  // Extract the path, handling both direct and nested routes
  const fullPath = event.path;
  const basePath = '/.netlify/functions/posts';

  // Check if the path starts with the base path
  if (!fullPath.startsWith(basePath)) {
    return error('Invalid path', 404);
  }

  // Extract the route path after the base path
  const path = fullPath.substring(basePath.length);

  // Log the request for debugging
  console.log(`Processing request: ${event.httpMethod} ${fullPath} (path: ${path})`);

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/create' && event.httpMethod === 'POST':
      return exports.createPost(event, context);
    case (path === '/feed' || path === '') && event.httpMethod === 'GET':
      return exports.getFeed(event, context);
    case path === '/post' && event.httpMethod === 'GET':
      return exports.getPost(event, context);
    case path === '/like' && event.httpMethod === 'POST':
      return exports.likePost(event, context);
    case path === '/unlike' && event.httpMethod === 'POST':
      return exports.unlikePost(event, context);
    case path === '/view' && event.httpMethod === 'POST':
      return exports.trackView(event, context);
    case path === '/view-time' && event.httpMethod === 'POST':
      return exports.trackViewTime(event, context);
    case path === '/share' && event.httpMethod === 'POST':
      return exports.trackShare(event, context);
    case path === '/comment' && event.httpMethod === 'POST':
      return exports.addComment(event, context);
    case path === '/delete-comment' && event.httpMethod === 'POST':
      return exports.deleteComment(event, context);
    default:
      console.log(`No route matched for: ${path}`);
      return error('Not found', 404);
  }
};
