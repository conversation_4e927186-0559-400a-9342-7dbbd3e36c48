<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient32" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow32" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="12" fill="url(#bgGradient32)" filter="url(#shadow32)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(16, 16)">
    <!-- Central N -->
    <text x="0" y="3.2" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="12.8" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-6.4" y1="-3.2" x2="6.4" y2="3.2" 
          stroke="#0a0a0a" stroke-width="0.64" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-4.8" cy="4.8" r="0.64" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="4.8" cy="4.8" r="0.64" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="6.4" r="0.48" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>