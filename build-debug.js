#!/usr/bin/env node

// Build debug script to help diagnose Netlify build issues
console.log('🔍 NeTuArk Build Debug Information');
console.log('=====================================');

// Environment information
console.log('\n📊 Environment:');
console.log(`Node.js: ${process.version}`);
console.log(`Platform: ${process.platform}`);
console.log(`Architecture: ${process.arch}`);
console.log(`Working Directory: ${process.cwd()}`);

// Check package.json
console.log('\n📦 Package.json Check:');
try {
  const fs = require('fs');
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  console.log(`✅ Package name: ${packageJson.name}`);
  console.log(`✅ Version: ${packageJson.version}`);
  console.log(`✅ Type: ${packageJson.type}`);
  
  if (packageJson.scripts && packageJson.scripts.build) {
    console.log(`✅ Build script: ${packageJson.scripts.build}`);
  } else {
    console.log('❌ No build script found');
  }
  
  console.log(`✅ Dependencies: ${Object.keys(packageJson.dependencies || {}).length}`);
  console.log(`✅ DevDependencies: ${Object.keys(packageJson.devDependencies || {}).length}`);
  
} catch (error) {
  console.log(`❌ Error reading package.json: ${error.message}`);
}

// Check required files
console.log('\n📁 Required Files Check:');
const requiredFiles = [
  'index.html',
  'src/main.js',
  'src/App.vue',
  'vite.config.js',
  'tailwind.config.js',
  'postcss.config.js',
  'src/style.css'
];

const fs = require('fs');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
  }
});

// Check node_modules
console.log('\n📚 Dependencies Check:');
const criticalDeps = ['vue', 'vite', '@vitejs/plugin-vue', 'tailwindcss', 'postcss', 'autoprefixer'];

criticalDeps.forEach(dep => {
  try {
    require.resolve(dep);
    console.log(`✅ ${dep}`);
  } catch (error) {
    console.log(`❌ ${dep} - NOT FOUND`);
  }
});

// Memory and disk space
console.log('\n💾 System Resources:');
const memUsage = process.memoryUsage();
console.log(`Memory Usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);

// Try to run build command
console.log('\n🔨 Build Test:');
console.log('Attempting to run build command...');

const { spawn } = require('child_process');
const buildProcess = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  shell: true
});

buildProcess.on('close', (code) => {
  console.log(`\n🎯 Build process exited with code: ${code}`);
  if (code === 0) {
    console.log('✅ Build successful!');
  } else {
    console.log('❌ Build failed!');
  }
  process.exit(code);
});

buildProcess.on('error', (error) => {
  console.log(`❌ Build process error: ${error.message}`);
  process.exit(1);
});
