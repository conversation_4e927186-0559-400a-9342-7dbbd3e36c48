import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useAIAssistantStore = defineStore('ai-assistant', () => {
  // State
  const responses = ref([]);
  const requestsRemaining = ref(3);
  const resetTime = ref(null);
  const loading = ref(false);
  const error = ref(null);

  // Getters
  const aiResponses = computed(() => responses.value);
  const hasLimitReached = computed(() => {
    const authStore = useAuthStore();
    return !authStore.isPremium && requestsRemaining.value <= 0;
  });
  const remainingRequests = computed(() => {
    const authStore = useAuthStore();
    return authStore.isPremium ? 'unlimited' : requestsRemaining.value;
  });
  const timeUntilReset = computed(() => {
    if (!resetTime.value) return null;

    const now = new Date();
    const reset = new Date(resetTime.value);
    const diff = reset - now;

    if (diff <= 0) return '0 minutes';

    const minutes = Math.floor(diff / 60000);
    return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
  });

  // Actions
  async function askAI(query, context = {}) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/kraizer/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          query,
          context
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get AI response');
      }

      // Add response to history
      responses.value.push({
        query,
        response: data.data.response,
        timestamp: new Date(),
        context
      });

      // Update requests remaining and reset time
      if (data.data.requestsRemaining !== 'unlimited') {
        requestsRemaining.value = data.data.requestsRemaining;
      }

      if (data.data.resetTime) {
        resetTime.value = data.data.resetTime;
      }

      return data.data.response;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function tagContent(postId, content, media = []) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/kraizer/tag-post', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          postId,
          content,
          media
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to tag content');
      }

      return data.data.tags;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function clearResponses() {
    responses.value = [];
  }

  return {
    // State
    responses,
    requestsRemaining,
    resetTime,
    loading,
    error,

    // Getters
    aiResponses,
    hasLimitReached,
    remainingRequests,
    timeUntilReset,

    // Actions
    askAI,
    tagContent,
    clearResponses
  };
});
