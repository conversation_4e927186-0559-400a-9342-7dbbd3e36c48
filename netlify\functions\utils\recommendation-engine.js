const { Matrix } = require('ml-matrix');
const natural = require('natural');
const sentiment = require('sentiment');
const stopword = require('stopword');
const _ = require('lodash');
const { find, aggregate, findOne } = require('./db');
const { ObjectId } = require('mongodb');

/**
 * Advanced Recommendation Engine for NeTuArk
 * Implements multiple ML algorithms for content recommendation
 */
class RecommendationEngine {
  constructor() {
    this.tfidf = new natural.TfIdf();
    this.sentimentAnalyzer = new sentiment();
    this.stemmer = natural.PorterStemmer;

    // Cache for computed matrices and user profiles
    this.userItemMatrix = null;
    this.contentFeatures = new Map();
    this.userProfiles = new Map();
    this.lastUpdate = null;

    // Configuration
    this.config = {
      minInteractions: 5,
      maxRecommendations: 50,
      diversityThreshold: 0.3,
      freshnessWeight: 0.2,
      popularityWeight: 0.15,
      personalWeight: 0.65,
      updateInterval: 300000 // 5 minutes
    };
  }

  /**
   * Generate personalized feed recommendations for a user
   */
  async generateRecommendations(userId, options = {}) {
    try {
      const {
        limit = 20,
        page = 1,
        excludePostIds = [],
        includeExploration = true,
        sortBy = 'hybrid'
      } = options;

      // Get user profile and interaction history
      const userProfile = await this.getUserProfile(userId);
      const userInteractions = await this.getUserInteractions(userId);

      // Get candidate posts
      const candidates = await this.getCandidatePosts(userId, excludePostIds);

      if (candidates.length === 0) {
        return { posts: [], scores: [], metadata: { algorithm: 'fallback' } };
      }

      // Calculate recommendation scores using multiple algorithms
      const scores = await this.calculateHybridScores(
        userId,
        candidates,
        userProfile,
        userInteractions,
        sortBy
      );

      // Apply diversity and freshness filters
      const diversifiedResults = this.applyDiversityFilter(scores, candidates);

      // Sort and paginate
      const sortedResults = diversifiedResults
        .sort((a, b) => b.score - a.score)
        .slice((page - 1) * limit, page * limit);

      // Prepare final recommendations
      const recommendations = sortedResults.map(result => ({
        post: result.post,
        score: result.score,
        reasons: result.reasons
      }));

      return {
        posts: recommendations.map(r => r.post),
        scores: recommendations.map(r => ({
          postId: r.post._id,
          score: r.score,
          reasons: r.reasons
        })),
        metadata: {
          algorithm: 'hybrid',
          totalCandidates: candidates.length,
          diversityApplied: true,
          userProfileStrength: userProfile.strength || 0
        }
      };

    } catch (error) {
      console.error('Error generating recommendations:', error);
      throw error;
    }
  }

  /**
   * Calculate hybrid recommendation scores
   */
  async calculateHybridScores(userId, candidates, userProfile, userInteractions, sortBy) {
    const scores = [];

    for (const post of candidates) {
      let totalScore = 0;
      const reasons = [];

      // 1. Collaborative Filtering Score
      const collaborativeScore = await this.calculateCollaborativeScore(userId, post, userInteractions);
      totalScore += collaborativeScore * this.config.personalWeight;
      if (collaborativeScore > 0.5) reasons.push('Similar users liked this');

      // 2. Content-Based Score
      const contentScore = this.calculateContentScore(post, userProfile);
      totalScore += contentScore * this.config.personalWeight;
      if (contentScore > 0.6) reasons.push('Matches your interests');

      // 3. Social Graph Score
      const socialScore = await this.calculateSocialScore(userId, post);
      totalScore += socialScore * 0.3;
      if (socialScore > 0.4) reasons.push('Popular among connections');

      // 4. Trending Score
      const trendingScore = this.calculateTrendingScore(post);
      totalScore += trendingScore * this.config.popularityWeight;
      if (trendingScore > 0.7) reasons.push('Trending content');

      // 5. Freshness Score
      const freshnessScore = this.calculateFreshnessScore(post);
      totalScore += freshnessScore * this.config.freshnessWeight;

      // 6. Quality Score
      const qualityScore = this.calculateQualityScore(post);
      totalScore *= qualityScore; // Multiplicative to penalize low quality

      // Apply sort-specific adjustments
      if (sortBy === 'recent') {
        totalScore = freshnessScore * 0.7 + totalScore * 0.3;
      } else if (sortBy === 'popular') {
        totalScore = trendingScore * 0.6 + totalScore * 0.4;
      }

      scores.push({
        post,
        score: Math.min(totalScore, 1.0), // Normalize to 0-1
        reasons,
        breakdown: {
          collaborative: collaborativeScore,
          content: contentScore,
          social: socialScore,
          trending: trendingScore,
          freshness: freshnessScore,
          quality: qualityScore
        }
      });
    }

    return scores;
  }

  /**
   * Collaborative filtering using user-item matrix
   */
  async calculateCollaborativeScore(userId, post, userInteractions) {
    try {
      // Find users who interacted with this post
      const postInteractions = await find('user_interactions', {
        postId: new ObjectId(post._id),
        type: { $in: ['like', 'comment', 'share', 'view'] }
      });

      if (postInteractions.length === 0) return 0;

      // Find similar users based on interaction patterns
      const similarUsers = await this.findSimilarUsers(userId, userInteractions);

      let score = 0;
      let totalWeight = 0;

      for (const interaction of postInteractions) {
        const similarUser = similarUsers.find(u => u.userId.toString() === interaction.userId.toString());
        if (similarUser) {
          const weight = similarUser.similarity;
          const interactionWeight = this.getInteractionWeight(interaction.type);
          score += weight * interactionWeight;
          totalWeight += weight;
        }
      }

      return totalWeight > 0 ? score / totalWeight : 0;
    } catch (error) {
      console.error('Error calculating collaborative score:', error);
      return 0;
    }
  }

  /**
   * Content-based filtering using post features and user preferences
   */
  calculateContentScore(post, userProfile) {
    try {
      let score = 0;

      // Hashtag similarity
      if (post.aiTags && post.aiTags.length > 0 && userProfile.preferredTags) {
        const tagSimilarity = this.calculateTagSimilarity(post.aiTags, userProfile.preferredTags);
        score += tagSimilarity * 0.4;
      }

      // Content sentiment match
      if (post.content && userProfile.sentimentPreference) {
        const postSentiment = this.sentimentAnalyzer.analyze(post.content);
        const sentimentMatch = this.calculateSentimentMatch(postSentiment, userProfile.sentimentPreference);
        score += sentimentMatch * 0.2;
      }

      // Author preference
      if (userProfile.preferredAuthors && userProfile.preferredAuthors.includes(post.author._id.toString())) {
        score += 0.3;
      }

      // Media type preference
      if (post.media && post.media.length > 0 && userProfile.mediaPreferences) {
        const mediaType = post.media[0].type.startsWith('image') ? 'image' : 'video';
        if (userProfile.mediaPreferences[mediaType] > 0.5) {
          score += 0.1;
        }
      }

      return Math.min(score, 1.0);
    } catch (error) {
      console.error('Error calculating content score:', error);
      return 0;
    }
  }

  /**
   * Social graph influence scoring
   */
  async calculateSocialScore(userId, post) {
    try {
      // Get user's social connections
      const user = await findOne('users', { _id: new ObjectId(userId) });
      if (!user || !user.following) return 0;

      // Check if post author is followed
      if (user.following.includes(post.author._id.toString())) {
        return 0.8; // High score for followed users
      }

      // Check if friends of friends liked this post
      const friendsInteractions = await find('user_interactions', {
        postId: new ObjectId(post._id),
        userId: { $in: user.following.map(id => new ObjectId(id)) },
        type: { $in: ['like', 'comment', 'share'] }
      });

      return Math.min(friendsInteractions.length * 0.1, 0.6);
    } catch (error) {
      console.error('Error calculating social score:', error);
      return 0;
    }
  }

  /**
   * Calculate trending score based on recent engagement
   */
  calculateTrendingScore(post) {
    try {
      const now = new Date();
      const postAge = (now - new Date(post.createdAt)) / (1000 * 60 * 60); // hours

      // Decay factor for post age (24 hour window)
      const ageFactor = Math.max(0, 1 - (postAge / 24));

      // Engagement velocity
      const totalEngagement = (post.likes || 0) + (post.comments || 0) * 2 + (post.shareCount || 0) * 3;
      const engagementRate = postAge > 0 ? totalEngagement / postAge : totalEngagement;

      // Normalize engagement rate (assuming max 100 interactions per hour)
      const normalizedEngagement = Math.min(engagementRate / 100, 1);

      return ageFactor * normalizedEngagement;
    } catch (error) {
      console.error('Error calculating trending score:', error);
      return 0;
    }
  }

  /**
   * Calculate freshness score based on post recency
   */
  calculateFreshnessScore(post) {
    try {
      const now = new Date();
      const postAge = (now - new Date(post.createdAt)) / (1000 * 60 * 60); // hours

      // Exponential decay over 48 hours
      return Math.exp(-postAge / 48);
    } catch (error) {
      console.error('Error calculating freshness score:', error);
      return 0;
    }
  }

  /**
   * Calculate quality score based on various factors
   */
  calculateQualityScore(post) {
    try {
      let score = 1.0;

      // Penalize very short content
      if (post.content && post.content.length < 10) {
        score *= 0.7;
      }

      // Penalize posts with no engagement after some time
      const postAge = (new Date() - new Date(post.createdAt)) / (1000 * 60 * 60);
      if (postAge > 2 && (post.likes || 0) === 0 && (post.comments || 0) === 0) {
        score *= 0.5;
      }

      // Boost posts with media
      if (post.media && post.media.length > 0) {
        score *= 1.1;
      }

      // Boost verified authors
      if (post.author.isVerified) {
        score *= 1.05;
      }

      return Math.min(score, 1.0);
    } catch (error) {
      console.error('Error calculating quality score:', error);
      return 1.0;
    }
  }

  /**
   * Apply diversity filter to avoid echo chambers
   */
  applyDiversityFilter(scores, candidates) {
    const diversified = [];
    const usedTags = new Set();
    const usedAuthors = new Set();

    // Sort by score first
    const sorted = scores.sort((a, b) => b.score - a.score);

    for (const item of sorted) {
      let diversityPenalty = 0;

      // Check tag diversity
      if (item.post.aiTags) {
        const tagOverlap = item.post.aiTags.filter(tag => usedTags.has(tag)).length;
        diversityPenalty += tagOverlap * 0.1;

        // Add tags to used set
        item.post.aiTags.forEach(tag => usedTags.add(tag));
      }

      // Check author diversity
      const authorId = item.post.author._id.toString();
      if (usedAuthors.has(authorId)) {
        diversityPenalty += 0.2;
      }
      usedAuthors.add(authorId);

      // Apply penalty
      item.score = Math.max(0, item.score - diversityPenalty);
      diversified.push(item);
    }

    return diversified;
  }

  /**
   * Get comprehensive user profile with preferences and behavior patterns
   */
  async getUserProfile(userId) {
    try {
      // Check cache first
      if (this.userProfiles.has(userId)) {
        const cached = this.userProfiles.get(userId);
        if (Date.now() - cached.timestamp < this.config.updateInterval) {
          return cached.profile;
        }
      }

      // Get user basic info
      const user = await findOne('users', { _id: new ObjectId(userId) });
      if (!user) throw new Error('User not found');

      // Get user interactions for preference analysis
      const interactions = await find('user_interactions',
        { userId: new ObjectId(userId) },
        { sort: { createdAt: -1 }, limit: 1000 }
      );

      // Analyze tag preferences
      const tagFrequency = {};
      const authorFrequency = {};
      const mediaPreferences = { image: 0, video: 0 };
      let totalInteractions = 0;

      for (const interaction of interactions) {
        totalInteractions++;

        // Get post details for analysis
        const post = await findOne('posts', { _id: interaction.postId });
        if (!post) continue;

        // Analyze tags
        if (post.aiTags) {
          post.aiTags.forEach(tag => {
            tagFrequency[tag] = (tagFrequency[tag] || 0) + this.getInteractionWeight(interaction.type);
          });
        }

        // Analyze authors
        const authorId = post.author.toString();
        authorFrequency[authorId] = (authorFrequency[authorId] || 0) + this.getInteractionWeight(interaction.type);

        // Analyze media preferences
        if (post.media && post.media.length > 0) {
          const mediaType = post.media[0].type.startsWith('image') ? 'image' : 'video';
          mediaPreferences[mediaType] += this.getInteractionWeight(interaction.type);
        }
      }

      // Normalize preferences
      const preferredTags = Object.entries(tagFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 20)
        .map(([tag, weight]) => ({ tag, weight: weight / totalInteractions }));

      const preferredAuthors = Object.entries(authorFrequency)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([authorId]) => authorId);

      // Calculate sentiment preference
      const sentimentPreference = await this.calculateUserSentimentPreference(userId, interactions);

      const profile = {
        userId,
        preferredTags: preferredTags.reduce((acc, {tag, weight}) => {
          acc[tag] = weight;
          return acc;
        }, {}),
        preferredAuthors,
        mediaPreferences: {
          image: mediaPreferences.image / totalInteractions,
          video: mediaPreferences.video / totalInteractions
        },
        sentimentPreference,
        activityLevel: this.calculateActivityLevel(interactions),
        strength: Math.min(totalInteractions / 100, 1), // Profile strength 0-1
        lastUpdated: new Date()
      };

      // Cache the profile
      this.userProfiles.set(userId, {
        profile,
        timestamp: Date.now()
      });

      return profile;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return {
        userId,
        preferredTags: {},
        preferredAuthors: <AUTHORS>
        mediaPreferences: { image: 0.5, video: 0.5 },
        sentimentPreference: { positive: 0.5, negative: 0.3, neutral: 0.2 },
        activityLevel: 0.5,
        strength: 0
      };
    }
  }

  /**
   * Get user interaction history
   */
  async getUserInteractions(userId) {
    try {
      return await find('user_interactions',
        { userId: new ObjectId(userId) },
        { sort: { createdAt: -1 }, limit: 500 }
      );
    } catch (error) {
      console.error('Error getting user interactions:', error);
      return [];
    }
  }

  /**
   * Get candidate posts for recommendation
   */
  async getCandidatePosts(userId, excludePostIds = []) {
    try {
      const user = await findOne('users', { _id: new ObjectId(userId) });
      if (!user) return [];

      // Build exclusion criteria
      const excludeIds = excludePostIds.map(id => new ObjectId(id));
      const blacklistedUserIds = (user.blacklistedUsers || []).map(id => new ObjectId(id));

      // Get recent posts (last 7 days) that user hasn't interacted with
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      const candidates = await aggregate('posts', [
        {
          $match: {
            _id: { $nin: excludeIds },
            author: { $nin: blacklistedUserIds },
            createdAt: { $gte: sevenDaysAgo },
            isDeleted: { $ne: true },
            $or: [
              { visibility: 'public' },
              {
                visibility: 'followers',
                author: { $in: (user.following || []).map(id => new ObjectId(id)) }
              }
            ]
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'author',
            foreignField: '_id',
            as: 'authorDetails'
          }
        },
        { $unwind: '$authorDetails' },
        {
          $project: {
            _id: 1,
            content: 1,
            media: 1,
            visibility: 1,
            likes: { $size: '$likes' },
            comments: { $size: '$comments' },
            viewCount: 1,
            shareCount: 1,
            aiTags: 1,
            createdAt: 1,
            author: {
              _id: '$authorDetails._id',
              username: '$authorDetails.username',
              profilePicture: '$authorDetails.profilePicture',
              isPremium: '$authorDetails.isPremium',
              isVerified: '$authorDetails.isVerified'
            }
          }
        },
        { $limit: 1000 } // Limit candidates for performance
      ]);

      return candidates;
    } catch (error) {
      console.error('Error getting candidate posts:', error);
      return [];
    }
  }

  /**
   * Find users with similar interaction patterns
   */
  async findSimilarUsers(userId, userInteractions) {
    try {
      // Get posts that the user has interacted with
      const userPostIds = userInteractions.map(i => i.postId);

      if (userPostIds.length === 0) return [];

      // Find other users who interacted with the same posts
      const similarInteractions = await find('user_interactions', {
        postId: { $in: userPostIds },
        userId: { $ne: new ObjectId(userId) }
      });

      // Calculate similarity scores
      const userSimilarity = {};

      for (const interaction of similarInteractions) {
        const otherUserId = interaction.userId.toString();
        if (!userSimilarity[otherUserId]) {
          userSimilarity[otherUserId] = { common: 0, total: 0 };
        }
        userSimilarity[otherUserId].common++;
      }

      // Calculate Jaccard similarity
      const similarities = [];
      for (const [otherUserId, data] of Object.entries(userSimilarity)) {
        if (data.common >= this.config.minInteractions) {
          // Get total interactions for other user
          const otherUserInteractions = await find('user_interactions',
            { userId: new ObjectId(otherUserId) },
            { limit: 1000 }
          );

          const union = userInteractions.length + otherUserInteractions.length - data.common;
          const similarity = data.common / union;

          similarities.push({
            userId: new ObjectId(otherUserId),
            similarity,
            commonInteractions: data.common
          });
        }
      }

      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, 50); // Top 50 similar users
    } catch (error) {
      console.error('Error finding similar users:', error);
      return [];
    }
  }

  /**
   * Calculate tag similarity using cosine similarity
   */
  calculateTagSimilarity(postTags, userTagPreferences) {
    try {
      if (!postTags || postTags.length === 0) return 0;

      let similarity = 0;
      let totalWeight = 0;

      for (const tag of postTags) {
        const weight = userTagPreferences[tag] || 0;
        similarity += weight;
        totalWeight += 1;
      }

      return totalWeight > 0 ? similarity / totalWeight : 0;
    } catch (error) {
      console.error('Error calculating tag similarity:', error);
      return 0;
    }
  }

  /**
   * Calculate sentiment match between post and user preference
   */
  calculateSentimentMatch(postSentiment, userSentimentPreference) {
    try {
      const postScore = postSentiment.score;
      let postType = 'neutral';

      if (postScore > 2) postType = 'positive';
      else if (postScore < -2) postType = 'negative';

      return userSentimentPreference[postType] || 0.5;
    } catch (error) {
      console.error('Error calculating sentiment match:', error);
      return 0.5;
    }
  }

  /**
   * Calculate user sentiment preference based on interaction history
   */
  async calculateUserSentimentPreference(userId, interactions) {
    try {
      const sentimentCounts = { positive: 0, negative: 0, neutral: 0 };
      let totalAnalyzed = 0;

      for (const interaction of interactions.slice(0, 100)) { // Analyze recent 100
        const post = await findOne('posts', { _id: interaction.postId });
        if (!post || !post.content) continue;

        const sentiment = this.sentimentAnalyzer.analyze(post.content);
        const weight = this.getInteractionWeight(interaction.type);

        if (sentiment.score > 2) {
          sentimentCounts.positive += weight;
        } else if (sentiment.score < -2) {
          sentimentCounts.negative += weight;
        } else {
          sentimentCounts.neutral += weight;
        }
        totalAnalyzed += weight;
      }

      if (totalAnalyzed === 0) {
        return { positive: 0.5, negative: 0.3, neutral: 0.2 };
      }

      return {
        positive: sentimentCounts.positive / totalAnalyzed,
        negative: sentimentCounts.negative / totalAnalyzed,
        neutral: sentimentCounts.neutral / totalAnalyzed
      };
    } catch (error) {
      console.error('Error calculating sentiment preference:', error);
      return { positive: 0.5, negative: 0.3, neutral: 0.2 };
    }
  }

  /**
   * Calculate user activity level
   */
  calculateActivityLevel(interactions) {
    try {
      const now = new Date();
      const dayMs = 24 * 60 * 60 * 1000;

      // Count interactions in last 7 days
      const recentInteractions = interactions.filter(i =>
        (now - new Date(i.createdAt)) < (7 * dayMs)
      );

      // Normalize to 0-1 scale (assuming 50 interactions per week is high activity)
      return Math.min(recentInteractions.length / 50, 1);
    } catch (error) {
      console.error('Error calculating activity level:', error);
      return 0.5;
    }
  }

  /**
   * Get interaction weight based on type
   */
  getInteractionWeight(type) {
    const weights = {
      'view': 0.1,
      'like': 0.5,
      'comment': 0.8,
      'share': 1.0,
      'save': 0.9
    };
    return weights[type] || 0.1;
  }
}

module.exports = { RecommendationEngine };
