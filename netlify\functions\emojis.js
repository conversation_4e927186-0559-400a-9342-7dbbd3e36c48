const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth, requirePremium } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { uploadFile } = require('./utils/storage');

// Create custom emoji
exports.createEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { name, image, isAnimated } = JSON.parse(event.body);
  
  // Validate input
  if (!name || !image || !image.data) {
    return error('Name and image are required');
  }
  
  // Get user to check emoji count and premium status
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Check if user has reached the free tier limit
  if (!user.isPremium && user.emojiCount >= 10) {
    return error('You have reached the limit of free emoji creations. Please upgrade to NTA+.', 403);
  }
  
  // Check if animated emoji is allowed for non-premium users
  if (isAnimated && !user.isPremium) {
    return error('Animated emojis are only available for NTA+ subscribers', 403);
  }
  
  // Decode base64 image
  const fileBuffer = Buffer.from(image.data, 'base64');
  
  // Generate unique filename
  const fileExtension = isAnimated ? 'gif' : 'png';
  const fileName = `emojis/${userId}_${Date.now()}.${fileExtension}`;
  
  // Upload to B2
  const uploadResult = await uploadFile(fileBuffer, fileName, isAnimated ? 'image/gif' : 'image/png');
  
  // Create emoji object
  const emoji = {
    creator: new ObjectId(userId),
    name,
    imageUrl: uploadResult.fileUrl,
    isAnimated,
    usageCount: 0,
    favorites: [],
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  // Insert emoji into database
  const result = await insertOne('emojis', emoji);
  
  // Increment user's emoji count
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $inc: { emojiCount: 1 } }
  );
  
  // Get the inserted emoji
  const newEmoji = await findOne('emojis', { _id: result.insertedId });
  
  // Return the new emoji
  return success({
    emoji: newEmoji,
    emojiCount: user.emojiCount + 1,
    emojiLimit: user.isPremium ? 'unlimited' : 10
  });
}));

// Get user's emojis
exports.getUserEmojis = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get emojis created by the user
  const emojis = await find(
    'emojis',
    { creator: new ObjectId(userId) },
    { sort: { createdAt: -1 } }
  );
  
  // Return emojis
  return success({ emojis });
}));

// Get popular emojis
exports.getPopularEmojis = handleAsync(async (event) => {
  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 20;
  const skip = (page - 1) * limit;
  
  // Get popular emojis
  const emojis = await aggregate('emojis', [
    { $lookup: {
        from: 'users',
        localField: 'creator',
        foreignField: '_id',
        as: 'creatorDetails'
      }
    },
    { $unwind: '$creatorDetails' },
    { $project: {
        _id: 1,
        name: 1,
        imageUrl: 1,
        isAnimated: 1,
        usageCount: 1,
        favoriteCount: { $size: '$favorites' },
        createdAt: 1,
        creator: {
          _id: '$creatorDetails._id',
          username: '$creatorDetails.username'
        }
      }
    },
    { $sort: { usageCount: -1, favoriteCount: -1 } },
    { $skip: skip },
    { $limit: limit }
  ]);
  
  // Return emojis
  return success({ emojis, page, limit });
});

// Favorite an emoji
exports.favoriteEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Check if emoji exists
  const emoji = await findOne('emojis', { _id: new ObjectId(emojiId) });
  if (!emoji) {
    return error('Emoji not found', 404);
  }
  
  // Get user to check favorite limit
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Count user's favorited emojis
  const favoritedCount = await aggregate('emojis', [
    { $match: { favorites: new ObjectId(userId) } },
    { $count: 'count' }
  ]);
  
  const currentFavorites = favoritedCount.length > 0 ? favoritedCount[0].count : 0;
  
  // Check if user has reached the free tier limit
  if (!user.isPremium && currentFavorites >= 15 && !emoji.favorites.some(id => id.toString() === userId)) {
    return error('You have reached the limit of favorited emojis. Please upgrade to NTA+.', 403);
  }
  
  // Add user to favorites array
  await updateOne(
    'emojis',
    { _id: new ObjectId(emojiId) },
    { $addToSet: { favorites: new ObjectId(userId) } }
  );
  
  // Return success
  return success({
    message: 'Emoji favorited successfully',
    favoriteCount: currentFavorites + 1,
    favoriteLimit: user.isPremium ? 'unlimited' : 15
  });
}));

// Unfavorite an emoji
exports.unfavoriteEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Check if emoji exists
  const emoji = await findOne('emojis', { _id: new ObjectId(emojiId) });
  if (!emoji) {
    return error('Emoji not found', 404);
  }
  
  // Remove user from favorites array
  await updateOne(
    'emojis',
    { _id: new ObjectId(emojiId) },
    { $pull: { favorites: new ObjectId(userId) } }
  );
  
  // Return success
  return success({ message: 'Emoji unfavorited successfully' });
}));

// Get user's favorite emojis
exports.getFavoriteEmojis = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get emojis favorited by the user
  const emojis = await aggregate('emojis', [
    { $match: { favorites: new ObjectId(userId) } },
    { $lookup: {
        from: 'users',
        localField: 'creator',
        foreignField: '_id',
        as: 'creatorDetails'
      }
    },
    { $unwind: '$creatorDetails' },
    { $project: {
        _id: 1,
        name: 1,
        imageUrl: 1,
        isAnimated: 1,
        usageCount: 1,
        createdAt: 1,
        creator: {
          _id: '$creatorDetails._id',
          username: '$creatorDetails.username'
        }
      }
    },
    { $sort: { createdAt: -1 } }
  ]);
  
  // Return emojis
  return success({ emojis });
}));

// Use emoji (increment usage count)
exports.useEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Check if emoji exists
  const emoji = await findOne('emojis', { _id: new ObjectId(emojiId) });
  if (!emoji) {
    return error('Emoji not found', 404);
  }
  
  // Increment usage count
  await updateOne(
    'emojis',
    { _id: new ObjectId(emojiId) },
    { $inc: { usageCount: 1 } }
  );
  
  // Return success
  return success({ message: 'Emoji usage recorded' });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/emojis', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/create' && event.httpMethod === 'POST':
      return exports.createEmoji(event, context);
    case path === '/user' && event.httpMethod === 'GET':
      return exports.getUserEmojis(event, context);
    case path === '/popular' && event.httpMethod === 'GET':
      return exports.getPopularEmojis(event, context);
    case path === '/favorite' && event.httpMethod === 'POST':
      return exports.favoriteEmoji(event, context);
    case path === '/unfavorite' && event.httpMethod === 'POST':
      return exports.unfavoriteEmoji(event, context);
    case path === '/favorites' && event.httpMethod === 'GET':
      return exports.getFavoriteEmojis(event, context);
    case path === '/use' && event.httpMethod === 'POST':
      return exports.useEmoji(event, context);
    default:
      return error('Not found', 404);
  }
};
