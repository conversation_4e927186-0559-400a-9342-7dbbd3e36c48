<template>
  <div class="ai-enhanced-feed">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading your personalized feed...</p>
    </div>
    
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="fetchPosts" class="retry-button">Retry</button>
    </div>
    
    <div v-else class="feed-container">
      <div v-if="posts.length === 0" class="empty-feed">
        <h3>Your feed is empty</h3>
        <p>Follow some users to see their posts here, or check out the discover page to find new content.</p>
        <router-link to="/discover" class="discover-link">Discover Users</router-link>
      </div>
      
      <div v-else class="posts-container">
        <div v-for="(item, index) in feedItems" :key="item.id" class="feed-item">
          <!-- Ad item -->
          <div v-if="item.type === 'ad'" class="ad-container">
            <div class="ad-label">Sponsored</div>
            <div class="ad-content">
              <img :src="item.ad.imageUrl" :alt="item.ad.title" class="ad-image" />
              <div class="ad-info">
                <h3>{{ item.ad.title }}</h3>
                <p>{{ item.ad.description }}</p>
                <a :href="item.ad.linkUrl" @click="trackAdClick(item.ad)" class="ad-link">Learn More</a>
              </div>
            </div>
          </div>
          
          <!-- Post item -->
          <post-card 
            v-else-if="item.type === 'post'" 
            :post="item.post" 
            @view="startViewTracking(item.post._id)"
            @share="sharePost(item.post)"
          />
        </div>
        
        <div v-if="hasMore && !loadingMore" class="load-more">
          <button @click="loadMorePosts" class="load-more-button">Load More</button>
        </div>
        
        <div v-if="loadingMore" class="loading-more">
          <div class="loading-spinner small"></div>
          <span>Loading more posts...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { usePostsStore } from '@/stores/posts';
import { useAdsStore } from '@/stores/ads';
import PostCard from '@/components/post/PostCard.vue';

// Stores
const postsStore = usePostsStore();
const adsStore = useAdsStore();

// State
const posts = ref([]);
const loading = ref(true);
const error = ref(null);
const loadingMore = ref(false);
const hasMore = ref(true);
const page = ref(1);
const viewTimeTracker = ref(null);

// Computed
const feedItems = computed(() => {
  const items = [];
  
  // Add posts and ads to feed
  posts.value.forEach((post, index) => {
    // Add post
    items.push({
      id: `post-${post._id}`,
      type: 'post',
      post
    });
    
    // Add ad every 5 posts
    const ad = adsStore.getAdForFeed(index);
    if (ad) {
      items.push({
        id: `ad-${ad._id}`,
        type: 'ad',
        ad
      });
    }
  });
  
  return items;
});

// Methods
async function fetchPosts() {
  loading.value = true;
  error.value = null;
  
  try {
    // Reset page
    page.value = 1;
    
    // Fetch posts
    const fetchedPosts = await postsStore.fetchPosts(page.value);
    posts.value = fetchedPosts;
    
    // Check if there are more posts
    hasMore.value = postsStore.hasMore;
    
    // Fetch ads
    await adsStore.fetchFeedAds();
  } catch (err) {
    error.value = err.message || 'Failed to load posts';
  } finally {
    loading.value = false;
  }
}

async function loadMorePosts() {
  if (loadingMore.value || !hasMore.value) return;
  
  loadingMore.value = true;
  
  try {
    // Increment page
    page.value++;
    
    // Fetch more posts
    const morePosts = await postsStore.fetchPosts(page.value);
    
    // Add to posts
    posts.value = [...posts.value, ...morePosts];
    
    // Check if there are more posts
    hasMore.value = postsStore.hasMore;
  } catch (err) {
    console.error('Failed to load more posts:', err);
  } finally {
    loadingMore.value = false;
  }
}

function startViewTracking(postId) {
  postsStore.startViewTracking(postId);
}

function trackAdClick(ad) {
  adsStore.trackClick(ad._id, 'feed');
}

async function sharePost(post) {
  try {
    const result = await postsStore.sharePost(post);
    
    if (result.success) {
      // Show success message
      alert(result.message);
    }
  } catch (err) {
    console.error('Failed to share post:', err);
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchPosts();
  
  // Set up scroll event listener for infinite scroll
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  // Clean up
  postsStore.stopViewTracking();
  
  // Remove scroll event listener
  window.removeEventListener('scroll', handleScroll);
});

// Infinite scroll
function handleScroll() {
  const scrollPosition = window.innerHeight + window.scrollY;
  const documentHeight = document.documentElement.offsetHeight;
  
  // Load more posts when user scrolls to bottom
  if (scrollPosition >= documentHeight - 500 && !loadingMore.value && hasMore.value) {
    loadMorePosts();
  }
}
</script>

<style scoped>
.ai-enhanced-feed {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 1rem;
}

.loading-container, .error-container, .empty-feed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-top: 1rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
  margin-right: 0.5rem;
  margin-bottom: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-button, .load-more-button, .discover-link {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1rem;
  text-decoration: none;
  display: inline-block;
}

.retry-button:hover, .load-more-button:hover, .discover-link:hover {
  background-color: #2980b9;
}

.feed-container {
  margin-top: 1rem;
}

.posts-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feed-item {
  margin-bottom: 1rem;
}

.ad-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #eaeaea;
}

.ad-label {
  font-size: 0.8rem;
  color: #777;
  margin-bottom: 0.5rem;
}

.ad-content {
  display: flex;
  gap: 1rem;
}

.ad-image {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
}

.ad-info {
  flex: 1;
}

.ad-info h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.ad-info p {
  margin-bottom: 0.5rem;
  color: #555;
}

.ad-link {
  color: #3498db;
  text-decoration: none;
  font-weight: bold;
}

.ad-link:hover {
  text-decoration: underline;
}

.load-more, .loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.loading-more {
  color: #777;
}
</style>
