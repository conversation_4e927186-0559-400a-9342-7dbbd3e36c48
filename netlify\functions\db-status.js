const { checkDatabaseStatus } = require('./utils/db');
const { success, error, handleAsync } = require('./utils/response');

// Get database status
exports.getStatus = handleAsync(async () => {
  try {
    const status = await checkDatabaseStatus();
    return success({ status });
  } catch (err) {
    console.error('Error checking database status:', err);
    return error('Failed to check database status', 500);
  }
});

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  console.log('Database status check requested');
  
  // Only allow GET requests
  if (event.httpMethod !== 'GET') {
    return error('Method not allowed', 405);
  }
  
  return exports.getStatus(event, context);
};
