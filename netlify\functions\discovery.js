const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { findOne, find, aggregate } = require('./utils/db');
const { ObjectId } = require('mongodb');

// Get recommended users
exports.getRecommendedUsers = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  if (!user) {
    return error('User not found', 404);
  }
  
  // Get user's following
  const following = user.following || [];
  
  // Convert to ObjectId
  const followingIds = following.map(id => new ObjectId(id));
  
  // Add user's own ID to exclude from recommendations
  followingIds.push(new ObjectId(userId));
  
  // Get user's blacklisted users
  const blacklistedUsers = user.blacklistedUsers || [];
  
  // Convert to ObjectId
  const blacklistedIds = blacklistedUsers.map(id => new ObjectId(id));
  
  // Combine IDs to exclude
  const excludeIds = [...followingIds, ...blacklistedIds];
  
  // Get user's interests (placeholder for now)
  const interests = user.interests || [];
  
  // In a real implementation, we would use embeddings and a similarity search
  // For now, we'll use a simple algorithm based on common interests and activity
  
  // Get users with common interests
  let recommendedUsers = [];
  
  if (interests.length > 0) {
    // Find users with common interests
    recommendedUsers = await find(
      'users',
      {
        _id: { $nin: excludeIds },
        interests: { $in: interests }
      },
      {
        limit: 10,
        sort: { lastActivityAt: -1 }
      }
    );
  }
  
  // If we don't have enough recommendations, add some active users
  if (recommendedUsers.length < 10) {
    const additionalUsers = await find(
      'users',
      {
        _id: { $nin: [...excludeIds, ...recommendedUsers.map(u => u._id)] }
      },
      {
        limit: 10 - recommendedUsers.length,
        sort: { lastActivityAt: -1 }
      }
    );
    
    recommendedUsers = [...recommendedUsers, ...additionalUsers];
  }
  
  // Calculate a simple recommendation score for each user
  // In a real implementation, this would be much more sophisticated
  recommendedUsers = recommendedUsers.map(user => {
    // Calculate common interests
    const commonInterests = interests.filter(interest => 
      user.interests && user.interests.includes(interest)
    );
    
    // Calculate a simple score
    const score = (commonInterests.length * 10) + 
                  (user.isPremium ? 5 : 0) + 
                  (user.postCount || 0) / 10;
    
    return {
      ...user,
      recommendationScore: score
    };
  });
  
  // Sort by recommendation score
  recommendedUsers.sort((a, b) => b.recommendationScore - a.recommendationScore);
  
  // Remove sensitive information
  recommendedUsers = recommendedUsers.map(user => ({
    _id: user._id,
    username: user.username,
    displayName: user.displayName,
    profilePicture: user.profilePicture,
    bio: user.bio,
    isPremium: user.isPremium,
    recommendationScore: user.recommendationScore,
    commonInterests: interests.filter(interest => 
      user.interests && user.interests.includes(interest)
    )
  }));
  
  // Return recommended users
  return success({ users: recommendedUsers });
}));

// Get trending users
exports.getTrendingUsers = handleAsync(async () => {
  // In a real implementation, we would calculate trending users based on engagement
  // For now, we'll use a simple algorithm based on recent activity and post count
  
  // Get users with recent activity and high post count
  const trendingUsers = await aggregate('users', [
    {
      $match: {
        lastActivityAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Active in the last 7 days
      }
    },
    {
      $project: {
        _id: 1,
        username: 1,
        displayName: 1,
        profilePicture: 1,
        bio: 1,
        isPremium: 1,
        postCount: { $ifNull: ['$postCount', 0] },
        followerCount: { $size: { $ifNull: ['$followers', []] } },
        engagementScore: {
          $add: [
            { $ifNull: ['$postCount', 0] },
            { $multiply: [{ $size: { $ifNull: ['$followers', []] } }, 2] }
          ]
        }
      }
    },
    {
      $sort: { engagementScore: -1 }
    },
    {
      $limit: 20
    }
  ]);
  
  // Return trending users
  return success({ users: trendingUsers });
});

// Search users
exports.searchUsers = handleAsync(async (event) => {
  // Get query from query parameters
  const query = event.queryStringParameters.q;
  
  // Validate input
  if (!query) {
    return error('Query is required');
  }
  
  // Search users by username or display name
  const users = await find(
    'users',
    {
      $or: [
        { username: { $regex: query, $options: 'i' } },
        { displayName: { $regex: query, $options: 'i' } }
      ]
    },
    {
      limit: 20,
      projection: {
        _id: 1,
        username: 1,
        displayName: 1,
        profilePicture: 1,
        bio: 1,
        isPremium: 1
      }
    }
  );
  
  // Return users
  return success({ users });
});

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/discovery', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/recommended' && event.httpMethod === 'GET':
      return exports.getRecommendedUsers(event, context);
    case path === '/trending' && event.httpMethod === 'GET':
      return exports.getTrendingUsers(event, context);
    case path === '/search' && event.httpMethod === 'GET':
      return exports.searchUsers(event, context);
    default:
      return error('Not found', 404);
  }
};
