<script setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// Form data
const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');

// Computed properties
const isFormValid = computed(() => {
  return email.value && password.value;
});

// Handle login
const handleLogin = async () => {
  if (!isFormValid.value || isSubmitting.value) return;

  errorMessage.value = '';
  isSubmitting.value = true;

  try {
    // Call the real backend API
    await authStore.login({
      email: email.value,
      password: password.value
    });

    // Redirect to the intended page or feed
    const redirectPath = route.query.redirect || '/feed';
    router.push(redirectPath);
  } catch (error) {
    console.error('Login error:', error);
    errorMessage.value = error.message || 'Login failed. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="min-h-screen flex flex-col justify-center items-center px-4 py-12 bg-dark-bg">
    <div class="w-full max-w-md">
      <!-- Logo -->
      <div class="flex justify-center mb-8">
        <img src="/logo2.png" alt="NeTuArk Logo" class="h-12" />
      </div>

      <!-- Login Form -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 shadow-lg p-8">
        <h2 class="text-2xl font-bold mb-6 text-center">Welcome Back</h2>

        <!-- Error message -->
        <div v-if="errorMessage" class="mb-6 p-3 bg-red-900/30 border border-red-800 rounded-md text-red-200 text-sm">
          {{ errorMessage }}
        </div>

        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Password -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Password</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="••••••••"
            />
          </div>

          <!-- Remember me -->
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-700 bg-dark-bg text-neon-blue focus:ring-neon-blue"
              />
              <label for="remember-me" class="ml-2 block text-sm text-gray-300">Remember me</label>
            </div>

            <div class="text-sm">
              <a href="#" class="text-neon-blue hover:text-neon-blue/80">Forgot password?</a>
            </div>
          </div>

          <!-- Submit button -->
          <div>
            <button
              type="submit"
              :disabled="!isFormValid || isSubmitting"
              class="w-full neon-button py-3 px-4 flex justify-center"
              :class="{ 'opacity-70 cursor-not-allowed': !isFormValid || isSubmitting }"
            >
              <span v-if="isSubmitting">Logging in...</span>
              <span v-else>Log in</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Register link -->
      <div class="mt-6 text-center">
        <p class="text-gray-400">
          Don't have an account?
        </p>
        <router-link
          to="/register"
          class="mt-3 inline-block px-6 py-2 rounded-full bg-neon-purple hover:bg-neon-purple/80 text-white font-medium transition-all duration-200 transform hover:scale-105"
        >
          Create Account
        </router-link>
      </div>
    </div>
  </div>
</template>
