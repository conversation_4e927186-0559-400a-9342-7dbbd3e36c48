@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #0a0a0a;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

@layer components {
  .neon-text-blue {
    @apply text-neon-blue;
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #00f3ff, 0 0 20px #00f3ff, 0 0 25px #00f3ff;
  }

  .neon-text-purple {
    @apply text-neon-purple;
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #9d00ff, 0 0 20px #9d00ff, 0 0 25px #9d00ff;
  }

  .neon-text-pink {
    @apply text-neon-pink;
    text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #ff00f7, 0 0 20px #ff00f7, 0 0 25px #ff00f7;
  }

  .neon-button {
    @apply px-6 py-3 rounded-md font-bold transition-all duration-300 border-2 border-neon-blue bg-transparent text-white relative overflow-hidden;
    box-shadow: 0 0 5px #00f3ff, 0 0 10px #00f3ff;
  }

  .neon-button:hover {
    @apply bg-neon-blue/10;
    box-shadow: 0 0 10px #00f3ff, 0 0 20px #00f3ff, 0 0 30px #00f3ff;
  }
}
