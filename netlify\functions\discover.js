const { ObjectId } = require('mongodb');
const { findOne, find, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Discover users
exports.discoverUsers = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Get user to check blacklisted users
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Convert blacklisted user IDs to ObjectId
  const blacklistedUserIds = (user.blacklistedUsers || []).map(id => new ObjectId(id));
  
  // Add current user to excluded list
  const excludedUserIds = [...blacklistedUserIds, new ObjectId(userId)];
  
  // Get users excluding blacklisted users and current user
  const users = await aggregate('users', [
    { $match: { _id: { $nin: excludedUserIds } } },
    // In a real implementation, this would use more sophisticated recommendation logic
    // For now, we'll just sort by creation date to show newer users
    { $sort: { createdAt: -1 } },
    { $skip: skip },
    { $limit: limit },
    // Add follow status and counts
    {
      $addFields: {
        isFollowing: {
          $in: [new ObjectId(userId), { $ifNull: ["$followers", []] }]
        },
        followersCount: { $size: { $ifNull: ["$followers", []] } },
        followingCount: { $size: { $ifNull: ["$following", []] } },
        postsCount: 0 // Will be populated by a separate lookup if needed
      }
    },
    { $project: {
        _id: 1,
        username: 1,
        displayName: 1,
        bio: 1,
        profilePicture: 1,
        isPremium: 1,
        isVerified: 1,
        createdAt: 1,
        isFollowing: 1,
        followersCount: 1,
        followingCount: 1,
        postsCount: 1
      }
    }
  ]);
  
  // Return users
  return success({ users, page, limit });
}));

// Get trending posts
exports.getTrendingPosts = handleAsync(async (event) => {
  // Get pagination parameters
  const limit = parseInt(event.queryStringParameters?.limit) || 10;

  // Get trending posts based on engagement metrics
  const posts = await aggregate('posts', [
    // Match posts from the last 7 days
    {
      $match: {
        createdAt: {
          $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        visibility: 'public'
      }
    },
    // Add engagement score calculation
    {
      $addFields: {
        engagementScore: {
          $add: [
            { $multiply: [{ $size: { $ifNull: ["$likes", []] } }, 2] }, // Likes worth 2 points
            { $multiply: [{ $size: { $ifNull: ["$comments", []] } }, 3] }, // Comments worth 3 points
            { $multiply: [{ $ifNull: ["$viewCount", 0] }, 0.1] } // Views worth 0.1 points
          ]
        }
      }
    },
    // Sort by engagement score
    { $sort: { engagementScore: -1, createdAt: -1 } },
    { $limit: limit },
    // Lookup author information
    {
      $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'author'
      }
    },
    { $unwind: '$author' },
    // Project required fields
    {
      $project: {
        _id: 1,
        content: 1,
        media: 1,
        aiTags: 1,
        likes: 1,
        comments: 1,
        viewCount: 1,
        createdAt: 1,
        engagementScore: 1,
        'author._id': 1,
        'author.username': 1,
        'author.profilePicture': 1,
        'author.isPremium': 1
      }
    }
  ]);

  // Return trending posts
  return success({ posts });
});

// Get trending hashtags
exports.getTrendingHashtags = handleAsync(async (event) => {
  // Get pagination parameters
  const limit = parseInt(event.queryStringParameters?.limit) || 10;

  // Get trending hashtags from recent posts
  const hashtags = await aggregate('posts', [
    // Match posts from the last 7 days
    {
      $match: {
        createdAt: {
          $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        },
        aiTags: { $exists: true, $ne: [] }
      }
    },
    // Unwind aiTags array
    { $unwind: '$aiTags' },
    // Group by tag and count occurrences
    {
      $group: {
        _id: '$aiTags',
        postCount: { $sum: 1 },
        latestPost: { $max: '$createdAt' }
      }
    },
    // Sort by post count and recency
    { $sort: { postCount: -1, latestPost: -1 } },
    { $limit: limit },
    // Format output
    {
      $project: {
        _id: 0,
        tag: '$_id',
        postCount: 1,
        description: {
          $concat: ['Trending topic with ', { $toString: '$postCount' }, ' posts']
        }
      }
    }
  ]);

  // Return trending hashtags
  return success({ hashtags });
});

// Search users
exports.searchUsers = handleAsync(async (event) => {
  // Get search query
  const query = event.queryStringParameters.q;
  
  // Validate input
  if (!query) {
    return error('Search query is required');
  }
  
  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Search users by username
  const users = await aggregate('users', [
    { $match: { username: { $regex: query, $options: 'i' } } },
    { $sort: { isPremium: -1, username: 1 } }, // Premium users first, then alphabetical
    { $skip: skip },
    { $limit: limit },
    { $project: {
        _id: 1,
        username: 1,
        bio: 1,
        profilePicture: 1,
        isPremium: 1
      }
    }
  ]);
  
  // Return search results
  return success({ users, query, page, limit });
});

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/discover', '');

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/users' && event.httpMethod === 'GET':
      return exports.discoverUsers(event, context);
    case path === '/trending' && event.httpMethod === 'GET':
      return exports.getTrendingPosts(event, context);
    case path === '/hashtags' && event.httpMethod === 'GET':
      return exports.getTrendingHashtags(event, context);
    case path === '/search' && event.httpMethod === 'GET':
      return exports.searchUsers(event, context);
    default:
      return error('Not found', 404);
  }
};
