const { ObjectId } = require('mongodb');
const { findOne, find, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Discover users
exports.discoverUsers = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Get user to check blacklisted users
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Convert blacklisted user IDs to ObjectId
  const blacklistedUserIds = (user.blacklistedUsers || []).map(id => new ObjectId(id));
  
  // Add current user to excluded list
  const excludedUserIds = [...blacklistedUserIds, new ObjectId(userId)];
  
  // Get users excluding blacklisted users and current user
  const users = await aggregate('users', [
    { $match: { _id: { $nin: excludedUserIds } } },
    // In a real implementation, this would use more sophisticated recommendation logic
    // For now, we'll just sort by creation date to show newer users
    { $sort: { createdAt: -1 } },
    { $skip: skip },
    { $limit: limit },
    { $project: {
        _id: 1,
        username: 1,
        bio: 1,
        profilePicture: 1,
        isPremium: 1,
        createdAt: 1
      }
    }
  ]);
  
  // Return users
  return success({ users, page, limit });
}));

// Get trending users
exports.getTrendingUsers = handleAsync(async (event) => {
  // Get pagination parameters
  const limit = parseInt(event.queryStringParameters?.limit) || 5;
  
  // In a real implementation, this would use engagement metrics to determine trending users
  // For now, we'll simulate trending users with a random selection
  const users = await aggregate('users', [
    // Sample random users
    { $sample: { size: limit } },
    { $project: {
        _id: 1,
        username: 1,
        bio: 1,
        profilePicture: 1,
        isPremium: 1
      }
    }
  ]);
  
  // Return trending users
  return success({ users });
});

// Search users
exports.searchUsers = handleAsync(async (event) => {
  // Get search query
  const query = event.queryStringParameters.q;
  
  // Validate input
  if (!query) {
    return error('Search query is required');
  }
  
  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Search users by username
  const users = await aggregate('users', [
    { $match: { username: { $regex: query, $options: 'i' } } },
    { $sort: { isPremium: -1, username: 1 } }, // Premium users first, then alphabetical
    { $skip: skip },
    { $limit: limit },
    { $project: {
        _id: 1,
        username: 1,
        bio: 1,
        profilePicture: 1,
        isPremium: 1
      }
    }
  ]);
  
  // Return search results
  return success({ users, query, page, limit });
});

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/discover', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/users' && event.httpMethod === 'GET':
      return exports.discoverUsers(event, context);
    case path === '/trending' && event.httpMethod === 'GET':
      return exports.getTrendingUsers(event, context);
    case path === '/search' && event.httpMethod === 'GET':
      return exports.searchUsers(event, context);
    default:
      return error('Not found', 404);
  }
};
