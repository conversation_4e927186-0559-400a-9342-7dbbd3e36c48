<template>
  <div class="privacy-settings">
    <div class="settings-header">
      <h2>Privacy & Security</h2>
      <p class="settings-description">
        Control who can see your content and interact with you on NeTuArk.
      </p>
    </div>

    <div class="settings-sections">
      <!-- Privacy Settings -->
      <div class="settings-section">
        <div class="section-header">
          <h3>
            <i class="fas fa-shield-alt"></i>
            Privacy Settings
          </h3>
        </div>

        <div class="settings-form">
          <div v-if="loadingSettings" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading privacy settings...</p>
          </div>

          <div v-else-if="settingsError" class="error-container">
            <p>{{ settingsError }}</p>
            <button @click="fetchPrivacySettings" class="retry-button">Retry</button>
          </div>

          <div v-else class="form-groups">
            <div class="form-group">
              <label for="post-visibility">Default Post Visibility</label>
              <select id="post-visibility" v-model="settings.postVisibility" @change="updateSettings">
                <option value="public">Public - Anyone can see</option>
                <option value="followers">Followers Only</option>
                <option value="private">Private - Only you</option>
              </select>
              <div class="form-help">
                Choose who can see your posts by default. You can change this for individual posts.
              </div>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  v-model="settings.allowFollowWithoutView" 
                  @change="updateSettings"
                />
                <span class="checkmark"></span>
                Allow following without viewing content
                <div class="form-help">
                  Let users follow you without being able to see your posts.
                </div>
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  v-model="settings.allowMentions" 
                  @change="updateSettings"
                />
                <span class="checkmark"></span>
                Allow mentions in posts and comments
                <div class="form-help">
                  Other users can mention you using @{{ currentUser?.username }}.
                </div>
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  v-model="settings.allowDirectMessages" 
                  @change="updateSettings"
                />
                <span class="checkmark"></span>
                Allow direct messages from anyone
                <div class="form-help">
                  If disabled, only people you follow can send you messages.
                </div>
              </label>
            </div>

            <div v-if="updatingSettings" class="updating-indicator">
              <i class="fas fa-spinner fa-spin"></i>
              Saving changes...
            </div>

            <div v-if="updateSuccess" class="success-message">
              <i class="fas fa-check-circle"></i>
              Settings updated successfully!
            </div>

            <div v-if="updateError" class="error-message">
              {{ updateError }}
            </div>
          </div>
        </div>
      </div>

      <!-- Blacklist Management -->
      <div class="settings-section">
        <div class="section-header">
          <h3>
            <i class="fas fa-user-slash"></i>
            Blocked Users
          </h3>
          <button @click="showAddUserModal = true" class="add-user-btn">
            <i class="fas fa-plus"></i> Block User
          </button>
        </div>

        <div class="blacklist-content">
          <div v-if="loadingBlacklist" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading blocked users...</p>
          </div>

          <div v-else-if="blacklistError" class="error-container">
            <p>{{ blacklistError }}</p>
            <button @click="fetchBlacklistedUsers" class="retry-button">Retry</button>
          </div>

          <div v-else-if="blacklistedUsers.length === 0" class="empty-blacklist">
            <div class="empty-content">
              <i class="fas fa-user-check empty-icon"></i>
              <h4>No blocked users</h4>
              <p>You haven't blocked anyone yet. Blocked users won't be able to see your content or interact with you.</p>
            </div>
          </div>

          <div v-else class="blacklist-list">
            <div 
              v-for="user in blacklistedUsers" 
              :key="user._id" 
              class="blacklist-item"
            >
              <div class="user-info">
                <img :src="user.profilePicture || defaultAvatar(user.username)" :alt="user.username" class="user-avatar" />
                <div class="user-details">
                  <div class="username">{{ user.username }}</div>
                  <div v-if="user.isPremium" class="premium-badge">
                    <i class="fas fa-crown"></i> Premium
                  </div>
                </div>
              </div>
              
              <button 
                @click="unblockUser(user)" 
                class="unblock-btn"
                :disabled="unblockingUser === user._id"
              >
                <span v-if="unblockingUser === user._id">
                  <i class="fas fa-spinner fa-spin"></i>
                </span>
                <span v-else>
                  <i class="fas fa-unlock"></i> Unblock
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add User to Blacklist Modal -->
    <div v-if="showAddUserModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Block User</h3>
          <button @click="closeAddUserModal" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="user-search">
            <label for="user-search-input">Search for a user to block:</label>
            <input 
              type="text" 
              id="user-search-input"
              v-model="userSearchQuery" 
              placeholder="Enter username..." 
              class="search-input"
              @input="searchUsers"
            />
            
            <div v-if="searchingUsers" class="loading-container small">
              <div class="loading-spinner small"></div>
              <p>Searching...</p>
            </div>
            
            <div v-else-if="userSearchError" class="error-container small">
              <p>{{ userSearchError }}</p>
            </div>
            
            <div v-else-if="userSearchResults.length === 0 && userSearchQuery.trim().length > 0" class="empty-results">
              <p>No users found</p>
            </div>
            
            <div v-else-if="userSearchResults.length > 0" class="user-results">
              <div 
                v-for="user in userSearchResults" 
                :key="user._id" 
                class="user-result-item"
                @click="blockUser(user)"
              >
                <img :src="user.profilePicture || defaultAvatar(user.username)" :alt="user.username" class="user-avatar small" />
                <div class="user-info">
                  <div class="username">{{ user.username }}</div>
                  <div v-if="user.isPremium" class="premium-indicator">Premium</div>
                </div>
                <button class="block-btn">
                  <i class="fas fa-ban"></i> Block
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import { useAuthStore } from '@/stores/auth';
import { useDiscoveryStore } from '@/stores/discovery';

// Stores
const userStore = useUserStore();
const authStore = useAuthStore();
const discoveryStore = useDiscoveryStore();

// State
const settings = ref({
  postVisibility: 'public',
  allowFollowWithoutView: false,
  allowMentions: true,
  allowDirectMessages: true
});

const loadingSettings = ref(true);
const loadingBlacklist = ref(true);
const settingsError = ref(null);
const blacklistError = ref(null);
const updatingSettings = ref(false);
const updateSuccess = ref(false);
const updateError = ref(null);
const unblockingUser = ref(null);

// Add user modal
const showAddUserModal = ref(false);
const userSearchQuery = ref('');
const userSearchResults = ref([]);
const searchingUsers = ref(false);
const userSearchError = ref(null);
const searchTimeout = ref(null);

// Computed
const blacklistedUsers = computed(() => userStore.userBlacklist);
const currentUser = computed(() => authStore.user);

// Methods
function defaultAvatar(username) {
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=95a5a6&color=fff&size=64`;
}

async function fetchPrivacySettings() {
  loadingSettings.value = true;
  settingsError.value = null;
  
  try {
    const fetchedSettings = await userStore.fetchPrivacySettings();
    settings.value = { ...settings.value, ...fetchedSettings };
  } catch (err) {
    settingsError.value = err.message || 'Failed to load privacy settings';
  } finally {
    loadingSettings.value = false;
  }
}

async function fetchBlacklistedUsers() {
  loadingBlacklist.value = true;
  blacklistError.value = null;
  
  try {
    await userStore.fetchBlacklistedUsers();
  } catch (err) {
    blacklistError.value = err.message || 'Failed to load blocked users';
  } finally {
    loadingBlacklist.value = false;
  }
}

async function updateSettings() {
  updatingSettings.value = true;
  updateSuccess.value = false;
  updateError.value = null;
  
  try {
    await userStore.updatePrivacySettings(settings.value);
    updateSuccess.value = true;
    
    // Hide success message after 3 seconds
    setTimeout(() => {
      updateSuccess.value = false;
    }, 3000);
  } catch (err) {
    updateError.value = err.message || 'Failed to update settings';
  } finally {
    updatingSettings.value = false;
  }
}

async function unblockUser(user) {
  unblockingUser.value = user._id;
  
  try {
    await userStore.removeFromBlacklist(user._id);
  } catch (err) {
    console.error('Failed to unblock user:', err);
  } finally {
    unblockingUser.value = null;
  }
}

function searchUsers() {
  // Clear existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  // Set new timeout for debounced search
  searchTimeout.value = setTimeout(async () => {
    if (userSearchQuery.value.trim().length >= 2) {
      searchingUsers.value = true;
      userSearchError.value = null;
      
      try {
        const results = await discoveryStore.searchUsers(userSearchQuery.value);
        // Filter out already blacklisted users
        userSearchResults.value = results.filter(user => 
          !blacklistedUsers.value.some(blocked => blocked._id === user._id)
        );
      } catch (err) {
        userSearchError.value = err.message || 'Failed to search users';
      } finally {
        searchingUsers.value = false;
      }
    } else {
      userSearchResults.value = [];
    }
  }, 300);
}

async function blockUser(user) {
  try {
    await userStore.blacklistUser(user._id);
    closeAddUserModal();
  } catch (err) {
    console.error('Failed to block user:', err);
  }
}

function closeAddUserModal() {
  showAddUserModal.value = false;
  userSearchQuery.value = '';
  userSearchResults.value = [];
  userSearchError.value = null;
}

// Lifecycle hooks
onMounted(() => {
  fetchPrivacySettings();
  fetchBlacklistedUsers();
});
</script>

<style scoped>
.privacy-settings {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.settings-header {
  margin-bottom: 2rem;
}

.settings-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: #333;
}

.settings-description {
  margin: 0;
  color: #777;
  font-size: 1rem;
}

.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.section-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-user-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.add-user-btn:hover {
  background-color: #c0392b;
}

.settings-form, .blacklist-content {
  padding: 1rem;
}

.loading-container, .error-container, .empty-blacklist, .empty-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-container.small, .error-container.small {
  padding: 1rem;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 2.5rem;
  color: #95a5a6;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.form-groups {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: bold;
  color: #333;
}

.form-group select {
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.form-help {
  font-size: 0.9rem;
  color: #777;
  margin-top: 0.25rem;
}

.updating-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3498db;
  font-size: 0.9rem;
}

.success-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #2ecc71;
  font-size: 0.9rem;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
}

.blacklist-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.blacklist-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #eee;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.user-avatar.small {
  width: 36px;
  height: 36px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.username {
  font-weight: bold;
  color: #333;
}

.premium-badge, .premium-indicator {
  font-size: 0.8rem;
  color: #f39c12;
}

.unblock-btn {
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unblock-btn:hover {
  background-color: #27ae60;
}

.unblock-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

.user-search {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.user-search label {
  font-weight: bold;
}

.search-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.user-results {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.user-result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.user-result-item:hover {
  background-color: #f9f9f9;
}

.user-result-item:last-child {
  border-bottom: none;
}

.user-result-item .user-info {
  flex: 1;
}

.block-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.block-btn:hover {
  background-color: #c0392b;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .blacklist-item {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .user-info {
    justify-content: center;
  }
}
</style>
