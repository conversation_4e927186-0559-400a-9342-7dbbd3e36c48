<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { useToastStore } from '../../stores/toast';
import Breadcrumb from '../../components/ui/Breadcrumb.vue';

const router = useRouter();
const authStore = useAuthStore();
const toastStore = useToastStore();

// Reactive data
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const success = ref(false);

// Form data
const formData = ref({
  displayName: '',
  bio: '',
  profilePicture: ''
});

// Computed
const currentUser = computed(() => authStore.user);

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Validation
const isFormValid = computed(() => {
  return formData.value.displayName.trim().length > 0;
});

// Initialize form with current user data
const initializeForm = () => {
  if (currentUser.value) {
    formData.value = {
      displayName: currentUser.value.displayName || currentUser.value.username || '',
      bio: currentUser.value.bio || '',
      profilePicture: currentUser.value.profilePicture || ''
    };
  }
};

// Save profile changes
const saveProfile = async () => {
  if (!isFormValid.value || saving.value) return;

  try {
    saving.value = true;
    error.value = null;
    success.value = false;

    const response = await fetch('/.netlify/functions/user/profile', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        displayName: formData.value.displayName.trim(),
        bio: formData.value.bio.trim(),
        profilePicture: formData.value.profilePicture.trim() || null
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to update profile');
    }

    // Update the auth store with new user data
    if (data.data && data.data.user) {
      // Update the user in the auth store directly with the returned data
      authStore.user = data.data.user;
    } else {
      // Fallback to fetching current user
      await authStore.fetchCurrentUser();
    }

    success.value = true;

    // Show success toast
    toastStore.success('Profile updated successfully!');

    // Redirect to profile page after a short delay
    setTimeout(() => {
      router.push(`/profile/${authStore.user.username}`);
    }, 1500);

  } catch (err) {
    console.error('Error updating profile:', err);
    error.value = err.message || 'Failed to update profile. Please try again.';

    // Show error toast
    toastStore.error(err.message || 'Failed to update profile. Please try again.');
  } finally {
    saving.value = false;
  }
};

// Cancel editing
const cancelEdit = () => {
  if (authStore.user?.username) {
    router.push(`/profile/${authStore.user.username}`);
  } else {
    router.back();
  }
};

onMounted(() => {
  initializeForm();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="cancelEdit" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Edit Profile</h1>
        </div>
        <button
          @click="saveProfile"
          :disabled="!isFormValid || saving"
          :class="[
            'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
            isFormValid && !saving
              ? 'bg-neon-blue text-dark-bg hover:bg-neon-blue/80'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          ]"
        >
          {{ saving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Breadcrumb -->
      <Breadcrumb :custom-breadcrumbs="[
        { name: 'Home', path: '/' },
        { name: 'My Profile', path: '/profile' },
        { name: 'Edit Profile', path: '/edit-profile', isLast: true }
      ]" />

      <!-- Not Authenticated -->
      <div v-if="!currentUser" class="bg-yellow-900/20 border border-yellow-500 rounded-xl p-6 text-center">
        <p class="text-yellow-400">Please log in to edit your profile.</p>
        <router-link to="/auth" class="mt-4 inline-block px-4 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80">
          Log In
        </router-link>
      </div>

      <!-- Edit Form -->
      <div v-else class="space-y-6">
        <!-- Success Message -->
        <div v-if="success" class="bg-green-900/20 border border-green-500 rounded-xl p-4 text-center">
          <p class="text-green-400">Profile updated successfully! Redirecting...</p>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="bg-red-900/20 border border-red-500 rounded-xl p-4 text-center">
          <p class="text-red-400">{{ error }}</p>
        </div>

        <!-- Profile Picture Preview -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Profile Picture</h2>
          <div class="flex items-center gap-6">
            <div class="w-24 h-24 rounded-full overflow-hidden flex-shrink-0">
              <img
                :src="formData.profilePicture || getDefaultAvatar()"
                :alt="currentUser.username"
                class="w-full h-full object-cover"
              />
            </div>
            <div class="flex-1">
              <label for="profilePicture" class="block text-sm font-medium mb-2">Profile Picture URL</label>
              <input
                id="profilePicture"
                v-model="formData.profilePicture"
                type="url"
                placeholder="https://example.com/your-image.jpg"
                class="w-full px-3 py-2 bg-dark-bg border border-gray-700 rounded-lg focus:outline-none focus:border-neon-blue"
              />
              <p class="text-xs text-gray-400 mt-1">Enter a URL to your profile picture or leave empty for default</p>
            </div>
          </div>
        </div>

        <!-- Basic Information -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
          <div class="space-y-4">
            <!-- Display Name -->
            <div>
              <label for="displayName" class="block text-sm font-medium mb-2">
                Display Name <span class="text-red-400">*</span>
              </label>
              <input
                id="displayName"
                v-model="formData.displayName"
                type="text"
                maxlength="50"
                placeholder="Your display name"
                class="w-full px-3 py-2 bg-dark-bg border border-gray-700 rounded-lg focus:outline-none focus:border-neon-blue"
                required
              />
              <p class="text-xs text-gray-400 mt-1">{{ formData.displayName.length }}/50 characters</p>
            </div>

            <!-- Username (Read-only) -->
            <div>
              <label for="username" class="block text-sm font-medium mb-2">Username</label>
              <input
                id="username"
                :value="currentUser.username"
                type="text"
                readonly
                class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-400 cursor-not-allowed"
              />
              <p class="text-xs text-gray-400 mt-1">Username cannot be changed</p>
            </div>

            <!-- Bio -->
            <div>
              <label for="bio" class="block text-sm font-medium mb-2">Bio</label>
              <textarea
                id="bio"
                v-model="formData.bio"
                rows="3"
                maxlength="160"
                placeholder="Tell us about yourself..."
                class="w-full px-3 py-2 bg-dark-bg border border-gray-700 rounded-lg focus:outline-none focus:border-neon-blue resize-none"
              ></textarea>
              <p class="text-xs text-gray-400 mt-1">{{ formData.bio.length }}/160 characters</p>
            </div>
          </div>
        </div>

        <!-- Account Information -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Account Information</h2>
          <div class="space-y-4">
            <!-- Email (Read-only) -->
            <div>
              <label for="email" class="block text-sm font-medium mb-2">Email</label>
              <input
                id="email"
                :value="currentUser.email"
                type="email"
                readonly
                class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-400 cursor-not-allowed"
              />
              <p class="text-xs text-gray-400 mt-1">Email cannot be changed here</p>
            </div>

            <!-- Premium Status (Read-only) -->
            <div>
              <label class="block text-sm font-medium mb-2">Account Type</label>
              <div class="flex items-center gap-2">
                <span :class="[
                  'px-3 py-1 rounded-full text-sm font-medium',
                  currentUser.isPremium
                    ? 'bg-neon-blue/20 text-neon-blue'
                    : 'bg-gray-700 text-gray-300'
                ]">
                  {{ currentUser.isPremium ? 'NTA+ Premium' : 'Free Account' }}
                </span>
                <span v-if="currentUser.isVerified" class="px-3 py-1 rounded-full text-sm font-medium bg-green-900/20 text-green-400">
                  Verified ✓
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-4">
          <button
            @click="cancelEdit"
            class="flex-1 px-4 py-2 border border-gray-700 text-gray-300 rounded-lg hover:border-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveProfile"
            :disabled="!isFormValid || saving"
            :class="[
              'flex-1 px-4 py-2 rounded-lg font-medium transition-colors',
              isFormValid && !saving
                ? 'bg-neon-blue text-dark-bg hover:bg-neon-blue/80'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            ]"
          >
            {{ saving ? 'Saving Changes...' : 'Save Changes' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
