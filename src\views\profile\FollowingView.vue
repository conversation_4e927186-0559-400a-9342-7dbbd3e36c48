<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// Get username from route params
const username = route.params.username;

// Reactive data
const loading = ref(true);
const error = ref(null);
const following = ref([]);
const searchQuery = ref('');
const page = ref(1);
const hasMore = ref(false);
const total = ref(0);
const followingUsers = ref(false);
const profileUser = ref(null);

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Computed
const filteredFollowing = computed(() => {
  if (!searchQuery.value) return following.value;
  
  return following.value.filter(user =>
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.displayName?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.bio?.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const isOwnProfile = computed(() => {
  return authStore.user?.username === username;
});

// Methods
const fetchFollowing = async (resetList = false) => {
  try {
    if (resetList) {
      loading.value = true;
      page.value = 1;
      following.value = [];
    }

    error.value = null;

    const params = new URLSearchParams({
      page: page.value.toString(),
      limit: '20'
    });

    if (searchQuery.value) {
      params.append('search', searchQuery.value);
    }

    const response = await fetch(`/.netlify/functions/user/following/${username}?${params}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch following');
    }

    if (resetList) {
      following.value = data.data.following;
    } else {
      following.value.push(...data.data.following);
    }

    total.value = data.data.total;
    hasMore.value = data.data.hasMore;

  } catch (err) {
    console.error('Error fetching following:', err);
    error.value = err.message;
  } finally {
    loading.value = false;
  }
};

const fetchProfileUser = async () => {
  try {
    const response = await fetch(`/.netlify/functions/user/profile/${username}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (response.ok) {
      profileUser.value = data.data.user;
    }
  } catch (err) {
    console.error('Error fetching profile user:', err);
  }
};

const loadMore = () => {
  if (!hasMore.value || loading.value) return;
  
  page.value++;
  fetchFollowing(false);
};

const followUser = async (userId) => {
  if (!authStore.isAuthenticated || followingUsers.value) return;

  followingUsers.value = true;

  try {
    const response = await fetch('/.netlify/functions/user/follow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ targetUserId: userId })
    });

    if (response.ok) {
      // Update the user's follow status in the list
      const userIndex = following.value.findIndex(user => user._id === userId);
      if (userIndex !== -1) {
        following.value[userIndex].isFollowing = true;
        following.value[userIndex].followersCount += 1;
      }
    }
  } catch (err) {
    console.error('Error following user:', err);
  } finally {
    followingUsers.value = false;
  }
};

const unfollowUser = async (userId) => {
  if (!authStore.isAuthenticated || followingUsers.value) return;

  followingUsers.value = true;

  try {
    const response = await fetch('/.netlify/functions/user/unfollow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ targetUserId: userId })
    });

    if (response.ok) {
      // Update the user's follow status in the list
      const userIndex = following.value.findIndex(user => user._id === userId);
      if (userIndex !== -1) {
        following.value[userIndex].isFollowing = false;
        following.value[userIndex].followersCount -= 1;
      }
    }
  } catch (err) {
    console.error('Error unfollowing user:', err);
  } finally {
    followingUsers.value = false;
  }
};

const viewProfile = (targetUsername) => {
  router.push(`/profile/${targetUsername}`);
};

const startConversation = (targetUsername) => {
  router.push(`/messages/new?user=${targetUsername}`);
};

const searchFollowing = () => {
  fetchFollowing(true);
};

// Lifecycle
onMounted(() => {
  fetchProfileUser();
  fetchFollowing(true);
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div>
            <h1 class="text-lg font-medium">Following</h1>
            <p class="text-sm text-gray-400">
              {{ profileUser?.displayName || `@${username}` }}
              <span v-if="total > 0" class="ml-1">({{ total }})</span>
            </p>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Search Bar -->
      <div class="mb-6">
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            v-model="searchQuery"
            @input="searchFollowing"
            type="text"
            placeholder="Search following..."
            class="w-full bg-darker-bg border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors"
          />
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading && following.length === 0" class="space-y-4">
        <div v-for="i in 5" :key="i" class="bg-darker-bg rounded-xl border border-gray-800 p-6 animate-pulse">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gray-700 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-700 rounded w-1/4"></div>
              <div class="h-3 bg-gray-700 rounded w-1/2"></div>
              <div class="h-3 bg-gray-700 rounded w-3/4"></div>
            </div>
            <div class="h-8 bg-gray-700 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500/30 rounded-xl p-6 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-lg font-medium text-red-400 mb-2">{{ error }}</h3>
        <button
          @click="fetchFollowing(true)"
          class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredFollowing.length === 0" class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-300 mb-2">
          {{ searchQuery ? 'No users found' : `${isOwnProfile ? 'You are' : `@${username} is`} not following anyone yet` }}
        </h3>
        <p class="text-gray-400">
          {{ searchQuery ? 'Try a different search term' : 'Following will appear here when you start following people.' }}
        </p>
      </div>

      <!-- Following List -->
      <div v-else class="space-y-4">
        <div
          v-for="user in filteredFollowing"
          :key="user._id"
          class="bg-darker-bg rounded-xl border border-gray-800 p-6 hover:border-gray-700 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <!-- Profile Picture -->
              <div class="relative cursor-pointer" @click="viewProfile(user.username)">
                <img
                  :src="user.profilePicture || getDefaultAvatar()"
                  :alt="user.username"
                  class="w-16 h-16 rounded-full object-cover"
                />
                <div v-if="user.isPremium" class="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-dark-bg" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              </div>

              <!-- User Info -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <h3 class="font-medium text-white truncate cursor-pointer hover:text-primary transition-colors" @click="viewProfile(user.username)">
                    {{ user.displayName }}
                  </h3>
                  <span v-if="user.isPremium" class="text-xs text-primary">NTA+</span>
                  <span v-if="user.isVerified" class="text-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </span>
                </div>
                <p class="text-gray-400 text-sm cursor-pointer hover:text-gray-300 transition-colors" @click="viewProfile(user.username)">@{{ user.username }}</p>
                <p v-if="user.bio" class="text-gray-300 text-sm mt-1 line-clamp-2">{{ user.bio }}</p>
                <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                  <span>{{ user.followersCount }} followers</span>
                  <span>{{ user.followingCount }} following</span>
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div v-if="!isOwnProfile && user.username !== authStore.user?.username" class="flex items-center space-x-2">
              <button
                @click="startConversation(user.username)"
                class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-colors"
                title="Send Message"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </button>
              
              <button
                v-if="!user.isFollowing"
                @click="followUser(user._id)"
                :disabled="followingUsers"
                class="px-4 py-2 bg-primary text-dark-bg rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors disabled:opacity-50"
              >
                Follow
              </button>
              
              <button
                v-else
                @click="unfollowUser(user._id)"
                :disabled="followingUsers"
                class="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
              >
                Following
              </button>
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore" class="text-center pt-6">
          <button
            @click="loadMore"
            :disabled="loading"
            class="bg-primary text-dark-bg px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors disabled:opacity-50"
          >
            <span v-if="loading">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
            <span v-else>Load More</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
