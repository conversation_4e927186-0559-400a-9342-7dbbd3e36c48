import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/Home.vue')
    },
    {
      path: '/auth',
      name: 'auth',
      component: () => import('../views/auth/AuthPage.vue'),
      meta: { guestOnly: true }
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/auth/Login.vue'),
      meta: { guestOnly: true }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/auth/Register.vue'),
      meta: { guestOnly: true }
    },
    {
      path: '/feed',
      name: 'feed',
      component: () => import('../views/feed/Feed.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/post/:id',
      name: 'post-detail',
      component: () => import('../views/feed/PostDetail.vue')
    },
    {
      path: '/create',
      name: 'create-content',
      component: () => import('../views/CreateContentView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/create-post',
      name: 'create-post',
      redirect: '/create'
    },
    {
      path: '/create-story',
      name: 'create-story',
      redirect: '/create'
    },
    {
      path: '/stories',
      name: 'stories',
      component: () => import('../views/stories/Stories.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/messages',
      name: 'messages',
      component: () => import('../views/messages/Messages.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/messages/new-group',
      name: 'new-group',
      component: () => import('../views/messages/NewGroup.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/messages/:id',
      name: 'conversation',
      component: () => import('../views/messages/Conversation.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile',
      name: 'my-profile',
      component: () => import('../views/profile/MyProfile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/profile/:username',
      name: 'user-profile',
      component: () => import('../views/profile/UserProfile.vue')
    },
    {
      path: '/edit-profile',
      name: 'edit-profile',
      component: () => import('../views/profile/EditProfile.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/emojis',
      name: 'emojis',
      component: () => import('../views/emojis/Emojis.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/create-emoji',
      name: 'create-emoji',
      component: () => import('../views/emojis/CreateEmoji.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/discover',
      name: 'discover',
      component: () => import('../views/discover/Discover.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/search',
      name: 'search',
      component: () => import('../views/discover/Search.vue')
    },
    {
      path: '/ai-assistant',
      name: 'ai-assistant',
      component: () => import('../views/ai/AIAssistant.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/subscription',
      name: 'subscription',
      component: () => import('../views/subscription/Subscription.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/settings/Settings.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/privacy',
      name: 'privacy',
      component: () => import('../views/settings/Privacy.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin',
      name: 'admin-dashboard',
      component: () => import('../views/admin/AdminDashboard.vue'),
      meta: { requiresAuth: true, requiresAdmin: true }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('../views/NotFound.vue')
    }
  ]
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)
  const guestOnly = to.matched.some(record => record.meta.guestOnly)

  // If user has a token but no user data, try to fetch current user
  if (authStore.token && !authStore.user && !authStore.loading) {
    try {
      await authStore.fetchCurrentUser()
    } catch (error) {
      console.error('Failed to fetch current user:', error)
      authStore.logout()
    }
  }

  if (requiresAuth && !authStore.isAuthenticated) {
    // Redirect to auth page if trying to access protected route while not authenticated
    next({ name: 'auth', query: { redirect: to.fullPath } })
  } else if (requiresAdmin && (!authStore.isAuthenticated || !authStore.user?.isAdmin)) {
    // Redirect to feed if trying to access admin route without admin privileges
    next({ name: 'feed' })
  } else if (guestOnly && authStore.isAuthenticated) {
    // Redirect to feed if trying to access guest-only route while authenticated
    next({ name: 'feed' })
  } else {
    next()
  }
})

export default router
