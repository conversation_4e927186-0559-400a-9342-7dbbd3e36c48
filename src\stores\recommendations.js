import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useRecommendationsStore = defineStore('recommendations', () => {
  // State
  const recommendedPosts = ref([]);
  const trendingPosts = ref([]);
  const similarPosts = ref([]);
  const recommendedHashtags = ref([]);
  const userAnalytics = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const lastFeedGeneration = ref(null);

  // Getters
  const allRecommendedPosts = computed(() => recommendedPosts.value);
  const allTrendingPosts = computed(() => trendingPosts.value);
  const allSimilarPosts = computed(() => similarPosts.value);
  const allRecommendedHashtags = computed(() => recommendedHashtags.value);
  const getUserAnalytics = computed(() => userAnalytics.value);

  // Actions

  /**
   * Generate personalized feed using ML recommendations
   */
  async function generatePersonalizedFeed(options = {}) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'hybrid',
        exploration = true
      } = options;

      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        exploration: exploration.toString()
      });

      const response = await fetch(`/.netlify/functions/recommendation/feed?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate personalized feed');
      }

      // Update recommended posts
      if (page === 1) {
        recommendedPosts.value = data.data.posts;
      } else {
        recommendedPosts.value = [...recommendedPosts.value, ...data.data.posts];
      }

      lastFeedGeneration.value = {
        timestamp: new Date(),
        algorithm: data.data.metadata.algorithm,
        totalCandidates: data.data.metadata.totalCandidates,
        page,
        hasMore: data.data.hasMore
      };

      return {
        posts: data.data.posts,
        hasMore: data.data.hasMore,
        metadata: data.data.metadata
      };

    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Track user interaction with posts
   */
  async function trackInteraction(postId, type, metadata = {}) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      await fetch('/.netlify/functions/recommendation/track', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          postId,
          type,
          metadata: {
            ...metadata,
            timestamp: new Date(),
            userAgent: navigator.userAgent
          }
        })
      });
    } catch (err) {
      console.error('Failed to track interaction:', err);
      // Don't throw error to avoid disrupting user experience
    }
  }

  /**
   * Get trending posts
   */
  async function fetchTrendingPosts(timeframe = '24h', limit = 20) {
    loading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        timeframe,
        limit: limit.toString()
      });

      const response = await fetch(`/.netlify/functions/recommendation/trending?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch trending posts');
      }

      trendingPosts.value = data.data.posts;
      return data.data.posts;

    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get similar posts based on content
   */
  async function fetchSimilarPosts(postId, limit = 10) {
    loading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        postId,
        limit: limit.toString()
      });

      const response = await fetch(`/.netlify/functions/recommendation/similar?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch similar posts');
      }

      similarPosts.value = data.data.posts;
      return data.data.posts;

    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get personalized hashtag recommendations
   */
  async function fetchHashtagRecommendations(limit = 10) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        limit: limit.toString()
      });

      const response = await fetch(`/.netlify/functions/recommendation/hashtags?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch hashtag recommendations');
      }

      recommendedHashtags.value = data.data.hashtags;
      return data.data.hashtags;

    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Get user analytics and interaction summary
   */
  async function fetchUserAnalytics(timeframe = '7d') {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const queryParams = new URLSearchParams({
        timeframe
      });

      const response = await fetch(`/.netlify/functions/recommendation/analytics?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch user analytics');
      }

      userAnalytics.value = data.data;
      return data.data;

    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Track post view with duration
   */
  async function trackView(postId, duration = null) {
    return trackInteraction(postId, 'view', { duration });
  }

  /**
   * Track post like
   */
  async function trackLike(postId) {
    return trackInteraction(postId, 'like');
  }

  /**
   * Track post unlike
   */
  async function trackUnlike(postId) {
    return trackInteraction(postId, 'unlike');
  }

  /**
   * Track comment
   */
  async function trackComment(postId, commentId) {
    return trackInteraction(postId, 'comment', { commentId });
  }

  /**
   * Track share
   */
  async function trackShare(postId, shareMethod = 'unknown') {
    return trackInteraction(postId, 'share', { shareMethod });
  }

  /**
   * Track save/bookmark
   */
  async function trackSave(postId) {
    return trackInteraction(postId, 'save');
  }

  /**
   * Track click on post elements
   */
  async function trackClick(postId, element) {
    return trackInteraction(postId, 'click', { element });
  }

  /**
   * Clear all recommendations (useful for logout)
   */
  function clearRecommendations() {
    recommendedPosts.value = [];
    trendingPosts.value = [];
    similarPosts.value = [];
    recommendedHashtags.value = [];
    userAnalytics.value = null;
    lastFeedGeneration.value = null;
    error.value = null;
  }

  return {
    // State
    recommendedPosts,
    trendingPosts,
    similarPosts,
    recommendedHashtags,
    userAnalytics,
    loading,
    error,
    lastFeedGeneration,

    // Getters
    allRecommendedPosts,
    allTrendingPosts,
    allSimilarPosts,
    allRecommendedHashtags,
    getUserAnalytics,

    // Actions
    generatePersonalizedFeed,
    trackInteraction,
    fetchTrendingPosts,
    fetchSimilarPosts,
    fetchHashtagRecommendations,
    fetchUserAnalytics,
    trackView,
    trackLike,
    trackUnlike,
    trackComment,
    trackShare,
    trackSave,
    trackClick,
    clearRecommendations
  };
});
