<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient192" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow192" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="96" cy="96" r="92" fill="url(#bgGradient192)" filter="url(#shadow192)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(96, 96)">
    <!-- Central N -->
    <text x="0" y="19.200000000000003" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="76.80000000000001" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-38.400000000000006" y1="-19.200000000000003" x2="38.400000000000006" y2="19.200000000000003" 
          stroke="#0a0a0a" stroke-width="3.84" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-28.799999999999997" cy="28.799999999999997" r="3.84" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="28.799999999999997" cy="28.799999999999997" r="3.84" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="38.400000000000006" r="2.88" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>