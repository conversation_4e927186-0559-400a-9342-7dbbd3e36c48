<script setup>
import { useToastStore } from '../../stores/toast';

const toastStore = useToastStore();

const getToastIcon = (type) => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };
  return icons[type] || 'ℹ️';
};

const getToastClasses = (type) => {
  const baseClasses = 'toast-item flex items-start p-4 rounded-lg shadow-lg border backdrop-blur-sm';
  const typeClasses = {
    success: 'bg-green-900/80 border-green-500 text-green-100',
    error: 'bg-red-900/80 border-red-500 text-red-100',
    warning: 'bg-yellow-900/80 border-yellow-500 text-yellow-100',
    info: 'bg-blue-900/80 border-blue-500 text-blue-100'
  };
  return `${baseClasses} ${typeClasses[type] || typeClasses.info}`;
};
</script>

<template>
  <div class="toast-container fixed top-4 right-4 z-50 space-y-2">
    <transition-group name="toast" tag="div">
      <div
        v-for="toast in toastStore.toasts"
        :key="toast.id"
        :class="getToastClasses(toast.type)"
      >
        <div class="flex-shrink-0 mr-3 text-lg">
          {{ getToastIcon(toast.type) }}
        </div>
        
        <div class="flex-1 min-w-0">
          <h4 v-if="toast.title" class="font-medium text-sm mb-1">
            {{ toast.title }}
          </h4>
          <p class="text-sm">
            {{ toast.message }}
          </p>
          
          <!-- Actions -->
          <div v-if="toast.actions && toast.actions.length > 0" class="mt-2 flex space-x-2">
            <button
              v-for="action in toast.actions"
              :key="action.label"
              @click="action.handler"
              class="text-xs px-2 py-1 rounded bg-white/20 hover:bg-white/30 transition-colors"
            >
              {{ action.label }}
            </button>
          </div>
        </div>
        
        <!-- Close button -->
        <button
          @click="toastStore.removeToast(toast.id)"
          class="flex-shrink-0 ml-3 text-white/60 hover:text-white transition-colors"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </transition-group>
  </div>
</template>

<style scoped>
.toast-container {
  max-width: 400px;
  width: 100%;
}

.toast-item {
  max-width: 100%;
  word-wrap: break-word;
}

/* Toast animations */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toast-move {
  transition: transform 0.3s ease;
}

@media (max-width: 640px) {
  .toast-container {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
  
  .toast-enter-from,
  .toast-leave-to {
    transform: translateY(-100%);
  }
}
</style>
