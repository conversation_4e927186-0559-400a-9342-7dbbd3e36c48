<template>
  <div class="emoji-studio">
    <div class="studio-header">
      <h2>Emoji Studio</h2>
      <div class="header-actions">
        <button @click="showCreateModal = true" class="create-emoji-btn" :disabled="hasReachedCreationLimit">
          <i class="fas fa-plus"></i> Create Emoji
        </button>
        <button @click="fetchAnalytics" class="analytics-btn">
          <i class="fas fa-chart-bar"></i> Analytics
        </button>
      </div>
    </div>

    <div class="studio-tabs">
      <button 
        @click="activeTab = 'my-emojis'" 
        class="tab-btn"
        :class="{ active: activeTab === 'my-emojis' }"
      >
        My Emojis ({{ userEmojis.length }}/{{ emojiLimits.creation === Infinity ? '∞' : emojiLimits.creation }})
      </button>
      <button 
        @click="activeTab = 'favorites'" 
        class="tab-btn"
        :class="{ active: activeTab === 'favorites' }"
      >
        Favorites ({{ favoriteEmojis.length }}/{{ emojiLimits.favorites === Infinity ? '∞' : emojiLimits.favorites }})
      </button>
    </div>

    <div class="studio-content">
      <!-- My Emojis Tab -->
      <div v-if="activeTab === 'my-emojis'" class="emojis-grid">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading your emojis...</p>
        </div>
        
        <div v-else-if="error" class="error-container">
          <p>{{ error }}</p>
          <button @click="fetchUserEmojis" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="userEmojis.length === 0" class="empty-emojis">
          <div class="empty-content">
            <i class="fas fa-smile empty-icon"></i>
            <h3>No custom emojis yet</h3>
            <p>Create your first custom emoji to express yourself uniquely!</p>
            <button @click="showCreateModal = true" class="create-emoji-btn">Create Your First Emoji</button>
          </div>
        </div>
        
        <div v-else class="emoji-list">
          <div v-for="emoji in userEmojis" :key="emoji._id" class="emoji-item">
            <div class="emoji-preview">
              <img :src="emoji.url" :alt="emoji.name" class="emoji-image" />
              <div v-if="emoji.isAnimated" class="animated-badge">GIF</div>
            </div>
            <div class="emoji-info">
              <div class="emoji-name">:{{ emoji.name }}:</div>
              <div class="emoji-stats">
                <span class="usage-count">{{ emoji.usageCount || 0 }} uses</span>
              </div>
            </div>
            <div class="emoji-actions">
              <button @click="copyEmojiCode(emoji)" class="action-btn copy-btn" title="Copy code">
                <i class="fas fa-copy"></i>
              </button>
              <button @click="deleteEmoji(emoji)" class="action-btn delete-btn" title="Delete">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Favorites Tab -->
      <div v-if="activeTab === 'favorites'" class="emojis-grid">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading favorite emojis...</p>
        </div>
        
        <div v-else-if="error" class="error-container">
          <p>{{ error }}</p>
          <button @click="fetchFavoriteEmojis" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="favoriteEmojis.length === 0" class="empty-emojis">
          <div class="empty-content">
            <i class="fas fa-heart empty-icon"></i>
            <h3>No favorite emojis yet</h3>
            <p>Add emojis to your favorites to access them quickly!</p>
          </div>
        </div>
        
        <div v-else class="emoji-list">
          <div v-for="emoji in favoriteEmojis" :key="emoji._id" class="emoji-item">
            <div class="emoji-preview">
              <img :src="emoji.url" :alt="emoji.name" class="emoji-image" />
              <div v-if="emoji.isAnimated" class="animated-badge">GIF</div>
            </div>
            <div class="emoji-info">
              <div class="emoji-name">:{{ emoji.name }}:</div>
              <div class="emoji-creator">by {{ emoji.creator?.username || 'Unknown' }}</div>
            </div>
            <div class="emoji-actions">
              <button @click="copyEmojiCode(emoji)" class="action-btn copy-btn" title="Copy code">
                <i class="fas fa-copy"></i>
              </button>
              <button @click="removeFromFavorites(emoji)" class="action-btn remove-btn" title="Remove from favorites">
                <i class="fas fa-heart-broken"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Emoji Modal -->
    <div v-if="showCreateModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Create Custom Emoji</h3>
          <button @click="closeCreateModal" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="create-form">
            <div class="form-group">
              <label for="emoji-name">Emoji Name</label>
              <input 
                type="text" 
                id="emoji-name"
                v-model="newEmojiName" 
                placeholder="Enter emoji name (letters, numbers, underscores only)"
                pattern="[a-zA-Z0-9_]+"
                maxlength="20"
              />
              <div class="form-help">
                This will be used as :{{ newEmojiName || 'name' }}:
              </div>
            </div>

            <div class="form-group">
              <label for="emoji-image">Emoji Image</label>
              <div class="image-upload">
                <label for="emoji-image-input" class="upload-label">
                  <div v-if="!newEmojiImage" class="upload-placeholder">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span>Upload Image (PNG, JPG, GIF)</span>
                    <small>Max 2MB, recommended 128x128px</small>
                  </div>
                  <div v-else class="image-preview">
                    <img :src="newEmojiImagePreview" alt="Emoji preview" class="preview-image" />
                  </div>
                </label>
                <input 
                  type="file" 
                  id="emoji-image-input" 
                  accept="image/*" 
                  @change="handleImageUpload" 
                  class="file-input"
                />
              </div>
            </div>

            <div v-if="newEmojiImage && newEmojiImage.type === 'image/gif'" class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="isAnimated" />
                <span class="checkmark"></span>
                This is an animated emoji
                <span v-if="!isPremium" class="premium-required">(NTA+ required)</span>
              </label>
            </div>

            <div v-if="createError" class="error-message">
              {{ createError }}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeCreateModal" class="cancel-btn">Cancel</button>
          <button 
            @click="createEmoji" 
            class="create-btn" 
            :disabled="isCreating || !isCreateFormValid"
          >
            <span v-if="isCreating">
              <i class="fas fa-spinner fa-spin"></i> Creating...
            </span>
            <span v-else>Create Emoji</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Analytics Modal -->
    <div v-if="showAnalyticsModal" class="modal-overlay">
      <div class="modal-content analytics-modal">
        <div class="modal-header">
          <h3>Emoji Analytics</h3>
          <button @click="showAnalyticsModal = false" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div v-if="loadingAnalytics" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading analytics...</p>
          </div>
          
          <div v-else-if="analyticsError" class="error-container">
            <p>{{ analyticsError }}</p>
            <button @click="fetchAnalytics" class="retry-button">Retry</button>
          </div>
          
          <div v-else class="analytics-content">
            <div class="analytics-summary">
              <div class="stat-card">
                <div class="stat-value">{{ analytics.totalEmojis || 0 }}</div>
                <div class="stat-label">Total Emojis</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">{{ analytics.totalUsage || 0 }}</div>
                <div class="stat-label">Total Uses</div>
              </div>
            </div>

            <div v-if="analytics.mostPopular" class="popular-emoji">
              <h4>Most Popular Emoji</h4>
              <div class="emoji-highlight">
                <img :src="analytics.mostPopular.url" :alt="analytics.mostPopular.name" class="emoji-image large" />
                <div class="emoji-details">
                  <div class="emoji-name">:{{ analytics.mostPopular.name }}:</div>
                  <div class="emoji-usage">{{ analytics.mostPopular.usageCount || 0 }} uses</div>
                </div>
              </div>
            </div>

            <div v-if="analytics.byPopularity && analytics.byPopularity.length > 0" class="popularity-ranking">
              <h4>Popularity Ranking</h4>
              <div class="ranking-list">
                <div 
                  v-for="(emoji, index) in analytics.byPopularity.slice(0, 5)" 
                  :key="emoji._id" 
                  class="ranking-item"
                >
                  <div class="rank-number">{{ index + 1 }}</div>
                  <img :src="emoji.url" :alt="emoji.name" class="emoji-image small" />
                  <div class="emoji-info">
                    <div class="emoji-name">:{{ emoji.name }}:</div>
                    <div class="emoji-usage">{{ emoji.usageCount || 0 }} uses</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useEmojiStore } from '@/stores/emoji';
import { useSubscriptionStore } from '@/stores/subscription';

// Stores
const emojiStore = useEmojiStore();
const subscriptionStore = useSubscriptionStore();

// State
const activeTab = ref('my-emojis');
const showCreateModal = ref(false);
const showAnalyticsModal = ref(false);
const newEmojiName = ref('');
const newEmojiImage = ref(null);
const newEmojiImagePreview = ref('');
const isAnimated = ref(false);
const isCreating = ref(false);
const createError = ref(null);
const loadingAnalytics = ref(false);
const analyticsError = ref(null);

// Computed
const userEmojis = computed(() => emojiStore.allUserEmojis);
const favoriteEmojis = computed(() => emojiStore.allFavoriteEmojis);
const loading = computed(() => emojiStore.loading);
const error = computed(() => emojiStore.error);
const emojiLimits = computed(() => emojiStore.emojiLimits);
const hasReachedCreationLimit = computed(() => emojiStore.hasReachedCreationLimit);
const hasReachedFavoritesLimit = computed(() => emojiStore.hasReachedFavoritesLimit);
const analytics = computed(() => emojiStore.emojiAnalytics);
const isPremium = computed(() => subscriptionStore.isPremium);

const isCreateFormValid = computed(() => {
  return newEmojiName.value.trim().length > 0 && 
         newEmojiImage.value && 
         /^[a-zA-Z0-9_]+$/.test(newEmojiName.value) &&
         (!isAnimated.value || isPremium.value);
});

// Methods
async function fetchUserEmojis() {
  try {
    await emojiStore.fetchUserEmojis();
  } catch (err) {
    console.error('Failed to fetch user emojis:', err);
  }
}

async function fetchFavoriteEmojis() {
  try {
    await emojiStore.fetchFavoriteEmojis();
  } catch (err) {
    console.error('Failed to fetch favorite emojis:', err);
  }
}

function handleImageUpload(event) {
  const file = event.target.files[0];
  
  if (!file) return;
  
  // Check file type
  if (!file.type.startsWith('image/')) {
    createError.value = 'Please select an image file';
    return;
  }
  
  // Check file size (2MB limit)
  if (file.size > 2 * 1024 * 1024) {
    createError.value = 'File size should not exceed 2MB';
    return;
  }
  
  // Create preview
  const reader = new FileReader();
  reader.onload = (e) => {
    newEmojiImage.value = file;
    newEmojiImagePreview.value = e.target.result;
    
    // Auto-detect if it's a GIF
    if (file.type === 'image/gif') {
      isAnimated.value = true;
    }
  };
  reader.readAsDataURL(file);
  
  // Clear error
  createError.value = null;
}

async function createEmoji() {
  if (!isCreateFormValid.value || isCreating.value) return;
  
  isCreating.value = true;
  createError.value = null;
  
  try {
    await emojiStore.createEmoji(
      newEmojiName.value,
      newEmojiImagePreview.value,
      isAnimated.value
    );
    
    // Close modal and refresh
    closeCreateModal();
    await fetchUserEmojis();
  } catch (err) {
    createError.value = err.message || 'Failed to create emoji';
  } finally {
    isCreating.value = false;
  }
}

function closeCreateModal() {
  showCreateModal.value = false;
  newEmojiName.value = '';
  newEmojiImage.value = null;
  newEmojiImagePreview.value = '';
  isAnimated.value = false;
  createError.value = null;
}

async function deleteEmoji(emoji) {
  if (!confirm(`Are you sure you want to delete :${emoji.name}:?`)) return;
  
  try {
    await emojiStore.deleteEmoji(emoji._id);
    await fetchUserEmojis();
  } catch (err) {
    console.error('Failed to delete emoji:', err);
  }
}

async function removeFromFavorites(emoji) {
  try {
    await emojiStore.removeFromFavorites(emoji._id);
    await fetchFavoriteEmojis();
  } catch (err) {
    console.error('Failed to remove from favorites:', err);
  }
}

function copyEmojiCode(emoji) {
  const code = `:${emoji.name}:`;
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(code).then(() => {
      // Show success feedback
      alert(`Copied ${code} to clipboard!`);
    });
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = code;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert(`Copied ${code} to clipboard!`);
  }
}

async function fetchAnalytics() {
  loadingAnalytics.value = true;
  analyticsError.value = null;
  showAnalyticsModal.value = true;
  
  try {
    await emojiStore.fetchEmojiAnalytics();
  } catch (err) {
    analyticsError.value = err.message || 'Failed to load analytics';
  } finally {
    loadingAnalytics.value = false;
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchUserEmojis();
  fetchFavoriteEmojis();
});
</script>

<style scoped>
.emoji-studio {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.studio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.studio-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.create-emoji-btn, .analytics-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-emoji-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.create-emoji-btn:not(:disabled):hover, .analytics-btn:hover {
  background-color: #2980b9;
}

.studio-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 1rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 1rem;
}

.tab-btn.active {
  border-bottom-color: #3498db;
  color: #3498db;
}

.tab-btn:hover {
  background-color: #f0f0f0;
}

.studio-content {
  min-height: 400px;
}

.loading-container, .error-container, .empty-emojis {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 3rem;
  color: #95a5a6;
  margin-bottom: 1rem;
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.emoji-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.emoji-item {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.emoji-preview {
  position: relative;
  flex-shrink: 0;
}

.emoji-image {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.emoji-image.large {
  width: 64px;
  height: 64px;
}

.emoji-image.small {
  width: 32px;
  height: 32px;
}

.animated-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #f39c12;
  color: white;
  font-size: 0.6rem;
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
}

.emoji-info {
  flex: 1;
  min-width: 0;
}

.emoji-name {
  font-weight: bold;
  font-family: monospace;
  margin-bottom: 0.25rem;
}

.emoji-stats, .emoji-creator {
  font-size: 0.8rem;
  color: #777;
}

.emoji-actions {
  display: flex;
  gap: 0.25rem;
}

.action-btn {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #777;
}

.action-btn:hover {
  background-color: #f0f0f0;
}

.copy-btn:hover {
  color: #3498db;
}

.delete-btn:hover, .remove-btn:hover {
  color: #e74c3c;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content.analytics-modal {
  max-width: 600px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

.create-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: bold;
}

.form-group input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-help {
  font-size: 0.8rem;
  color: #777;
  font-family: monospace;
}

.image-upload {
  width: 100%;
}

.upload-label {
  display: block;
  cursor: pointer;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  background-color: #f0f0f0;
  border: 2px dashed #ddd;
  border-radius: 8px;
  gap: 0.5rem;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #777;
}

.upload-placeholder small {
  color: #999;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-input {
  display: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.premium-required {
  color: #f39c12;
  font-size: 0.8rem;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #eee;
}

.cancel-btn, .create-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.create-btn {
  background-color: #3498db;
  color: white;
  border: none;
}

.create-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.create-btn:not(:disabled):hover {
  background-color: #2980b9;
}

/* Analytics styles */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.analytics-summary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stat-card {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 0.9rem;
  color: #777;
  margin-top: 0.25rem;
}

.popular-emoji h4, .popularity-ranking h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.emoji-highlight {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
}

.emoji-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.emoji-usage {
  font-size: 0.9rem;
  color: #777;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.rank-number {
  font-weight: bold;
  color: #3498db;
  width: 24px;
  text-align: center;
}
</style>
