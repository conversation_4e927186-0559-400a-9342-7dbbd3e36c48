const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { findOne, updateOne } = require('./utils/db');
const { ObjectId } = require('mongodb');

// Dummy AI tagging function (placeholder for real Kraizer API)
async function generateTags(content, mediaUrls = []) {
  // In a real implementation, this would call an external AI service
  // For now, we'll use a simple keyword-based approach
  
  // Define some common themes/moods/intents
  const themes = [
    { keyword: 'nature', tags: ['nature', 'outdoors', 'environment'] },
    { keyword: 'food', tags: ['food', 'cooking', 'recipe', 'delicious'] },
    { keyword: 'tech', tags: ['technology', 'gadget', 'innovation', 'digital'] },
    { keyword: 'travel', tags: ['travel', 'adventure', 'explore', 'destination'] },
    { keyword: 'fitness', tags: ['fitness', 'workout', 'health', 'exercise'] },
    { keyword: 'art', tags: ['art', 'creative', 'design', 'artistic'] },
    { keyword: 'music', tags: ['music', 'song', 'artist', 'melody'] },
    { keyword: 'fashion', tags: ['fashion', 'style', 'trend', 'outfit'] },
    { keyword: 'business', tags: ['business', 'entrepreneur', 'startup', 'success'] },
    { keyword: 'education', tags: ['education', 'learning', 'knowledge', 'study'] }
  ];
  
  const moods = [
    { keyword: 'happy', tags: ['happy', 'joy', 'positive', 'cheerful'] },
    { keyword: 'sad', tags: ['sad', 'melancholy', 'emotional', 'reflective'] },
    { keyword: 'excited', tags: ['excited', 'enthusiastic', 'energetic', 'thrilled'] },
    { keyword: 'calm', tags: ['calm', 'peaceful', 'relaxed', 'serene'] },
    { keyword: 'angry', tags: ['angry', 'frustrated', 'upset', 'annoyed'] },
    { keyword: 'inspired', tags: ['inspired', 'motivated', 'uplifting', 'encouraging'] },
    { keyword: 'funny', tags: ['funny', 'humor', 'comedy', 'amusing'] }
  ];
  
  const intents = [
    { keyword: 'question', tags: ['question', 'inquiry', 'asking', 'curious'] },
    { keyword: 'advice', tags: ['advice', 'tip', 'recommendation', 'suggestion'] },
    { keyword: 'announcement', tags: ['announcement', 'news', 'update', 'important'] },
    { keyword: 'opinion', tags: ['opinion', 'perspective', 'viewpoint', 'thought'] },
    { keyword: 'promotion', tags: ['promotion', 'marketing', 'advertising', 'selling'] },
    { keyword: 'sharing', tags: ['sharing', 'experience', 'story', 'moment'] }
  ];
  
  // Normalize content for matching
  const normalizedContent = content ? content.toLowerCase() : '';
  
  // Match content against keywords
  const matchedTags = new Set();
  
  // Check for theme matches
  themes.forEach(theme => {
    if (normalizedContent.includes(theme.keyword)) {
      // Add the main theme tag
      matchedTags.add(theme.keyword);
      
      // Add a random related tag
      const relatedTags = theme.tags.filter(tag => tag !== theme.keyword);
      if (relatedTags.length > 0) {
        const randomRelatedTag = relatedTags[Math.floor(Math.random() * relatedTags.length)];
        matchedTags.add(randomRelatedTag);
      }
    }
  });
  
  // Check for mood matches
  moods.forEach(mood => {
    if (normalizedContent.includes(mood.keyword)) {
      // Add the main mood tag
      matchedTags.add(mood.keyword);
      
      // Add a random related tag
      const relatedTags = mood.tags.filter(tag => tag !== mood.keyword);
      if (relatedTags.length > 0) {
        const randomRelatedTag = relatedTags[Math.floor(Math.random() * relatedTags.length)];
        matchedTags.add(randomRelatedTag);
      }
    }
  });
  
  // Check for intent matches
  intents.forEach(intent => {
    if (normalizedContent.includes(intent.keyword)) {
      // Add the main intent tag
      matchedTags.add(intent.keyword);
      
      // Add a random related tag
      const relatedTags = intent.tags.filter(tag => tag !== intent.keyword);
      if (relatedTags.length > 0) {
        const randomRelatedTag = relatedTags[Math.floor(Math.random() * relatedTags.length)];
        matchedTags.add(randomRelatedTag);
      }
    }
  });
  
  // If no tags were matched, add some default tags
  if (matchedTags.size === 0) {
    // Check if there are media attachments
    if (mediaUrls && mediaUrls.length > 0) {
      // Check media types
      const hasImages = mediaUrls.some(media => media.type === 'image');
      const hasVideos = mediaUrls.some(media => media.type === 'video');
      
      if (hasImages) {
        matchedTags.add('photo');
        matchedTags.add('visual');
      }
      
      if (hasVideos) {
        matchedTags.add('video');
        matchedTags.add('watch');
      }
    } else {
      // Text-only post
      matchedTags.add('thoughts');
      matchedTags.add('status');
    }
  }
  
  // Convert Set to Array
  return Array.from(matchedTags);
}

// Tag a post with AI-generated tags
exports.tagPost = handleAsync(requireAuth(async (event) => {
  try {
    // Parse request body
    const { postId, content, media } = JSON.parse(event.body);
    
    // Validate input
    if (!postId) {
      return error('Post ID is required', 400);
    }
    
    if (!content && (!media || media.length === 0)) {
      return error('Content or media is required for tagging', 400);
    }
    
    // Generate tags using AI (dummy implementation for now)
    const tags = await generateTags(content, media);
    
    // Update post with generated tags
    await updateOne(
      'posts',
      { _id: new ObjectId(postId) },
      { $set: { aiTags: tags } }
    );
    
    // Return the generated tags
    return success({ tags });
  } catch (err) {
    console.error('Error in AI tagging:', err);
    return error('Failed to generate tags', 500);
  }
}));

// Ask Kraizer AI assistant (for @mentions)
exports.askKraizer = handleAsync(requireAuth(async (event) => {
  try {
    // Get user from token
    const userId = event.user._id;
    
    // Get user details to check premium status and request limits
    const user = await findOne('users', { _id: new ObjectId(userId) });
    
    if (!user) {
      return error('User not found', 404);
    }
    
    // Check if user has reached request limit (for non-premium users)
    if (!user.isPremium) {
      // Check if user has requests remaining
      if (user.aiRequestsRemaining <= 0) {
        // Check if reset time has passed
        const now = new Date();
        if (user.aiRequestsResetTime && now < new Date(user.aiRequestsResetTime)) {
          return error('You have reached your hourly limit for AI requests. Upgrade to NTA+ for unlimited requests.', 429);
        }
        
        // Reset the counter if the reset time has passed
        await updateOne(
          'users',
          { _id: new ObjectId(userId) },
          { 
            $set: { 
              aiRequestsRemaining: 3,
              aiRequestsResetTime: new Date(Date.now() + 3600000) // 1 hour from now
            } 
          }
        );
      } else {
        // Decrement the counter
        await updateOne(
          'users',
          { _id: new ObjectId(userId) },
          { $inc: { aiRequestsRemaining: -1 } }
        );
      }
    }
    
    // Parse request body
    const { query, context } = JSON.parse(event.body);
    
    // Validate input
    if (!query) {
      return error('Query is required', 400);
    }
    
    // Generate AI response (dummy implementation for now)
    const response = generateAIResponse(query, context);
    
    // Return the AI response
    return success({ 
      response,
      requestsRemaining: user.isPremium ? 'unlimited' : Math.max(0, user.aiRequestsRemaining - 1),
      resetTime: user.isPremium ? null : user.aiRequestsResetTime
    });
  } catch (err) {
    console.error('Error in AI assistant:', err);
    return error('Failed to generate AI response', 500);
  }
}));

// Dummy AI response generator
function generateAIResponse(query, context = {}) {
  // In a real implementation, this would call an external AI service
  // For now, we'll use a simple template-based approach
  
  // Normalize query
  const normalizedQuery = query.toLowerCase();
  
  // Define some response templates
  const responses = [
    { 
      keywords: ['hello', 'hi', 'hey', 'greetings'],
      templates: [
        "Hello there! How can I help you today?",
        "Hi! I'm Kraizer, your NeTuArk AI assistant. What can I do for you?",
        "Hey! Nice to chat with you. What's on your mind?"
      ]
    },
    {
      keywords: ['help', 'assist', 'support'],
      templates: [
        "I'm here to help! You can ask me about posts, find content, or get recommendations.",
        "Need assistance? I can help with finding content, answering questions, or providing recommendations.",
        "How can I assist you today? I'm good at finding relevant content and answering questions."
      ]
    },
    {
      keywords: ['recommend', 'suggestion', 'what should'],
      templates: [
        "Based on your interests, you might enjoy exploring content about technology and innovation.",
        "I'd recommend checking out the trending posts in the 'creativity' category right now.",
        "You might find some interesting content in the 'travel' section that matches your preferences."
      ]
    },
    {
      keywords: ['weather', 'forecast', 'temperature'],
      templates: [
        "I don't have access to real-time weather data, but I can help you find weather-related content on NeTuArk!",
        "While I can't check the weather for you, there are some great posts about weather photography you might enjoy.",
        "I don't have weather information, but I can help you with other questions about content on NeTuArk."
      ]
    }
  ];
  
  // Try to match query with keywords
  for (const responseType of responses) {
    if (responseType.keywords.some(keyword => normalizedQuery.includes(keyword))) {
      // Return a random template from the matched response type
      const templates = responseType.templates;
      return templates[Math.floor(Math.random() * templates.length)];
    }
  }
  
  // Default responses if no keywords match
  const defaultResponses = [
    "That's an interesting question! While I'm still learning, I'd be happy to help you find content related to that on NeTuArk.",
    "I'm not sure I understand completely, but I can help you discover relevant content on NeTuArk if you provide more details.",
    "Thanks for your message! I'm Kraizer, the NeTuArk AI assistant. I can help you find content, answer questions, or provide recommendations.",
    "I'm processing your request. To help you better, could you provide more specific details about what you're looking for?"
  ];
  
  return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  // Extract the path
  const fullPath = event.path;
  const basePath = '/.netlify/functions/kraizer';
  
  // Check if the path starts with the base path
  if (!fullPath.startsWith(basePath)) {
    return error('Invalid path', 404);
  }
  
  // Extract the route path after the base path
  const path = fullPath.substring(basePath.length);
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/tag-post' && event.httpMethod === 'POST':
      return exports.tagPost(event, context);
    case path === '/ask' && event.httpMethod === 'POST':
      return exports.askKraizer(event, context);
    default:
      return error('Not found', 404);
  }
};
