<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const defaultPostVisibility = ref('public');
const allowFollowersToViewContent = ref(true);
const allowMessagesFromNonFollowers = ref(true);
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Privacy Settings</h1>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Privacy Settings</h2>
        <p class="text-gray-400">This is a placeholder for the Privacy Settings component.</p>
        
        <div class="mt-6 space-y-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Default Post Visibility</label>
            <select 
              v-model="defaultPostVisibility"
              class="w-full px-4 py-2 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
            >
              <option value="public">Public</option>
              <option value="followers">Followers Only</option>
              <option value="private">Private</option>
            </select>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-300">Allow followers to view content</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="allowFollowersToViewContent" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-neon-blue rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm font-medium text-gray-300">Allow messages from non-followers</span>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="allowMessagesFromNonFollowers" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-neon-blue rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>
          
          <button class="w-full neon-button py-2 px-4 mt-6">Save Changes</button>
        </div>
      </div>
    </div>
  </div>
</template>
