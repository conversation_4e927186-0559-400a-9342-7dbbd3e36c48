<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { gsap } from 'gsap';

const router = useRouter();
const notFoundText = ref(null);
const glitchEffect = ref(null);
const particlesContainer = ref(null);
const particles = ref([]);

// Navigate back to home
const goHome = () => {
  router.push('/');
};

// Create particles for background effect
const createParticles = () => {
  if (!particlesContainer.value) return;

  const containerWidth = particlesContainer.value.offsetWidth;
  const containerHeight = particlesContainer.value.offsetHeight;

  // Create 50 particles
  for (let i = 0; i < 50; i++) {
    const particle = document.createElement('div');
    particle.classList.add('absolute', 'rounded-full', 'bg-neon-blue');

    // Random size between 2px and 6px
    const size = Math.random() * 4 + 2;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;

    // Random position
    particle.style.left = `${Math.random() * containerWidth}px`;
    particle.style.top = `${Math.random() * containerHeight}px`;

    // Random opacity
    particle.style.opacity = Math.random() * 0.5 + 0.3;

    // Add to container
    particlesContainer.value.appendChild(particle);
    particles.value.push(particle);

    // Animate particle
    animateParticle(particle, containerWidth, containerHeight);
  }
};

// Animate a single particle
const animateParticle = (particle, containerWidth, containerHeight) => {
  // Random duration between 3 and 8 seconds
  const duration = Math.random() * 5 + 3;

  // Random new position
  const newX = Math.random() * containerWidth;
  const newY = Math.random() * containerHeight;

  gsap.to(particle, {
    x: newX - parseFloat(particle.style.left),
    y: newY - parseFloat(particle.style.top),
    duration,
    ease: 'power1.inOut',
    onComplete: () => {
      // Reset position for next animation
      particle.style.left = `${newX}px`;
      particle.style.top = `${newY}px`;
      particle.style.transform = 'translate(0, 0)';

      // Animate again
      if (particlesContainer.value) {
        animateParticle(particle, containerWidth, containerHeight);
      }
    }
  });
};

// Animate 404 text with glitch effect
const animateNotFoundText = () => {
  if (!notFoundText.value) return;

  // Initial animation
  gsap.fromTo(notFoundText.value,
    { opacity: 0, y: -50 },
    { opacity: 1, y: 0, duration: 1, ease: 'power3.out' }
  );

  // Glitch effect animation
  const glitchTimeline = gsap.timeline({
    repeat: -1,
    repeatDelay: 3
  });

  glitchTimeline.to(notFoundText.value, {
    skewX: 20,
    duration: 0.1,
    ease: 'power1.inOut'
  })
  .to(notFoundText.value, {
    skewX: 0,
    duration: 0.1,
    ease: 'power1.inOut'
  })
  .to(notFoundText.value, {
    opacity: 0.8,
    duration: 0.1
  })
  .to(notFoundText.value, {
    opacity: 1,
    duration: 0.1
  })
  .to(notFoundText.value, {
    x: -10,
    duration: 0.1
  })
  .to(notFoundText.value, {
    x: 0,
    duration: 0.1
  });
};

onMounted(() => {
  createParticles();
  animateNotFoundText();
});
</script>

<template>
  <div class="relative min-h-screen flex flex-col items-center justify-center bg-darker-bg overflow-hidden">
    <!-- Header with logo -->
    <div class="absolute top-0 left-0 right-0 p-4 z-10">
      <div class="max-w-7xl mx-auto">
        <a href="/" class="inline-block">
          <img src="/logo1.png" alt="NeTuArk Logo" class="h-10 md:hidden" />
          <img src="/logo2.png" alt="NeTuArk Logo" class="hidden md:block h-10" />
        </a>
      </div>
    </div>

    <!-- Particles background -->
    <div ref="particlesContainer" class="absolute inset-0 z-0"></div>

    <!-- Main content -->
    <div class="z-10 text-center px-4">
      <!-- 404 Text -->
      <h1
        ref="notFoundText"
        class="text-9xl font-bold mb-8 animate-glow"
        :class="{ 'glitch': glitchEffect }"
      >
        <span class="neon-text-blue">4</span>
        <span class="neon-text-purple">0</span>
        <span class="neon-text-pink">4</span>
      </h1>

      <!-- Message -->
      <p class="text-xl md:text-2xl mb-12 text-gray-300 animate-pulse-slow">
        Lost in the network? Let's get you back.
      </p>

      <!-- Return Home Button -->
      <button
        @click="goHome"
        class="neon-button group"
      >
        Return Home
        <span class="absolute inset-0 bg-neon-blue/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
      </button>
    </div>
  </div>
</template>

<style scoped>
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  left: 2px;
  text-shadow: -2px 0 #ff00f7;
  clip: rect(24px, 550px, 90px, 0);
  animation: glitch-anim-1 2s infinite linear alternate-reverse;
}

.glitch::after {
  left: -2px;
  text-shadow: -2px 0 #00f3ff;
  clip: rect(85px, 550px, 140px, 0);
  animation: glitch-anim-2 2s infinite linear alternate-reverse;
}

@keyframes glitch-anim-1 {
  0% {
    clip: rect(24px, 550px, 90px, 0);
  }
  20% {
    clip: rect(125px, 550px, 140px, 0);
  }
  40% {
    clip: rect(24px, 550px, 90px, 0);
  }
  60% {
    clip: rect(85px, 550px, 140px, 0);
  }
  80% {
    clip: rect(24px, 550px, 90px, 0);
  }
  100% {
    clip: rect(125px, 550px, 140px, 0);
  }
}

@keyframes glitch-anim-2 {
  0% {
    clip: rect(85px, 550px, 140px, 0);
  }
  20% {
    clip: rect(24px, 550px, 90px, 0);
  }
  40% {
    clip: rect(125px, 550px, 140px, 0);
  }
  60% {
    clip: rect(24px, 550px, 90px, 0);
  }
  80% {
    clip: rect(85px, 550px, 140px, 0);
  }
  100% {
    clip: rect(24px, 550px, 90px, 0);
  }
}
</style>
