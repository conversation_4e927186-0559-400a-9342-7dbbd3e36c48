<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// Reactive data
const conversations = ref([]);
const loading = ref(true);
const error = ref(null);
const searchQuery = ref('');

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Computed
const filteredConversations = computed(() => {
  if (!searchQuery.value) return conversations.value;

  return conversations.value.filter(conv =>
    conv.participant.displayName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    conv.participant.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    conv.lastMessage.content.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const totalUnreadCount = computed(() => {
  return conversations.value.reduce((total, conv) => total + conv.unreadCount, 0);
});

// Methods
const fetchConversations = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await fetch('/.netlify/functions/messages/conversations', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch conversations');
    }

    conversations.value = data.data.conversations || [];

  } catch (err) {
    console.error('Error fetching conversations:', err);
    error.value = 'Failed to load conversations. Please try again.';
    conversations.value = [];
  } finally {
    loading.value = false;
  }
};

const openConversation = (conversationId) => {
  router.push(`/messages/${conversationId}`);
};

const formatTime = (timestamp) => {
  const now = new Date();
  const messageTime = new Date(timestamp);
  const diffInHours = (now - messageTime) / (1000 * 60 * 60);

  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now - messageTime) / (1000 * 60));
    return diffInMinutes < 1 ? 'now' : `${diffInMinutes}m`;
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h`;
  } else {
    const diffInDays = Math.floor(diffInHours / 24);
    return diffInDays === 1 ? '1d' : `${diffInDays}d`;
  }
};

const startNewConversation = () => {
  // Navigate to discover page to find users
  router.push('/discover');
};

// Lifecycle
onMounted(() => {
  fetchConversations();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div class="flex items-center gap-2">
            <h1 class="text-lg font-medium">Messages</h1>
            <span v-if="totalUnreadCount > 0" class="bg-primary text-dark-bg text-xs font-bold px-2 py-1 rounded-full">
              {{ totalUnreadCount }}
            </span>
          </div>
        </div>

        <button
          @click="startNewConversation"
          class="bg-primary text-dark-bg px-4 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors"
        >
          New Chat
        </button>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Search Bar -->
      <div class="mb-6">
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search conversations..."
            class="w-full bg-darker-bg border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors"
          />
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="space-y-4">
        <div v-for="i in 3" :key="i" class="bg-darker-bg rounded-xl border border-gray-800 p-4 animate-pulse">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gray-700 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-700 rounded w-1/4"></div>
              <div class="h-3 bg-gray-700 rounded w-3/4"></div>
            </div>
            <div class="h-3 bg-gray-700 rounded w-8"></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500/30 rounded-xl p-6 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-lg font-medium text-red-400 mb-2">{{ error }}</h3>
        <button
          @click="fetchConversations"
          class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>

      <!-- Empty State -->
      <div v-else-if="filteredConversations.length === 0" class="text-center py-12">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        <h3 class="text-lg font-medium text-gray-300 mb-2">
          {{ searchQuery ? 'No conversations found' : 'No messages yet' }}
        </h3>
        <p class="text-gray-400 mb-6">
          {{ searchQuery ? 'Try a different search term' : 'Start a conversation with someone!' }}
        </p>
        <button
          @click="startNewConversation"
          class="bg-primary text-dark-bg px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
        >
          Find People to Chat With
        </button>
      </div>

      <!-- Conversations List -->
      <div v-else class="space-y-2">
        <div
          v-for="conversation in filteredConversations"
          :key="conversation.id"
          @click="openConversation(conversation.id)"
          class="bg-darker-bg rounded-xl border border-gray-800 p-4 hover:border-gray-700 transition-colors cursor-pointer group"
        >
          <div class="flex items-center space-x-4">
            <!-- Profile Picture -->
            <div class="relative">
              <img
                :src="conversation.participant.profilePicture || getDefaultAvatar()"
                :alt="conversation.participant.displayName"
                class="w-12 h-12 rounded-full object-cover"
              />
              <div v-if="conversation.participant.isPremium" class="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-2.5 w-2.5 text-dark-bg" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
            </div>

            <!-- Conversation Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2 mb-1">
                <h3 class="font-medium text-white truncate">{{ conversation.participant.displayName }}</h3>
                <span v-if="conversation.participant.isVerified" class="text-primary">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </span>
                <span class="text-gray-400 text-sm">@{{ conversation.participant.username }}</span>
              </div>
              <p class="text-gray-400 text-sm truncate">{{ conversation.lastMessage.content }}</p>
            </div>

            <!-- Time and Unread -->
            <div class="flex flex-col items-end space-y-1">
              <span class="text-gray-400 text-xs">{{ formatTime(conversation.lastMessage.timestamp) }}</span>
              <div v-if="conversation.unreadCount > 0" class="bg-primary text-dark-bg text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center">
                {{ conversation.unreadCount }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
