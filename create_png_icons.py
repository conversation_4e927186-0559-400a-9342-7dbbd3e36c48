#!/usr/bin/env python3
"""
PNG Icon Generator for NeTuArk PWA
Creates PNG icons from SVG or generates new ones
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import io
import base64

# Icon sizes needed for PWA
ICON_SIZES = [48, 72, 96, 144, 192, 512]

# NeTuArk brand colors
BG_COLOR = '#00d4ff'  # NeTuArk blue
TEXT_COLOR = '#0a0a0a'  # Dark text
ACCENT_COLOR = '#ffffff'  # White accent

def create_netuark_icon(size):
    """Create a NeTuArk branded icon"""
    # Create image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw background circle with gradient effect
    center = size // 2
    radius = size // 2 - 4
    
    # Create a simple gradient by drawing multiple circles
    for i in range(radius, 0, -2):
        alpha = int(255 * (i / radius))
        color = (*hex_to_rgb(BG_COLOR), alpha)
        draw.ellipse([center - i, center - i, center + i, center + i], fill=color)
    
    # Draw the main "N" letter
    try:
        # Try to use a system font
        font_size = size // 3
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
            except:
                font = ImageFont.load_default()
    
    # Draw the "N" letter
    text = "N"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = center - text_width // 2
    text_y = center - text_height // 2 - size // 20
    
    # Draw text shadow
    shadow_offset = max(1, size // 100)
    draw.text((text_x + shadow_offset, text_y + shadow_offset), text, 
              fill=(0, 0, 0, 100), font=font)
    
    # Draw main text
    draw.text((text_x, text_y), text, fill=hex_to_rgb(TEXT_COLOR), font=font)
    
    # Draw connection dots/lines for network effect
    dot_size = max(2, size // 50)
    
    # Small dots around the N
    positions = [
        (center - size//4, center + size//6),
        (center + size//4, center + size//6),
        (center, center + size//4),
        (center - size//6, center - size//6),
        (center + size//6, center - size//6)
    ]
    
    for x, y in positions:
        draw.ellipse([x - dot_size, y - dot_size, x + dot_size, y + dot_size], 
                    fill=hex_to_rgb(TEXT_COLOR))
    
    # Draw connecting lines
    line_width = max(1, size // 100)
    draw.line([center - size//5, center - size//8, center + size//5, center + size//8], 
              fill=hex_to_rgb(TEXT_COLOR), width=line_width)
    
    return img

def hex_to_rgb(hex_color):
    """Convert hex color to RGB tuple"""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def save_icon(img, size, output_dir='public'):
    """Save icon to file"""
    filename = f'icon-{size}.png'
    filepath = os.path.join(output_dir, filename)
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Save the image
    img.save(filepath, 'PNG', optimize=True)
    print(f"✅ Created {filename} ({size}x{size})")
    
    return filepath

def create_all_icons():
    """Create all required PNG icons"""
    print("🎨 Creating NeTuArk PNG icons...")
    print(f"📁 Output directory: public/")
    
    created_files = []
    
    for size in ICON_SIZES:
        try:
            # Create the icon
            icon = create_netuark_icon(size)
            
            # Save the icon
            filepath = save_icon(icon, size)
            created_files.append(filepath)
            
        except Exception as e:
            print(f"❌ Error creating icon {size}x{size}: {e}")
    
    print(f"\n🎉 Successfully created {len(created_files)} PNG icons!")
    print("\n📋 Next steps:")
    print("1. All PNG icons are now in the public/ directory")
    print("2. The manifest.json has been updated to use PNG icons")
    print("3. Deploy to Netlify to fix the PWA manifest errors")
    print("4. Test PWA installation on different devices")
    
    return created_files

def create_favicon():
    """Create a favicon.ico file"""
    try:
        # Create 32x32 icon for favicon
        icon = create_netuark_icon(32)
        
        # Save as ICO
        favicon_path = os.path.join('public', 'favicon.ico')
        icon.save(favicon_path, format='ICO', sizes=[(32, 32)])
        print(f"✅ Created favicon.ico")
        
        return favicon_path
    except Exception as e:
        print(f"❌ Error creating favicon: {e}")
        return None

def main():
    """Main function"""
    print("🚀 NeTuArk PNG Icon Generator")
    print("=" * 40)
    
    # Check if PIL is available
    try:
        from PIL import Image
    except ImportError:
        print("❌ Error: Pillow (PIL) is required but not installed.")
        print("📦 Install it with: pip install Pillow")
        sys.exit(1)
    
    # Create all icons
    created_files = create_all_icons()
    
    # Create favicon
    favicon = create_favicon()
    if favicon:
        created_files.append(favicon)
    
    print(f"\n📊 Summary:")
    print(f"   • Created {len(created_files)} files")
    print(f"   • Icon sizes: {', '.join(f'{s}x{s}' for s in ICON_SIZES)}")
    print(f"   • Format: PNG with transparency")
    print(f"   • Brand colors: {BG_COLOR} (background), {TEXT_COLOR} (text)")
    
    print(f"\n🔧 Files created:")
    for file in created_files:
        print(f"   • {file}")

if __name__ == "__main__":
    main()
