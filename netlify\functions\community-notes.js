const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

/**
 * Community Notes System for NeTuArk
 * Collaborative fact-checking and content verification
 */

// Create a community note
exports.createNote = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const { postId, content, sources = [], category = 'general' } = JSON.parse(event.body);

    // Validate input
    if (!postId || !content) {
      return error('Post ID and content are required', 400);
    }

    if (content.length > 1000) {
      return error('Note content cannot exceed 1000 characters', 400);
    }

    // Check if post exists
    const post = await findOne('posts', { _id: new ObjectId(postId) });
    if (!post) {
      return error('Post not found', 404);
    }

    // Get user details for credibility scoring
    const user = await findOne('users', { _id: new ObjectId(userId) });
    if (!user) {
      return error('User not found', 404);
    }

    // Calculate initial credibility score
    const credibilityScore = calculateUserCredibility(user);

    // Validate sources
    const validatedSources = sources.filter(source => {
      try {
        new URL(source.url);
        return source.title && source.url;
      } catch {
        return false;
      }
    }).slice(0, 5); // Max 5 sources

    // Create note object
    const note = {
      postId: new ObjectId(postId),
      author: new ObjectId(userId),
      content,
      sources: validatedSources,
      category,
      votes: {
        helpful: [],
        notHelpful: [],
        score: 0
      },
      credibilityScore,
      status: 'pending', // pending, approved, rejected
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      reportCount: 0,
      viewCount: 0
    };

    // Insert note
    const result = await insertOne('community_notes', note);
    if (!result.insertedId) {
      return error('Failed to create note', 500);
    }

    // Get the created note with author details
    const createdNote = await aggregate('community_notes', [
      { $match: { _id: result.insertedId } },
      {
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorDetails'
        }
      },
      { $unwind: '$authorDetails' },
      {
        $project: {
          _id: 1,
          postId: 1,
          content: 1,
          sources: 1,
          category: 1,
          votes: 1,
          credibilityScore: 1,
          status: 1,
          createdAt: 1,
          viewCount: 1,
          author: {
            _id: '$authorDetails._id',
            username: '$authorDetails.username',
            profilePicture: '$authorDetails.profilePicture',
            isVerified: '$authorDetails.isVerified'
          }
        }
      }
    ]);

    return success({ note: createdNote[0] });

  } catch (err) {
    console.error('Error creating community note:', err);
    return error('Failed to create community note', 500);
  }
}));

// Get notes for a post
exports.getNotesForPost = handleAsync(async (event) => {
  try {
    const postId = event.queryStringParameters?.postId;
    const sortBy = event.queryStringParameters?.sortBy || 'score'; // score, recent, helpful

    if (!postId) {
      return error('Post ID is required', 400);
    }

    // Build sort criteria
    let sortCriteria = {};
    switch (sortBy) {
      case 'recent':
        sortCriteria = { createdAt: -1 };
        break;
      case 'helpful':
        sortCriteria = { 'votes.score': -1, createdAt: -1 };
        break;
      case 'score':
      default:
        sortCriteria = { credibilityScore: -1, 'votes.score': -1 };
    }

    // Get notes with author details
    const notes = await aggregate('community_notes', [
      {
        $match: {
          postId: new ObjectId(postId),
          isDeleted: false,
          status: { $in: ['approved', 'pending'] }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorDetails'
        }
      },
      { $unwind: '$authorDetails' },
      {
        $addFields: {
          helpfulVotes: { $size: '$votes.helpful' },
          notHelpfulVotes: { $size: '$votes.notHelpful' },
          totalVotes: { $add: [{ $size: '$votes.helpful' }, { $size: '$votes.notHelpful' }] }
        }
      },
      {
        $project: {
          _id: 1,
          postId: 1,
          content: 1,
          sources: 1,
          category: 1,
          credibilityScore: 1,
          status: 1,
          createdAt: 1,
          viewCount: 1,
          helpfulVotes: 1,
          notHelpfulVotes: 1,
          totalVotes: 1,
          voteScore: '$votes.score',
          author: {
            _id: '$authorDetails._id',
            username: '$authorDetails.username',
            profilePicture: '$authorDetails.profilePicture',
            isVerified: '$authorDetails.isVerified'
          }
        }
      },
      { $sort: sortCriteria },
      { $limit: 50 }
    ]);

    return success({ notes });

  } catch (err) {
    console.error('Error getting notes for post:', err);
    return error('Failed to get notes', 500);
  }
});

// Vote on a community note
exports.voteOnNote = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const { noteId, voteType } = JSON.parse(event.body); // 'helpful' or 'notHelpful'

    // Validate input
    if (!noteId || !['helpful', 'notHelpful'].includes(voteType)) {
      return error('Valid note ID and vote type are required', 400);
    }

    // Check if note exists
    const note = await findOne('community_notes', { _id: new ObjectId(noteId) });
    if (!note) {
      return error('Note not found', 404);
    }

    // Check if user already voted
    const userObjectId = new ObjectId(userId);
    const hasVotedHelpful = note.votes.helpful.some(id => id.toString() === userId);
    const hasVotedNotHelpful = note.votes.notHelpful.some(id => id.toString() === userId);

    if (hasVotedHelpful || hasVotedNotHelpful) {
      return error('You have already voted on this note', 400);
    }

    // Add vote
    const updateOperation = {
      $push: { [`votes.${voteType}`]: userObjectId },
      $set: { updatedAt: new Date() }
    };

    await updateOne('community_notes', { _id: new ObjectId(noteId) }, updateOperation);

    // Recalculate vote score
    const updatedNote = await findOne('community_notes', { _id: new ObjectId(noteId) });
    const helpfulCount = updatedNote.votes.helpful.length;
    const notHelpfulCount = updatedNote.votes.notHelpful.length;
    const totalVotes = helpfulCount + notHelpfulCount;
    
    // Calculate Wilson score for better ranking
    const voteScore = totalVotes > 0 ? calculateWilsonScore(helpfulCount, totalVotes) : 0;

    await updateOne(
      'community_notes',
      { _id: new ObjectId(noteId) },
      { $set: { 'votes.score': voteScore } }
    );

    return success({ 
      message: 'Vote recorded successfully',
      voteScore,
      helpfulVotes: helpfulCount,
      notHelpfulVotes: notHelpfulCount
    });

  } catch (err) {
    console.error('Error voting on note:', err);
    return error('Failed to record vote', 500);
  }
}));

// Report a community note
exports.reportNote = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const { noteId, reason } = JSON.parse(event.body);

    // Validate input
    if (!noteId || !reason) {
      return error('Note ID and reason are required', 400);
    }

    // Check if note exists
    const note = await findOne('community_notes', { _id: new ObjectId(noteId) });
    if (!note) {
      return error('Note not found', 404);
    }

    // Increment report count
    await updateOne(
      'community_notes',
      { _id: new ObjectId(noteId) },
      { 
        $inc: { reportCount: 1 },
        $set: { updatedAt: new Date() }
      }
    );

    // Create report record
    await insertOne('note_reports', {
      noteId: new ObjectId(noteId),
      reporterId: new ObjectId(userId),
      reason,
      createdAt: new Date()
    });

    return success({ message: 'Note reported successfully' });

  } catch (err) {
    console.error('Error reporting note:', err);
    return error('Failed to report note', 500);
  }
}));

// Get user's credibility score and note statistics
exports.getUserNoteStats = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;

    // Get user's notes statistics
    const stats = await aggregate('community_notes', [
      { $match: { author: new ObjectId(userId), isDeleted: false } },
      {
        $group: {
          _id: null,
          totalNotes: { $sum: 1 },
          approvedNotes: { $sum: { $cond: [{ $eq: ['$status', 'approved'] }, 1, 0] } },
          totalHelpfulVotes: { $sum: { $size: '$votes.helpful' } },
          totalNotHelpfulVotes: { $sum: { $size: '$votes.notHelpful' } },
          averageCredibilityScore: { $avg: '$credibilityScore' }
        }
      }
    ]);

    const userStats = stats[0] || {
      totalNotes: 0,
      approvedNotes: 0,
      totalHelpfulVotes: 0,
      totalNotHelpfulVotes: 0,
      averageCredibilityScore: 0
    };

    // Calculate overall credibility
    const user = await findOne('users', { _id: new ObjectId(userId) });
    const overallCredibility = calculateUserCredibility(user);

    return success({
      stats: userStats,
      credibilityScore: overallCredibility,
      helpfulnessRatio: userStats.totalHelpfulVotes + userStats.totalNotHelpfulVotes > 0 
        ? userStats.totalHelpfulVotes / (userStats.totalHelpfulVotes + userStats.totalNotHelpfulVotes)
        : 0
    });

  } catch (err) {
    console.error('Error getting user note stats:', err);
    return error('Failed to get user statistics', 500);
  }
}));

// Helper functions
function calculateUserCredibility(user) {
  let score = 0.5; // Base score

  // Account age factor (max +0.2)
  const accountAge = (new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24); // days
  score += Math.min(accountAge / 365, 0.2); // Max bonus for 1+ year old accounts

  // Verification bonus
  if (user.isVerified) score += 0.1;

  // Premium user bonus
  if (user.isPremium) score += 0.05;

  // Activity level (based on posts, comments, etc.)
  const activityScore = Math.min((user.postCount || 0) / 100, 0.1);
  score += activityScore;

  // Follower count factor (diminishing returns)
  const followerBonus = Math.min(Math.log10((user.followerCount || 0) + 1) / 100, 0.05);
  score += followerBonus;

  return Math.min(score, 1.0); // Cap at 1.0
}

function calculateWilsonScore(positive, total, confidence = 0.95) {
  if (total === 0) return 0;
  
  const z = 1.96; // 95% confidence interval
  const p = positive / total;
  
  const score = (p + z * z / (2 * total) - z * Math.sqrt((p * (1 - p) + z * z / (4 * total)) / total)) / (1 + z * z / total);
  
  return Math.max(0, score);
}

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/community-notes', '');
  
  switch (true) {
    case path === '/create' && event.httpMethod === 'POST':
      return exports.createNote(event, context);
    case path === '/post' && event.httpMethod === 'GET':
      return exports.getNotesForPost(event, context);
    case path === '/vote' && event.httpMethod === 'POST':
      return exports.voteOnNote(event, context);
    case path === '/report' && event.httpMethod === 'POST':
      return exports.reportNote(event, context);
    case path === '/stats' && event.httpMethod === 'GET':
      return exports.getUserNoteStats(event, context);
    default:
      return error('Not found', 404);
  }
};
