const { success, error, handleAsync } = require('./utils/response');
const { B2 } = require('backblaze-b2');

// Proxy endpoint to serve media files from private B2 bucket
exports.handler = handleAsync(async (event) => {
  try {
    // Get file path from query parameters
    const fileName = event.queryStringParameters?.file;

    if (!fileName) {
      return error('File parameter is required', 400);
    }

    // Validate file name to prevent directory traversal
    if (fileName.includes('..') || (fileName.includes('/') && !fileName.startsWith('posts/') && !fileName.startsWith('stories/'))) {
      return error('Invalid file path', 400);
    }

    console.log(`Media Proxy: Serving file ${fileName}`);

    // Initialize and authorize B2 client
    const b2 = new B2({
      applicationKeyId: process.env.B2_KEY_ID,
      applicationKey: process.env.B2_APP_KEY
    });

    await b2.authorize();

    // Download file from B2
    const downloadResponse = await b2.downloadFileByName({
      bucketName: process.env.B2_BUCKET_NAME,
      fileName: fileName
    });

    if (!downloadResponse || !downloadResponse.data) {
      return error('File not found', 404);
    }

    // Get content type from file extension
    const contentType = getContentType(fileName);

    // Return file with proper headers
    return {
      statusCode: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Content-Length': downloadResponse.data.length
      },
      body: downloadResponse.data.toString('base64'),
      isBase64Encoded: true
    };

  } catch (err) {
    console.error('Media proxy error:', err);
    return error('Failed to serve media file', 500);
  }
});

function getContentType(fileName) {
  const ext = fileName.toLowerCase().split('.').pop();

  const contentTypes = {
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'mp4': 'video/mp4',
    'mov': 'video/quicktime',
    'webm': 'video/webm'
  };

  return contentTypes[ext] || 'application/octet-stream';
}
