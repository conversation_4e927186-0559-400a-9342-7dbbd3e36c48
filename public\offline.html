<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - NeTuArk</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: #00d4ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            color: #0a0a0a;
        }

        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            opacity: 0.7;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #00d4ff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        p {
            font-size: 1.1rem;
            color: #cccccc;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .retry-button {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: #0a0a0a;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }

        .home-button {
            background: transparent;
            color: #00d4ff;
            border: 2px solid #00d4ff;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }

        .home-button:hover {
            background: #00d4ff;
            color: #0a0a0a;
            transform: translateY(-2px);
        }

        .features {
            margin-top: 50px;
            text-align: left;
        }

        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #00d4ff;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: #00d4ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 18px;
            color: #0a0a0a;
        }

        .feature-text {
            flex: 1;
        }

        .feature-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .feature-description {
            font-size: 0.9rem;
            color: #aaaaaa;
        }

        .motto {
            margin-top: 40px;
            font-style: italic;
            color: #00d4ff;
            font-size: 1.2rem;
        }

        @media (max-width: 600px) {
            h1 {
                font-size: 2rem;
            }
            
            .features {
                text-align: center;
            }
            
            .feature {
                flex-direction: column;
                text-align: center;
            }
            
            .feature-icon {
                margin-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="logo">NT</div>
        
        <div class="offline-icon">📡</div>
        
        <h1>You're Offline</h1>
        
        <p>
            It looks like you've lost your internet connection. Don't worry - 
            NeTuArk will sync your activity once you're back online.
        </p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
        
        <a href="/" class="home-button">
            Go Home
        </a>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💾</div>
                <div class="feature-text">
                    <div class="feature-title">Offline Storage</div>
                    <div class="feature-description">Your drafts and recent content are saved locally</div>
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🔄</div>
                <div class="feature-text">
                    <div class="feature-title">Auto Sync</div>
                    <div class="feature-description">Everything will sync automatically when you're back online</div>
                </div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-text">
                    <div class="feature-title">PWA Ready</div>
                    <div class="feature-description">Install NeTuArk as an app for the best offline experience</div>
                </div>
            </div>
        </div>
        
        <div class="motto">
            "The World Is Opening"
        </div>
    </div>

    <script>
        // Check for connection and auto-reload when back online
        window.addEventListener('online', () => {
            console.log('Connection restored');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        });

        // Show connection status
        if (navigator.onLine) {
            console.log('Online');
        } else {
            console.log('Offline');
        }
    </script>
</body>
</html>
