<template>
  <div class="kraizer-chat">
    <div class="chat-header">
      <div class="ai-info">
        <div class="ai-avatar">
          <i class="fas fa-robot"></i>
        </div>
        <div class="ai-details">
          <h3>Kraizer AI Assistant</h3>
          <p class="ai-status" :class="{ 'premium': isPremium }">
            {{ isPremium ? 'Unlimited requests' : `${remainingRequests} requests remaining this hour` }}
          </p>
        </div>
      </div>
      
      <button @click="clearChat" class="clear-chat-btn" title="Clear conversation">
        <i class="fas fa-trash"></i>
      </button>
    </div>

    <div class="chat-messages" ref="messagesContainer">
      <div v-if="messages.length === 0" class="welcome-message">
        <div class="welcome-content">
          <i class="fas fa-robot welcome-icon"></i>
          <h4>Hello! I'm <PERSON><PERSON><PERSON>, your AI assistant.</h4>
          <p>I can help you with:</p>
          <ul class="help-list">
            <li><i class="fas fa-lightbulb"></i> Content ideas and suggestions</li>
            <li><i class="fas fa-edit"></i> Writing and editing assistance</li>
            <li><i class="fas fa-question-circle"></i> Answering questions about NeTuArk</li>
            <li><i class="fas fa-chart-line"></i> Social media tips and strategies</li>
            <li><i class="fas fa-palette"></i> Creative inspiration</li>
          </ul>
          <p>What would you like to know?</p>
        </div>
      </div>

      <div v-for="message in messages" :key="message.id" class="message" :class="message.type">
        <div class="message-avatar">
          <img v-if="message.type === 'user'" :src="currentUser?.profilePicture || defaultAvatar" :alt="currentUser?.username" class="user-avatar" />
          <div v-else class="ai-avatar">
            <i class="fas fa-robot"></i>
          </div>
        </div>
        
        <div class="message-content">
          <div class="message-header">
            <span class="message-sender">
              {{ message.type === 'user' ? (currentUser?.username || 'You') : 'Kraizer' }}
            </span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          
          <div class="message-text" v-html="formatMessage(message.content)"></div>
          
          <div v-if="message.type === 'ai' && message.suggestions" class="message-suggestions">
            <div class="suggestions-label">Suggested follow-ups:</div>
            <div class="suggestions-list">
              <button 
                v-for="suggestion in message.suggestions" 
                :key="suggestion"
                @click="sendMessage(suggestion)"
                class="suggestion-btn"
              >
                {{ suggestion }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="isTyping" class="message ai typing">
        <div class="message-avatar">
          <div class="ai-avatar">
            <i class="fas fa-robot"></i>
          </div>
        </div>
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-input-container">
      <div v-if="!isPremium && remainingRequests <= 0" class="rate-limit-notice">
        <i class="fas fa-clock"></i>
        <span>You've reached your hourly limit. Upgrade to NTA+ for unlimited AI requests!</span>
        <router-link to="/subscription" class="upgrade-link">Upgrade Now</router-link>
      </div>
      
      <div class="chat-input">
        <textarea 
          v-model="inputMessage" 
          placeholder="Ask Kraizer anything..." 
          rows="1"
          class="message-input"
          @keydown.enter.prevent="handleEnter"
          @input="adjustTextareaHeight"
          ref="messageInput"
          :disabled="isLoading || (!isPremium && remainingRequests <= 0)"
        ></textarea>
        
        <button 
          @click="sendMessage()" 
          class="send-btn"
          :disabled="!inputMessage.trim() || isLoading || (!isPremium && remainingRequests <= 0)"
        >
          <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-paper-plane"></i>
        </button>
      </div>
      
      <div class="quick-actions">
        <button @click="sendMessage('Help me create engaging content')" class="quick-action-btn">
          <i class="fas fa-lightbulb"></i> Content Ideas
        </button>
        <button @click="sendMessage('What are some social media best practices?')" class="quick-action-btn">
          <i class="fas fa-chart-line"></i> Social Tips
        </button>
        <button @click="sendMessage('Help me write a caption for my post')" class="quick-action-btn">
          <i class="fas fa-edit"></i> Caption Help
        </button>
      </div>
    </div>

    <div v-if="error" class="error-message">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error }}
      <button @click="error = null" class="close-error-btn">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { useSubscriptionStore } from '@/stores/subscription';
import { useAiAssistantStore } from '@/stores/ai-assistant';

// Stores
const authStore = useAuthStore();
const subscriptionStore = useSubscriptionStore();
const aiAssistantStore = useAiAssistantStore();

// State
const messages = ref([]);
const inputMessage = ref('');
const isLoading = ref(false);
const isTyping = ref(false);
const error = ref(null);
const messagesContainer = ref(null);
const messageInput = ref(null);

// Computed
const currentUser = computed(() => authStore.user);
const isPremium = computed(() => subscriptionStore.isPremium);
const remainingRequests = computed(() => aiAssistantStore.remainingRequests);
const defaultAvatar = `https://ui-avatars.com/api/?name=User&background=3498db&color=fff&size=48`;

// Methods
function formatTime(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function formatMessage(content) {
  // Convert markdown-like formatting to HTML
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
}

async function sendMessage(messageText = null) {
  const text = messageText || inputMessage.value.trim();
  if (!text || isLoading.value) return;
  
  if (!isPremium.value && remainingRequests.value <= 0) {
    error.value = 'You have reached your hourly limit. Upgrade to NTA+ for unlimited requests.';
    return;
  }

  // Add user message
  const userMessage = {
    id: Date.now(),
    type: 'user',
    content: text,
    timestamp: new Date()
  };
  
  messages.value.push(userMessage);
  
  // Clear input if it was typed (not from suggestion)
  if (!messageText) {
    inputMessage.value = '';
  }
  
  // Scroll to bottom
  scrollToBottom();
  
  // Show typing indicator
  isTyping.value = true;
  isLoading.value = true;
  error.value = null;
  
  try {
    // Call AI assistant
    const response = await aiAssistantStore.chatWithAI(text);
    
    // Add AI response
    const aiMessage = {
      id: Date.now() + 1,
      type: 'ai',
      content: response.response,
      suggestions: response.suggestions || [],
      timestamp: new Date()
    };
    
    messages.value.push(aiMessage);
    
    // Scroll to bottom
    scrollToBottom();
  } catch (err) {
    error.value = err.message || 'Failed to get AI response. Please try again.';
  } finally {
    isTyping.value = false;
    isLoading.value = false;
  }
}

function handleEnter(event) {
  if (!event.shiftKey) {
    sendMessage();
  }
}

function adjustTextareaHeight() {
  const textarea = messageInput.value;
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }
}

function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
}

function clearChat() {
  if (confirm('Are you sure you want to clear the conversation?')) {
    messages.value = [];
  }
}

// Lifecycle hooks
onMounted(() => {
  // Focus input
  if (messageInput.value) {
    messageInput.value.focus();
  }
  
  // Fetch remaining requests
  aiAssistantStore.fetchRemainingRequests();
});
</script>

<style scoped>
.kraizer-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  overflow: hidden;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.ai-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-avatar {
  width: 48px;
  height: 48px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.ai-details h3 {
  margin: 0;
  font-size: 1.1rem;
}

.ai-status {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.ai-status.premium {
  color: #f39c12;
}

.clear-chat-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.clear-chat-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.welcome-message {
  text-align: center;
  padding: 2rem 1rem;
}

.welcome-icon {
  font-size: 3rem;
  color: #3498db;
  margin-bottom: 1rem;
}

.welcome-content h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.help-list {
  text-align: left;
  max-width: 300px;
  margin: 1rem auto;
  padding: 0;
  list-style: none;
}

.help-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  color: #555;
}

.help-list i {
  color: #3498db;
  width: 16px;
}

.message {
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar .user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.message-avatar .ai-avatar {
  width: 36px;
  height: 36px;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.message-content {
  max-width: 70%;
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 0.75rem;
}

.message.user .message-content {
  background-color: #3498db;
  color: white;
}

.message.ai .message-content {
  background-color: #e8f4fd;
  border: 1px solid #3498db;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-sender {
  font-weight: bold;
  font-size: 0.9rem;
}

.message-time {
  font-size: 0.8rem;
  opacity: 0.7;
}

.message-text {
  line-height: 1.4;
  word-wrap: break-word;
}

.message-suggestions {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(52, 152, 219, 0.2);
}

.suggestions-label {
  font-size: 0.8rem;
  color: #777;
  margin-bottom: 0.5rem;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.suggestion-btn {
  background-color: rgba(52, 152, 219, 0.1);
  border: 1px solid #3498db;
  color: #3498db;
  border-radius: 16px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
}

.suggestion-btn:hover {
  background-color: #3498db;
  color: white;
}

.typing {
  opacity: 0.7;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #3498db;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.chat-input-container {
  padding: 1rem;
  border-top: 1px solid #ddd;
  background-color: #f8f9fa;
}

.rate-limit-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #856404;
}

.upgrade-link {
  color: #f39c12;
  text-decoration: none;
  font-weight: bold;
}

.upgrade-link:hover {
  text-decoration: underline;
}

.chat-input {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
  margin-bottom: 0.75rem;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 24px;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  outline: none;
  min-height: 44px;
  max-height: 120px;
}

.message-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.message-input:disabled {
  background-color: #f0f0f0;
  cursor: not-allowed;
}

.send-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;
}

.send-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
  background-color: #2980b9;
}

.quick-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.quick-action-btn {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 0.5rem 0.75rem;
  font-size: 0.8rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #555;
}

.quick-action-btn:hover {
  background-color: #f0f0f0;
  border-color: #3498db;
  color: #3498db;
}

.error-message {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: #fee;
  color: #c0392b;
  border: 1px solid #e74c3c;
  border-radius: 8px;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 10;
}

.close-error-btn {
  background: none;
  border: none;
  color: #c0392b;
  cursor: pointer;
  margin-left: auto;
}

@media (max-width: 768px) {
  .quick-actions {
    flex-direction: column;
  }
  
  .quick-action-btn {
    justify-content: center;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style>
