const { findOne, updateOne } = require('./utils/db');
const { generateToken, verifyPassword } = require('./utils/auth-utils');

// Background function for user login
// This runs with a 15-minute timeout instead of 10 seconds
exports.handler = async (event, context) => {
  try {
    console.log('Background login process started');
    console.time('login-background-total');
    
    // Parse request body
    const body = JSON.parse(event.body || '{}');
    const { email, password } = body;
    
    // Basic validation
    if (!email || !password) {
      return {
        statusCode: 400,
        body: JSON.stringify({ success: false, error: 'Missing required fields' })
      };
    }

    // Find user by email
    console.log(`Attempting to find user with email: ${email}`);
    console.time('db-find-user');
    const user = await findOne('users', { email });
    console.timeEnd('db-find-user');

    console.log('Database lookup result:', {
      userFound: !!user,
      database: process.env.NODE_ENV === 'production' ? 'MongoDB' : 'in-memory'
    });

    if (!user) {
      return {
        statusCode: 401,
        body: JSON.stringify({ success: false, error: 'Invalid email or password' })
      };
    }

    // Verify password
    console.time('password-verify');
    const isPasswordValid = await verifyPassword(password, user.password);
    console.timeEnd('password-verify');

    if (!isPasswordValid) {
      return {
        statusCode: 401,
        body: JSON.stringify({ success: false, error: 'Invalid email or password' })
      };
    }

    // Update last login time
    console.time('db-update-login');
    await updateOne(
      'users',
      { _id: user._id },
      { $set: { lastLoginAt: new Date(), updatedAt: new Date() } }
    );
    console.timeEnd('db-update-login');

    // Generate JWT token with appropriate expiration
    console.time('token-generation');
    const token = generateToken(user);
    console.timeEnd('token-generation');

    console.timeEnd('login-background-total');
    
    // Return user data and token (excluding sensitive information)
    return {
      statusCode: 200,
      body: JSON.stringify({
        success: true,
        data: {
          user: {
            _id: user._id,
            email: user.email,
            username: user.username,
            profilePicture: user.profilePicture,
            isPremium: user.isPremium,
            role: user.role,
            createdAt: user.createdAt,
            lastLoginAt: new Date()
          },
          token
        }
      })
    };
  } catch (error) {
    console.error('Error in background login process:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ success: false, error: 'An unexpected error occurred' })
    };
  }
};
