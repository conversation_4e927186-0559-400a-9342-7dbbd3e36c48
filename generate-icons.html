<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeTuArk Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: white;
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            display: block;
            margin-bottom: 5px;
        }
        button {
            background: #00d4ff;
            color: #0a0a0a;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #0099cc;
        }
        .instructions {
            background: #1a1a1a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>NeTuArk Icon Generator</h1>

    <div class="instructions">
        <h2>Instructions:</h2>
        <ol>
            <li>Click "Generate Icons" to create all required PWA icons</li>
            <li>Right-click each icon and "Save image as..." to your public folder</li>
            <li>Save with the exact filenames shown below each icon</li>
            <li>Also create placeholder screenshots for the manifest</li>
        </ol>
    </div>

    <button onclick="generateIcons()">Generate Icons</button>
    <button onclick="generateScreenshots()">Generate Screenshots</button>
    <button onclick="downloadAllIcons()">Download All Icons</button>

    <div id="icons-container"></div>
    <div id="screenshots-container"></div>

    <script>
        function createIcon(size, filename, isMaskable = false) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');

            // Background
            if (isMaskable) {
                // Maskable icons need safe area
                ctx.fillStyle = '#00d4ff';
                ctx.fillRect(0, 0, size, size);

                // Safe area (80% of icon)
                const safeSize = size * 0.8;
                const offset = (size - safeSize) / 2;
                ctx.fillStyle = '#0a0a0a';
                ctx.fillRect(offset, offset, safeSize, safeSize);

                // Logo in safe area
                ctx.fillStyle = '#00d4ff';
                ctx.font = `bold ${safeSize * 0.4}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('NT', size / 2, size / 2);
            } else {
                // Regular icons
                ctx.fillStyle = '#00d4ff';
                ctx.fillRect(0, 0, size, size);

                // Logo
                ctx.fillStyle = '#0a0a0a';
                ctx.font = `bold ${size * 0.4}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('NT', size / 2, size / 2);
            }

            // Create download link
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');

            // Create preview
            const preview = document.createElement('div');
            preview.className = 'icon-preview';

            const img = document.createElement('img');
            img.src = canvas.toDataURL('image/png');
            img.className = 'icon';
            img.style.width = Math.min(size, 128) + 'px';
            img.style.height = Math.min(size, 128) + 'px';
            img.style.border = '1px solid #333';

            const label = document.createElement('div');
            label.textContent = filename;
            label.style.fontSize = '12px';
            label.style.color = '#ccc';

            preview.appendChild(link);
            link.appendChild(img);
            preview.appendChild(label);

            return preview;
        }

        function generateIcons() {
            const container = document.getElementById('icons-container');
            container.innerHTML = '<h2>Generated Icons (Right-click to save):</h2>';

            const icons = [
                { size: 48, filename: 'icon-48.png' },
                { size: 72, filename: 'icon-72.png' },
                { size: 96, filename: 'icon-96.png' },
                { size: 144, filename: 'icon-144.png' },
                { size: 192, filename: 'icon-192.png' },
                { size: 512, filename: 'icon-512.png' },
                { size: 192, filename: 'icon-192-maskable.png', isMaskable: true },
                { size: 512, filename: 'icon-512-maskable.png', isMaskable: true }
            ];

            icons.forEach(icon => {
                const preview = createIcon(icon.size, icon.filename, icon.isMaskable);
                container.appendChild(preview);
            });
        }

        function createScreenshot(width, height, filename, type) {
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');

            // Background
            ctx.fillStyle = '#0a0a0a';
            ctx.fillRect(0, 0, width, height);

            // Header
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, 60);

            // Logo
            ctx.fillStyle = '#00d4ff';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'left';
            ctx.textBaseline = 'middle';
            ctx.fillText('NeTuArk', 20, 30);

            // Subtitle
            ctx.fillStyle = '#ccc';
            ctx.font = '14px Arial';
            ctx.fillText('The World Is Opening', 120, 30);

            // Content area
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(20, 80, width - 40, height - 120);

            // Sample content
            ctx.fillStyle = '#00d4ff';
            ctx.font = 'bold 18px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Welcome to NeTuArk', width / 2, height / 2 - 40);

            ctx.fillStyle = '#ccc';
            ctx.font = '14px Arial';
            ctx.fillText('A modern social platform where the world is opening', width / 2, height / 2);

            if (type === 'mobile') {
                // Mobile-specific elements
                ctx.fillStyle = '#333';
                ctx.fillRect(0, height - 60, width, 60);

                // Bottom navigation
                const navItems = ['Home', 'Discover', 'Messages', 'Profile'];
                const itemWidth = width / navItems.length;

                navItems.forEach((item, index) => {
                    ctx.fillStyle = index === 0 ? '#00d4ff' : '#ccc';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(item, (index + 0.5) * itemWidth, height - 20);
                });
            }

            // Create download link
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');

            // Create preview
            const preview = document.createElement('div');
            preview.className = 'icon-preview';

            const img = document.createElement('img');
            img.src = canvas.toDataURL('image/png');
            img.className = 'icon';
            img.style.width = Math.min(width / 4, 320) + 'px';
            img.style.height = Math.min(height / 4, 240) + 'px';
            img.style.border = '1px solid #333';

            const label = document.createElement('div');
            label.textContent = filename;
            label.style.fontSize = '12px';
            label.style.color = '#ccc';

            preview.appendChild(link);
            link.appendChild(img);
            preview.appendChild(label);

            return preview;
        }

        function generateScreenshots() {
            const container = document.getElementById('screenshots-container');
            container.innerHTML = '<h2>Generated Screenshots (Right-click to save):</h2>';

            const screenshots = [
                { width: 1280, height: 720, filename: 'screenshot-desktop.png', type: 'desktop' },
                { width: 750, height: 1334, filename: 'screenshot-mobile.png', type: 'mobile' }
            ];

            screenshots.forEach(screenshot => {
                const preview = createScreenshot(screenshot.width, screenshot.height, screenshot.filename, screenshot.type);
                container.appendChild(preview);
            });
        }

        // Auto-generate on load
        window.onload = function() {
            generateIcons();
            generateScreenshots();
        };

        // Auto-download all icons
        function downloadAllIcons() {
            const icons = [
                { size: 48, filename: 'icon-48.png' },
                { size: 72, filename: 'icon-72.png' },
                { size: 96, filename: 'icon-96.png' },
                { size: 144, filename: 'icon-144.png' },
                { size: 192, filename: 'icon-192.png' },
                { size: 512, filename: 'icon-512.png' },
                { size: 192, filename: 'icon-192-maskable.png', isMaskable: true },
                { size: 512, filename: 'icon-512-maskable.png', isMaskable: true }
            ];

            icons.forEach((icon, index) => {
                setTimeout(() => {
                    const canvas = document.createElement('canvas');
                    canvas.width = icon.size;
                    canvas.height = icon.size;
                    const ctx = canvas.getContext('2d');

                    if (icon.isMaskable) {
                        ctx.fillStyle = '#00d4ff';
                        ctx.fillRect(0, 0, icon.size, icon.size);

                        const safeSize = icon.size * 0.8;
                        const offset = (icon.size - safeSize) / 2;
                        ctx.fillStyle = '#0a0a0a';
                        ctx.fillRect(offset, offset, safeSize, safeSize);

                        ctx.fillStyle = '#00d4ff';
                        ctx.font = `bold ${safeSize * 0.4}px Arial`;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('NT', icon.size / 2, icon.size / 2);
                    } else {
                        ctx.fillStyle = '#00d4ff';
                        ctx.fillRect(0, 0, icon.size, icon.size);

                        ctx.fillStyle = '#0a0a0a';
                        ctx.font = `bold ${icon.size * 0.4}px Arial`;
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'middle';
                        ctx.fillText('NT', icon.size / 2, icon.size / 2);
                    }

                    const link = document.createElement('a');
                    link.download = icon.filename;
                    link.href = canvas.toDataURL('image/png');
                    link.click();
                }, index * 500);
            });
        }
    </script>
</body>
</html>
