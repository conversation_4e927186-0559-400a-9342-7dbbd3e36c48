const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { findOne, find, insertOne, updateOne, deleteOne } = require('./utils/db');
const { uploadToB2, deleteFromB2 } = require('./utils/storage');
const { ObjectId } = require('mongodb');

// Get user's custom emojis
exports.getUserEmojis = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user's custom emojis
  const emojis = await find('emojis', { creator: new ObjectId(userId) });
  
  // Return emojis
  return success({ emojis });
}));

// Get user's favorite emojis
exports.getFavoriteEmojis = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  if (!user) {
    return error('User not found', 404);
  }
  
  // Get favorite emoji IDs
  const favoriteEmojiIds = user.favoriteEmojis || [];
  
  // Convert to ObjectId
  const objectIds = favoriteEmojiIds.map(id => new ObjectId(id));
  
  // Get emojis
  const emojis = await find('emojis', { _id: { $in: objectIds } });
  
  // Return emojis
  return success({ emojis });
}));

// Create a new custom emoji
exports.createEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { name, imageData, isAnimated } = JSON.parse(event.body);
  
  // Validate input
  if (!name || !imageData) {
    return error('Name and image data are required');
  }
  
  // Check if name is valid (alphanumeric, underscores, no spaces)
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    return error('Emoji name must contain only letters, numbers, and underscores');
  }
  
  // Check if emoji with this name already exists for this user
  const existingEmoji = await findOne('emojis', { 
    creator: new ObjectId(userId),
    name
  });
  
  if (existingEmoji) {
    return error('You already have an emoji with this name');
  }
  
  // Check if user has reached their emoji limit
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  if (!user) {
    return error('User not found', 404);
  }
  
  // Count user's emojis
  const emojiCount = await find('emojis', { creator: new ObjectId(userId) }, { count: true });
  
  // Check if user has reached their limit
  const emojiLimit = user.isPremium ? Infinity : 10;
  
  if (emojiCount >= emojiLimit) {
    return error('You have reached your emoji creation limit. Upgrade to NTA+ for unlimited emoji creation.');
  }
  
  // Upload image to Backblaze B2
  const imageBuffer = Buffer.from(imageData.replace(/^data:image\/\w+;base64,/, ''), 'base64');
  const contentType = imageData.match(/^data:image\/(\w+);base64,/)[1];
  const extension = contentType === 'gif' ? 'gif' : 'png';
  
  // Check if animated emoji is allowed for this user
  if (isAnimated && !user.isPremium) {
    return error('Animated emojis are only available for NTA+ users');
  }
  
  // Upload to B2
  const fileName = `emojis/${userId}/${name}.${extension}`;
  
  try {
    const uploadResult = await uploadToB2(imageBuffer, fileName, `image/${contentType}`);
    
    // Create emoji in database
    const emoji = {
      name,
      creator: new ObjectId(userId),
      url: uploadResult.url,
      isAnimated: isAnimated || false,
      usageCount: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const result = await insertOne('emojis', emoji);
    
    // Return emoji with ID
    return success({
      emoji: {
        _id: result.insertedId,
        ...emoji
      }
    });
  } catch (uploadError) {
    console.error('Error uploading emoji:', uploadError);
    return error('Failed to upload emoji image');
  }
}));

// Add emoji to favorites
exports.addToFavorites = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Check if emoji exists
  const emoji = await findOne('emojis', { _id: new ObjectId(emojiId) });
  
  if (!emoji) {
    return error('Emoji not found', 404);
  }
  
  // Get user
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  if (!user) {
    return error('User not found', 404);
  }
  
  // Check if emoji is already in favorites
  const favoriteEmojis = user.favoriteEmojis || [];
  
  if (favoriteEmojis.includes(emojiId)) {
    return error('Emoji is already in favorites');
  }
  
  // Check if user has reached their favorites limit
  const favoritesLimit = user.isPremium ? Infinity : 15;
  
  if (favoriteEmojis.length >= favoritesLimit) {
    return error('You have reached your favorites limit. Upgrade to NTA+ for unlimited favorites.');
  }
  
  // Add emoji to favorites
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $push: { favoriteEmojis: emojiId } }
  );
  
  // Return emoji
  return success({ emoji });
}));

// Remove emoji from favorites
exports.removeFromFavorites = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Remove emoji from favorites
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $pull: { favoriteEmojis: emojiId } }
  );
  
  // Return success
  return success({ message: 'Emoji removed from favorites' });
}));

// Delete a custom emoji
exports.deleteEmoji = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { emojiId } = JSON.parse(event.body);
  
  // Validate input
  if (!emojiId) {
    return error('Emoji ID is required');
  }
  
  // Check if emoji exists and belongs to user
  const emoji = await findOne('emojis', { 
    _id: new ObjectId(emojiId),
    creator: new ObjectId(userId)
  });
  
  if (!emoji) {
    return error('Emoji not found or you do not have permission to delete it', 404);
  }
  
  // Delete emoji from B2
  try {
    // Extract file name from URL
    const fileName = emoji.url.split('/').pop();
    await deleteFromB2(`emojis/${userId}/${fileName}`);
  } catch (deleteError) {
    console.error('Error deleting emoji from B2:', deleteError);
    // Continue with deletion from database even if B2 deletion fails
  }
  
  // Delete emoji from database
  await deleteOne('emojis', { _id: new ObjectId(emojiId) });
  
  // Remove emoji from favorites for all users
  await updateOne(
    'users',
    { favoriteEmojis: emojiId },
    { $pull: { favoriteEmojis: emojiId } },
    { multi: true }
  );
  
  // Return success
  return success({ message: 'Emoji deleted successfully' });
}));

// Get emoji analytics
exports.getEmojiAnalytics = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user's emojis
  const emojis = await find('emojis', { creator: new ObjectId(userId) });
  
  // Calculate analytics
  const analytics = {
    totalEmojis: emojis.length,
    totalUsage: emojis.reduce((total, emoji) => total + (emoji.usageCount || 0), 0),
    mostPopular: null,
    leastPopular: null,
    byPopularity: []
  };
  
  // Sort emojis by usage count
  const sortedEmojis = [...emojis].sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0));
  
  // Set most and least popular
  if (sortedEmojis.length > 0) {
    analytics.mostPopular = sortedEmojis[0];
    analytics.leastPopular = sortedEmojis[sortedEmojis.length - 1];
    analytics.byPopularity = sortedEmojis;
  }
  
  // Return analytics
  return success({ analytics });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/emoji', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/user-emojis' && event.httpMethod === 'GET':
      return exports.getUserEmojis(event, context);
    case path === '/favorites' && event.httpMethod === 'GET':
      return exports.getFavoriteEmojis(event, context);
    case path === '/create' && event.httpMethod === 'POST':
      return exports.createEmoji(event, context);
    case path === '/add-favorite' && event.httpMethod === 'POST':
      return exports.addToFavorites(event, context);
    case path === '/remove-favorite' && event.httpMethod === 'POST':
      return exports.removeFromFavorites(event, context);
    case path === '/delete' && event.httpMethod === 'DELETE':
      return exports.deleteEmoji(event, context);
    case path === '/analytics' && event.httpMethod === 'GET':
      return exports.getEmojiAnalytics(event, context);
    default:
      return error('Not found', 404);
  }
};
