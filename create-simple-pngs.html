<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple PNG Icon Creator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
        }
        .container {
            background: #1a1a1a;
            padding: 30px;
            border-radius: 12px;
        }
        h1 {
            color: #00d4ff;
            text-align: center;
        }
        .btn {
            background: #00d4ff;
            color: #0a0a0a;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px;
            display: block;
            width: 100%;
        }
        .btn:hover {
            background: #00b8e6;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .success {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            color: #00d4ff;
        }
        canvas {
            display: none;
        }
        .download-links {
            margin-top: 20px;
        }
        .download-link {
            display: inline-block;
            margin: 5px;
            padding: 10px 15px;
            background: #333;
            color: #00d4ff;
            text-decoration: none;
            border-radius: 5px;
        }
        .download-link:hover {
            background: #444;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Simple PNG Icon Creator</h1>
        
        <button class="btn" onclick="createAllPNGs()">
            🚀 Create All PNG Icons
        </button>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="downloadLinks" class="download-links" style="display: none;">
            <h3>📥 Download Icons:</h3>
        </div>
        
        <canvas id="canvas"></canvas>
    </div>

    <script>
        const iconSizes = [48, 72, 96, 144, 192, 512];
        const bgColor = '#00d4ff';
        const textColor = '#0a0a0a';
        
        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }
        
        function createIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Draw background circle
            const center = size / 2;
            const radius = size / 2 - 4;
            
            // Create gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, bgColor);
            gradient.addColorStop(1, '#0099cc');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = 3;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // Draw the "N" letter
            ctx.fillStyle = textColor;
            ctx.font = `bold ${size * 0.4}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('N', center, center - size * 0.05);
            
            // Reset shadow
            ctx.shadowColor = 'transparent';
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            
            // Draw connection lines
            ctx.strokeStyle = textColor;
            ctx.lineWidth = size * 0.02;
            ctx.globalAlpha = 0.7;
            ctx.beginPath();
            ctx.moveTo(center - size * 0.2, center - size * 0.1);
            ctx.lineTo(center + size * 0.2, center + size * 0.1);
            ctx.stroke();
            
            // Draw small dots
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = textColor;
            
            const dotSize = size * 0.02;
            const positions = [
                [center - size * 0.15, center + size * 0.15],
                [center + size * 0.15, center + size * 0.15],
                [center, center + size * 0.2]
            ];
            
            positions.forEach(([x, y]) => {
                ctx.beginPath();
                ctx.arc(x, y, dotSize, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            ctx.globalAlpha = 1;
            
            return canvas.toDataURL('image/png');
        }
        
        function downloadIcon(dataUrl, size) {
            const link = document.createElement('a');
            link.download = `icon-${size}.png`;
            link.href = dataUrl;
            link.textContent = `icon-${size}.png`;
            link.className = 'download-link';
            
            document.getElementById('downloadLinks').appendChild(link);
        }
        
        function createAllPNGs() {
            showStatus('Creating PNG icons...', 'success');
            
            const downloadLinksEl = document.getElementById('downloadLinks');
            downloadLinksEl.innerHTML = '<h3>📥 Download Icons:</h3>';
            downloadLinksEl.style.display = 'block';
            
            iconSizes.forEach(size => {
                try {
                    const dataUrl = createIcon(size);
                    downloadIcon(dataUrl, size);
                } catch (error) {
                    console.error(`Error creating icon ${size}:`, error);
                }
            });
            
            showStatus('✅ All PNG icons created! Click the links below to download them.', 'success');
        }
        
        // Auto-create icons on page load
        window.addEventListener('load', () => {
            showStatus('Ready to create PNG icons. Click the button above!', 'success');
        });
    </script>
</body>
</html>
