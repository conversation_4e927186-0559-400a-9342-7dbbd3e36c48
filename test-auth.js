// Simple test script to verify authentication is working
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:8888/.netlify/functions';

async function testAuth() {
  console.log('Testing NeTuArk Authentication...\n');

  // Test 1: Register a new user
  console.log('1. Testing Registration...');
  try {
    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        username: 'testuser',
        password: 'testpassword123'
      })
    });

    const registerData = await registerResponse.json();
    console.log('Register Response Status:', registerResponse.status);
    console.log('Register Response:', registerData);

    if (registerResponse.ok) {
      console.log('✅ Registration successful!');
      
      // Test 2: Login with the registered user
      console.log('\n2. Testing Login...');
      const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpassword123'
        })
      });

      const loginData = await loginResponse.json();
      console.log('Login Response Status:', loginResponse.status);
      console.log('Login Response:', loginData);

      if (loginResponse.ok) {
        console.log('✅ Login successful!');
        
        // Test 3: Get current user info
        console.log('\n3. Testing Get Current User...');
        const meResponse = await fetch(`${BASE_URL}/auth/me`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${loginData.data.token}`
          }
        });

        const meData = await meResponse.json();
        console.log('Me Response Status:', meResponse.status);
        console.log('Me Response:', meData);

        if (meResponse.ok) {
          console.log('✅ Get current user successful!');
        } else {
          console.log('❌ Get current user failed');
        }
      } else {
        console.log('❌ Login failed');
      }
    } else {
      console.log('❌ Registration failed');
      
      // If registration failed due to existing user, try login
      if (registerResponse.status === 409) {
        console.log('\n2. User already exists, testing login...');
        const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'testpassword123'
          })
        });

        const loginData = await loginResponse.json();
        console.log('Login Response Status:', loginResponse.status);
        console.log('Login Response:', loginData);

        if (loginResponse.ok) {
          console.log('✅ Login successful!');
        } else {
          console.log('❌ Login failed');
        }
      }
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testAuth();
