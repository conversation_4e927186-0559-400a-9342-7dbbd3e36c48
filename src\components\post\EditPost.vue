<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { usePostsStore } from '../../stores/posts';
import { useAuthStore } from '../../stores/auth';

const props = defineProps({
  postId: {
    type: String,
    required: true
  },
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'updated']);

const postsStore = usePostsStore();
const authStore = useAuthStore();

// State
const post = ref(null);
const content = ref('');
const visibility = ref('public');
const loading = ref(false);
const saving = ref(false);
const error = ref(null);
const maxContentLength = 500;

// Computed
const contentLength = computed(() => content.value.length);
const isContentValid = computed(() => 
  content.value.trim().length > 0 && content.value.length <= maxContentLength
);

// Fetch post data
const fetchPost = async () => {
  if (!props.postId) return;

  try {
    loading.value = true;
    error.value = null;

    const response = await fetch(`/.netlify/functions/posts/${props.postId}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch post');
    }

    post.value = data.data.post;
    content.value = post.value.content || '';
    visibility.value = post.value.visibility || 'public';

  } catch (err) {
    error.value = err.message;
    console.error('Error fetching post:', err);
  } finally {
    loading.value = false;
  }
};

// Save changes
const saveChanges = async () => {
  if (!isContentValid.value || !post.value) return;

  try {
    saving.value = true;
    error.value = null;

    const response = await fetch(`/.netlify/functions/posts/${props.postId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        content: content.value.trim(),
        visibility: visibility.value
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to update post');
    }

    // Update the post in the store
    const updatedPost = data.data.post;
    const postIndex = postsStore.posts.findIndex(p => p._id === props.postId);
    if (postIndex !== -1) {
      postsStore.posts[postIndex] = updatedPost;
    }

    emit('updated', updatedPost);
    emit('close');

  } catch (err) {
    error.value = err.message;
    console.error('Error updating post:', err);
  } finally {
    saving.value = false;
  }
};

// Close modal
const closeModal = () => {
  emit('close');
};

// Watch for prop changes
watch(() => props.show, (newValue) => {
  if (newValue && props.postId) {
    fetchPost();
  }
});

// Check if user can edit this post
const canEdit = computed(() => {
  return post.value && authStore.user && 
    (post.value.author._id === authStore.user._id || authStore.user.isAdmin);
});

onMounted(() => {
  if (props.show && props.postId) {
    fetchPost();
  }
});
</script>

<template>
  <div
    v-if="show"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="closeModal"
  >
    <div
      class="bg-darker-bg rounded-xl border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- Header -->
      <div class="p-6 border-b border-gray-800 flex justify-between items-center">
        <h2 class="text-xl font-semibold">Edit Post</h2>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 overflow-y-auto max-h-[60vh]">
        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-neon-blue"></div>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="bg-red-900/20 border border-red-500 rounded-lg p-4 text-center">
          <p class="text-red-400">{{ error }}</p>
          <button @click="fetchPost" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
            Try Again
          </button>
        </div>

        <!-- No Permission -->
        <div v-else-if="post && !canEdit" class="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <p class="text-red-400">You don't have permission to edit this post</p>
        </div>

        <!-- Edit Form -->
        <div v-else-if="post && canEdit" class="space-y-6">
          <!-- Content Editor -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Content
            </label>
            <textarea
              v-model="content"
              placeholder="What's on your mind?"
              class="w-full h-32 bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-neon-blue focus:outline-none resize-none"
              :maxlength="maxContentLength"
            ></textarea>
            
            <!-- Character count -->
            <div class="flex justify-between items-center mt-2 text-sm">
              <span 
                :class="[
                  'transition-colors',
                  contentLength > maxContentLength * 0.9 
                    ? 'text-red-400' 
                    : contentLength > maxContentLength * 0.8 
                      ? 'text-yellow-400' 
                      : 'text-gray-400'
                ]"
              >
                {{ contentLength }}/{{ maxContentLength }}
              </span>
            </div>
          </div>

          <!-- Visibility Settings -->
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Visibility
            </label>
            <select
              v-model="visibility"
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none"
            >
              <option value="public">Public</option>
              <option value="followers">Followers only</option>
              <option value="private">Private</option>
            </select>
          </div>

          <!-- Original Post Info -->
          <div class="bg-dark-bg rounded-lg p-4 border border-gray-700">
            <div class="flex items-center gap-3 mb-3">
              <img 
                :src="post.author.profilePicture || 'https://via.placeholder.com/40'" 
                :alt="post.author.username"
                class="w-8 h-8 rounded-full object-cover"
              />
              <div>
                <div class="font-medium text-sm">{{ post.author.username }}</div>
                <div class="text-xs text-gray-400">
                  {{ new Date(post.createdAt).toLocaleDateString() }}
                </div>
              </div>
            </div>
            
            <!-- Media preview if exists -->
            <div v-if="post.media && post.media.length > 0" class="mb-3">
              <div class="grid grid-cols-2 gap-2">
                <img
                  v-for="(media, index) in post.media.slice(0, 4)"
                  :key="index"
                  :src="media.url"
                  :alt="`Media ${index + 1}`"
                  class="w-full h-20 object-cover rounded-lg"
                />
              </div>
              <p class="text-xs text-gray-400 mt-2">
                Note: Media cannot be edited after posting
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div v-if="post && canEdit" class="p-6 border-t border-gray-800 flex justify-between items-center">
        <button
          @click="closeModal"
          class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
        >
          Cancel
        </button>
        
        <button
          @click="saveChanges"
          :disabled="!isContentValid || saving"
          class="px-6 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          <div v-if="saving" class="animate-spin rounded-full h-4 w-4 border-b-2 border-dark-bg"></div>
          {{ saving ? 'Saving...' : 'Save Changes' }}
        </button>
      </div>
    </div>
  </div>
</template>
