<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAIAssistantStore } from '../../stores/ai-assistant';

const router = useRouter();
const aiAssistantStore = useAIAssistantStore();
const prompt = ref('');
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Kraizer AI Assistant</h1>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">AI Assistant</h2>
        <p class="text-gray-400">This is a placeholder for the AI Assistant component.</p>
        
        <div class="mt-6">
          <div class="flex">
            <input 
              v-model="prompt"
              type="text"
              placeholder="Ask Kraizer something..."
              class="flex-grow px-4 py-2 rounded-l-md bg-dark-bg border border-gray-700 text-white focus:border-neon-purple focus:ring-1 focus:ring-neon-purple"
            />
            <button 
              class="px-4 py-2 bg-neon-purple text-white rounded-r-md hover:bg-neon-purple/90"
            >
              Ask
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
