import { defineS<PERSON> } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useNotificationsStore = defineStore('notifications', () => {
  // State
  const notifications = ref([]);
  const unreadCount = ref(0);
  const loading = ref(false);
  const error = ref(null);
  const page = ref(1);
  const hasMore = ref(true);

  // Getters
  const allNotifications = computed(() => notifications.value);
  const unreadNotifications = computed(() => 
    notifications.value.filter(notification => !notification.read)
  );

  // Fetch notifications
  async function fetchNotifications(reset = false) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      loading.value = true;
      error.value = null;

      if (reset) {
        page.value = 1;
        notifications.value = [];
      }

      const response = await fetch(`/.netlify/functions/notifications?page=${page.value}&limit=20`, {
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch notifications');
      }

      if (reset) {
        notifications.value = data.data.notifications;
      } else {
        notifications.value.push(...data.data.notifications);
      }

      unreadCount.value = data.data.unreadCount || 0;
      hasMore.value = data.data.hasMore || false;
      page.value += 1;

    } catch (err) {
      error.value = err.message;
      console.error('Error fetching notifications:', err);
    } finally {
      loading.value = false;
    }
  }

  // Mark notification as read
  async function markAsRead(notificationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch(`/.netlify/functions/notifications/${notificationId}/read`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      if (response.ok) {
        const notification = notifications.value.find(n => n._id === notificationId);
        if (notification && !notification.read) {
          notification.read = true;
          unreadCount.value = Math.max(0, unreadCount.value - 1);
        }
      }
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  }

  // Mark all notifications as read
  async function markAllAsRead() {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch('/.netlify/functions/notifications/read-all', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      if (response.ok) {
        notifications.value.forEach(notification => {
          notification.read = true;
        });
        unreadCount.value = 0;
      }
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  }

  // Delete notification
  async function deleteNotification(notificationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch(`/.netlify/functions/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      if (response.ok) {
        const index = notifications.value.findIndex(n => n._id === notificationId);
        if (index !== -1) {
          const notification = notifications.value[index];
          if (!notification.read) {
            unreadCount.value = Math.max(0, unreadCount.value - 1);
          }
          notifications.value.splice(index, 1);
        }
      }
    } catch (err) {
      console.error('Error deleting notification:', err);
    }
  }

  // Clear all notifications
  async function clearAllNotifications() {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch('/.netlify/functions/notifications/clear', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      if (response.ok) {
        notifications.value = [];
        unreadCount.value = 0;
      }
    } catch (err) {
      console.error('Error clearing notifications:', err);
    }
  }

  // Get notification icon based on type
  function getNotificationIcon(type) {
    const icons = {
      like: '❤️',
      comment: '💬',
      follow: '👤',
      mention: '@',
      story: '📖',
      message: '✉️',
      system: '🔔',
      premium: '⭐'
    };
    return icons[type] || '🔔';
  }

  // Format notification time
  function formatNotificationTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours}h ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  // Initialize notifications on store creation
  fetchNotifications(true);

  return {
    // State
    notifications,
    unreadCount,
    loading,
    error,
    hasMore,

    // Getters
    allNotifications,
    unreadNotifications,

    // Actions
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    getNotificationIcon,
    formatNotificationTime
  };
});
