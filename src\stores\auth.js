import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(JSON.parse(localStorage.getItem('user')) || null);
  const token = ref(localStorage.getItem('token') || null);
  const loading = ref(false);
  const error = ref(null);
  const lastLoginRedirect = ref(null);

  // Getters
  const isAuthenticated = computed(() => !!token.value);
  const isPremium = computed(() => user.value?.isPremium || false);
  const userProfile = computed(() => user.value);

  // Actions
  async function register(userData) {
    loading.value = true;
    error.value = null;

    try {
      console.log('Starting registration process for:', userData.email);

      // Validate input before sending to server
      if (!userData.email || !userData.username || !userData.password) {
        throw new Error('Email, username, and password are required');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(userData.email)) {
        throw new Error('Invalid email format');
      }

      // Validate password strength
      if (userData.password.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Validate username format
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
      if (!usernameRegex.test(userData.username)) {
        throw new Error('Username must be 3-20 characters and can only contain letters, numbers, and underscores');
      }

      console.log('Validation passed, sending registration request to backend');

      // Send registration request to backend
      const registerUrl = '/.netlify/functions/auth/register';
      console.log('Registration URL:', registerUrl);

      const response = await fetch(registerUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      console.log('Registration response status:', response.status);

      // Parse response
      let data;
      try {
        data = await response.json();
        console.log('Registration response data:', data);
      } catch (e) {
        console.error('Error parsing registration response:', e);
        throw new Error('Invalid response from server');
      }

      // Handle error responses
      if (!response.ok) {
        console.error('Registration failed with status:', response.status, 'Error:', data.error);
        throw new Error(data.error || 'Registration failed');
      }

      // Check if this is a background processing response
      if (data.data && data.data.status === 'processing') {
        console.log('Registration is being processed in the background');

        // Set temporary user data
        const tempUser = {
          email: userData.email,
          username: userData.username,
          profilePicture: null,
          isPremium: false,
          createdAt: new Date()
        };

        user.value = tempUser;

        // Don't set the token yet since it's not ready
        // Instead, redirect to a "registration pending" page or show a message
        return {
          user: tempUser,
          status: 'processing',
          message: 'Your registration is being processed. Please try logging in after a few moments.'
        };
      }

      // Validate response data for normal registration
      if (!data.data || !data.data.user || !data.data.token) {
        console.error('Invalid registration response data:', data);
        throw new Error('Invalid response data from server');
      }

      console.log('Registration successful for user:', data.data.user.username);

      // Set user and token
      user.value = data.data.user;
      token.value = data.data.token;

      // Save user and token to localStorage
      localStorage.setItem('user', JSON.stringify(data.data.user));
      localStorage.setItem('token', data.data.token);

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function login(credentials) {
    loading.value = true;
    error.value = null;

    try {
      // Validate input before sending to server
      if (!credentials.email || !credentials.password) {
        throw new Error('Email and password are required');
      }

      console.log('Attempting login with email:', credentials.email);

      // Send login request to backend
      const loginUrl = '/.netlify/functions/auth/login';
      console.log('Login request URL:', loginUrl);

      const response = await fetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(credentials)
      });

      console.log('Login response status:', response.status);

      // Parse response
      let data;
      try {
        data = await response.json();
        console.log('Login response data:', data);
      } catch (e) {
        console.error('Error parsing login response:', e);
        throw new Error('Invalid response from server');
      }

      // Handle error responses
      if (!response.ok) {
        console.error('Login failed with status:', response.status, 'Error:', data.error);
        throw new Error(data.error || 'Login failed');
      }

      // Validate response data
      if (!data.data || !data.data.user || !data.data.token) {
        console.error('Invalid login response data:', data);
        throw new Error('Invalid response data from server');
      }

      console.log('Login successful for user:', data.data.user.username);

      // Set user and token
      user.value = data.data.user;
      token.value = data.data.token;

      // Save user and token to localStorage
      localStorage.setItem('user', JSON.stringify(data.data.user));
      localStorage.setItem('token', data.data.token);

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function logout() {
    // Clear user and token
    user.value = null;
    token.value = null;

    // Remove data from localStorage
    localStorage.removeItem('token');
    localStorage.removeItem('user');

    // In a real implementation, we would also call the backend to invalidate the token
    console.log('User logged out successfully');
  }

  async function fetchCurrentUser() {
    if (!token.value) {
      console.log('No token available, skipping user fetch');
      return null;
    }

    loading.value = true;
    error.value = null;

    try {
      // Send request to get current user data
      const meUrl = '/.netlify/functions/auth/me';
      console.log('Fetching user profile from:', meUrl);
      console.log('Using token:', token.value ? 'Token exists' : 'No token');

      const response = await fetch(meUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token.value}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('User profile response status:', response.status);

      // Parse response
      let data;
      try {
        data = await response.json();
        console.log('User profile response data:', data);
      } catch (e) {
        console.error('Error parsing user profile response:', e);
        throw new Error('Invalid response from server');
      }

      // Handle error responses
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // Token is invalid, expired, or user is disabled
          console.warn('Authentication failed:', data.error);
          logout();
          throw new Error('Your session has expired. Please log in again.');
        }
        console.error('Failed to fetch user:', data.error);
        throw new Error(data.error || 'Failed to fetch user');
      }

      // Validate response data
      if (!data.data || !data.data.user) {
        console.error('Invalid user profile response data:', data);
        throw new Error('Invalid response data from server');
      }

      console.log('User profile fetch successful for:', data.data.user.username);

      // Set user
      user.value = data.data.user;

      // Update localStorage
      localStorage.setItem('user', JSON.stringify(data.data.user));

      // Check premium status expiration
      if (user.value.isPremium && user.value.premiumExpiry) {
        const expiryDate = new Date(user.value.premiumExpiry);
        const now = new Date();

        if (expiryDate < now) {
          console.log('Premium subscription has expired');
          user.value.isPremium = false;
          localStorage.setItem('user', JSON.stringify(user.value));
        }
      }

      return data.data.user;
    } catch (err) {
      error.value = err.message;

      // Don't throw for 401/403 errors after logout
      if (err.message === 'Your session has expired. Please log in again.') {
        return null;
      }

      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function updateProfile(profileData) {
    if (!token.value) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify(profileData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile');
      }

      // Refresh user data
      await fetchCurrentUser();

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function uploadProfilePicture(imageData, isPremiumAvatar = false) {
    if (!token.value) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/user/profile-picture', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token.value}`
        },
        body: JSON.stringify({
          image: imageData,
          isPremiumAvatar
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to upload profile picture');
      }

      // Refresh user data
      await fetchCurrentUser();

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  // Initialize by trying to fetch the current user if a token exists
  if (token.value) {
    fetchCurrentUser().catch(() => {
      // If fetching fails, clear the token
      logout();
    });
  }

  // Add an alias for fetchCurrentUser called fetchUserProfile for better naming consistency
  const fetchUserProfile = fetchCurrentUser;

  // Helper methods for redirect handling
  const setLoginRedirect = (path) => {
    lastLoginRedirect.value = path;
  };

  const getAndClearLoginRedirect = () => {
    const redirect = lastLoginRedirect.value;
    lastLoginRedirect.value = null;
    return redirect;
  };

  return {
    // State
    user,
    token,
    loading,
    error,
    lastLoginRedirect,

    // Getters
    isAuthenticated,
    isPremium,
    userProfile,

    // Actions
    register,
    login,
    logout,
    fetchCurrentUser,
    fetchUserProfile, // Add the alias
    updateProfile,
    uploadProfilePicture,
    setLoginRedirect,
    getAndClearLoginRedirect
  };
});
