<template>
  <div class="people-discovery">
    <div class="discovery-header">
      <h2>Discover People</h2>
      <div class="search-container">
        <div class="search-input-wrapper">
          <i class="fas fa-search search-icon"></i>
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder="Search for users..." 
            class="search-input"
            @input="handleSearch"
          />
          <button v-if="searchQuery" @click="clearSearch" class="clear-search-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="discovery-tabs">
      <button 
        @click="activeTab = 'recommended'" 
        class="tab-btn"
        :class="{ active: activeTab === 'recommended' }"
      >
        <i class="fas fa-user-friends"></i> Recommended
      </button>
      <button 
        @click="activeTab = 'trending'" 
        class="tab-btn"
        :class="{ active: activeTab === 'trending' }"
      >
        <i class="fas fa-fire"></i> Trending
      </button>
      <button 
        @click="activeTab = 'search'" 
        class="tab-btn"
        :class="{ active: activeTab === 'search' }"
        v-if="searchQuery || searchResults.length > 0"
      >
        <i class="fas fa-search"></i> Search Results
      </button>
    </div>

    <div class="discovery-content">
      <!-- Recommended Users -->
      <div v-if="activeTab === 'recommended'" class="users-section">
        <div v-if="loadingRecommended" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Finding people you might like...</p>
        </div>
        
        <div v-else-if="recommendedError" class="error-container">
          <p>{{ recommendedError }}</p>
          <button @click="fetchRecommended" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="recommendedUsers.length === 0" class="empty-users">
          <div class="empty-content">
            <i class="fas fa-users empty-icon"></i>
            <h3>No recommendations yet</h3>
            <p>Follow some users or update your interests to get personalized recommendations!</p>
          </div>
        </div>
        
        <div v-else class="users-grid">
          <user-card 
            v-for="user in recommendedUsers" 
            :key="user._id" 
            :user="user"
            :show-recommendation-score="true"
            @follow="handleFollow"
            @unfollow="handleUnfollow"
          />
        </div>
      </div>

      <!-- Trending Users -->
      <div v-if="activeTab === 'trending'" class="users-section">
        <div class="trending-info">
          <p>
            <i class="fas fa-info-circle"></i>
            Trending users are based on recent activity and engagement.
            <span v-if="!isPremium" class="premium-note">
              NTA+ users appear higher in search results.
            </span>
          </p>
        </div>
        
        <div v-if="loadingTrending" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading trending users...</p>
        </div>
        
        <div v-else-if="trendingError" class="error-container">
          <p>{{ trendingError }}</p>
          <button @click="fetchTrending" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="sortedTrendingUsers.length === 0" class="empty-users">
          <div class="empty-content">
            <i class="fas fa-chart-line empty-icon"></i>
            <h3>No trending users</h3>
            <p>Check back later to see who's trending on NeTuArk!</p>
          </div>
        </div>
        
        <div v-else class="users-grid">
          <user-card 
            v-for="(user, index) in sortedTrendingUsers" 
            :key="user._id" 
            :user="user"
            :show-trending-rank="true"
            :trending-rank="index + 1"
            @follow="handleFollow"
            @unfollow="handleUnfollow"
          />
        </div>
      </div>

      <!-- Search Results -->
      <div v-if="activeTab === 'search'" class="users-section">
        <div class="search-info">
          <p v-if="searchQuery">
            <i class="fas fa-search"></i>
            Search results for "{{ searchQuery }}"
            <span v-if="!isPremium" class="premium-note">
              (NTA+ users appear first)
            </span>
          </p>
        </div>
        
        <div v-if="searchingUsers" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Searching users...</p>
        </div>
        
        <div v-else-if="searchError" class="error-container">
          <p>{{ searchError }}</p>
          <button @click="searchUsers" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="searchResults.length === 0 && searchQuery" class="empty-users">
          <div class="empty-content">
            <i class="fas fa-search empty-icon"></i>
            <h3>No users found</h3>
            <p>Try searching with different keywords or check the spelling.</p>
          </div>
        </div>
        
        <div v-else-if="searchResults.length > 0" class="users-grid">
          <user-card 
            v-for="user in searchResults" 
            :key="user._id" 
            :user="user"
            @follow="handleFollow"
            @unfollow="handleUnfollow"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useDiscoveryStore } from '@/stores/discovery';
import { useSubscriptionStore } from '@/stores/subscription';
import { useUserStore } from '@/stores/user';
import UserCard from './UserCard.vue';

// Stores
const discoveryStore = useDiscoveryStore();
const subscriptionStore = useSubscriptionStore();
const userStore = useUserStore();

// State
const activeTab = ref('recommended');
const searchQuery = ref('');
const searchResults = ref([]);
const searchTimeout = ref(null);
const loadingRecommended = ref(true);
const loadingTrending = ref(false);
const searchingUsers = ref(false);
const recommendedError = ref(null);
const trendingError = ref(null);
const searchError = ref(null);

// Computed
const recommendedUsers = computed(() => discoveryStore.allRecommendedUsers);
const trendingUsers = computed(() => discoveryStore.allTrendingUsers);
const sortedTrendingUsers = computed(() => discoveryStore.sortedTrendingUsers);
const isPremium = computed(() => subscriptionStore.isPremium);

// Methods
async function fetchRecommended() {
  loadingRecommended.value = true;
  recommendedError.value = null;
  
  try {
    await discoveryStore.fetchRecommendedUsers();
  } catch (err) {
    recommendedError.value = err.message || 'Failed to load recommended users';
  } finally {
    loadingRecommended.value = false;
  }
}

async function fetchTrending() {
  loadingTrending.value = true;
  trendingError.value = null;
  
  try {
    await discoveryStore.fetchTrendingUsers();
  } catch (err) {
    trendingError.value = err.message || 'Failed to load trending users';
  } finally {
    loadingTrending.value = false;
  }
}

function handleSearch() {
  // Clear existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  // Set new timeout for debounced search
  searchTimeout.value = setTimeout(() => {
    if (searchQuery.value.trim().length >= 2) {
      searchUsers();
      activeTab.value = 'search';
    } else {
      searchResults.value = [];
      if (activeTab.value === 'search') {
        activeTab.value = 'recommended';
      }
    }
  }, 300);
}

async function searchUsers() {
  if (!searchQuery.value.trim()) return;
  
  searchingUsers.value = true;
  searchError.value = null;
  
  try {
    const results = await discoveryStore.searchUsers(searchQuery.value);
    searchResults.value = results;
  } catch (err) {
    searchError.value = err.message || 'Failed to search users';
  } finally {
    searchingUsers.value = false;
  }
}

function clearSearch() {
  searchQuery.value = '';
  searchResults.value = [];
  searchError.value = null;
  
  if (activeTab.value === 'search') {
    activeTab.value = 'recommended';
  }
}

async function handleFollow(user) {
  try {
    await userStore.followUser(user._id, true); // Follow with content view
    
    // Update user in all lists
    updateUserInLists(user._id, { isFollowing: true });
  } catch (err) {
    console.error('Failed to follow user:', err);
  }
}

async function handleUnfollow(user) {
  try {
    await userStore.unfollowUser(user._id);
    
    // Update user in all lists
    updateUserInLists(user._id, { isFollowing: false });
  } catch (err) {
    console.error('Failed to unfollow user:', err);
  }
}

function updateUserInLists(userId, updates) {
  // Update in recommended users
  const recommendedIndex = recommendedUsers.value.findIndex(u => u._id === userId);
  if (recommendedIndex !== -1) {
    Object.assign(recommendedUsers.value[recommendedIndex], updates);
  }
  
  // Update in trending users
  const trendingIndex = trendingUsers.value.findIndex(u => u._id === userId);
  if (trendingIndex !== -1) {
    Object.assign(trendingUsers.value[trendingIndex], updates);
  }
  
  // Update in search results
  const searchIndex = searchResults.value.findIndex(u => u._id === userId);
  if (searchIndex !== -1) {
    Object.assign(searchResults.value[searchIndex], updates);
  }
}

// Watch for tab changes
watch(activeTab, (newTab) => {
  if (newTab === 'trending' && trendingUsers.value.length === 0) {
    fetchTrending();
  }
});

// Lifecycle hooks
onMounted(() => {
  fetchRecommended();
});
</script>

<style scoped>
.people-discovery {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.discovery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.discovery-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: #777;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
}

.search-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.clear-search-btn {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
}

.clear-search-btn:hover {
  background-color: #f0f0f0;
}

.discovery-tabs {
  display: flex;
  border-bottom: 1px solid #ddd;
  margin-bottom: 1rem;
  overflow-x: auto;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.tab-btn.active {
  border-bottom-color: #3498db;
  color: #3498db;
}

.tab-btn:hover {
  background-color: #f0f0f0;
}

.discovery-content {
  min-height: 400px;
}

.trending-info, .search-info {
  background-color: #f8f9fa;
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: #555;
}

.premium-note {
  color: #f39c12;
  font-weight: bold;
}

.loading-container, .error-container, .empty-users {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 3rem;
  color: #95a5a6;
  margin-bottom: 1rem;
}

.empty-content h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.empty-content p {
  margin: 0;
  color: #777;
  max-width: 300px;
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.retry-button:hover {
  background-color: #2980b9;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .discovery-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-container {
    max-width: none;
  }
  
  .users-grid {
    grid-template-columns: 1fr;
  }
}
</style>
