<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { useUserStore } from '../../stores/user';

const router = useRouter();
const authStore = useAuthStore();
const userStore = useUserStore();

// Reactive data
const loading = ref(true);
const error = ref(null);
const activeTab = ref('users');
const searchQuery = ref('');
const suggestedUsers = ref([]);
const trendingPosts = ref([]);
const trendingHashtags = ref([]);
const followingUsers = ref(false);

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Computed
const filteredUsers = computed(() => {
  if (!searchQuery.value) return suggestedUsers.value;

  return suggestedUsers.value.filter(user =>
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.displayName?.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.bio?.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const filteredPosts = computed(() => {
  if (!searchQuery.value) return trendingPosts.value;

  return trendingPosts.value.filter(post =>
    post.content.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    post.author.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    post.aiTags?.some(tag => tag.toLowerCase().includes(searchQuery.value.toLowerCase()))
  );
});

// Methods
const fetchDiscoverData = async () => {
  try {
    loading.value = true;
    error.value = null;

    // Fetch suggested users
    const usersResponse = await fetch('/.netlify/functions/discover/users', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      suggestedUsers.value = usersData.data.users || [];
    }

    // Fetch trending posts
    const postsResponse = await fetch('/.netlify/functions/discover/trending', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (postsResponse.ok) {
      const postsData = await postsResponse.json();
      trendingPosts.value = postsData.data.posts || [];
    }

    // Fetch trending hashtags
    const hashtagsResponse = await fetch('/.netlify/functions/discover/hashtags', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (hashtagsResponse.ok) {
      const hashtagsData = await hashtagsResponse.json();
      trendingHashtags.value = hashtagsData.data.hashtags || [];
    }

  } catch (err) {
    console.error('Error fetching discover data:', err);
    error.value = 'Failed to load discover content. Please try again.';
  } finally {
    loading.value = false;
  }
};

const followUser = async (userId) => {
  if (!authStore.isAuthenticated || followingUsers.value) return;

  followingUsers.value = true;

  try {
    const response = await fetch('/.netlify/functions/user/follow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ userId })
    });

    if (response.ok) {
      // Update the user's follow status in the list
      const userIndex = suggestedUsers.value.findIndex(user => user._id === userId);
      if (userIndex !== -1) {
        suggestedUsers.value[userIndex].isFollowing = true;
        suggestedUsers.value[userIndex].followersCount += 1;
      }
    }
  } catch (err) {
    console.error('Error following user:', err);
  } finally {
    followingUsers.value = false;
  }
};

const unfollowUser = async (userId) => {
  if (!authStore.isAuthenticated || followingUsers.value) return;

  followingUsers.value = true;

  try {
    const response = await fetch('/.netlify/functions/user/unfollow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ userId })
    });

    if (response.ok) {
      // Update the user's follow status in the list
      const userIndex = suggestedUsers.value.findIndex(user => user._id === userId);
      if (userIndex !== -1) {
        suggestedUsers.value[userIndex].isFollowing = false;
        suggestedUsers.value[userIndex].followersCount -= 1;
      }
    }
  } catch (err) {
    console.error('Error unfollowing user:', err);
  } finally {
    followingUsers.value = false;
  }
};

const viewProfile = (username) => {
  router.push(`/profile/${username}`);
};

const viewPost = (postId) => {
  router.push(`/post/${postId}`);
};

const startConversation = (username) => {
  router.push(`/messages/new?user=${username}`);
};

const formatTime = (dateString) => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now - date;

  if (diff < 60000) return 'Just now';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}d ago`;

  return date.toLocaleDateString();
};

// Lifecycle
onMounted(() => {
  fetchDiscoverData();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Discover</h1>
        </div>
        <router-link to="/search" class="text-gray-300 hover:text-white transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </router-link>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Search Bar -->
      <div class="mb-6">
        <div class="relative">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search users, posts, or hashtags..."
            class="w-full bg-darker-bg border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors"
          />
        </div>
      </div>

      <!-- Tabs -->
      <div class="mb-6">
        <div class="bg-darker-bg rounded-xl border border-gray-800">
          <nav class="flex">
            <button
              @click="activeTab = 'users'"
              :class="[
                'flex-1 px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                activeTab === 'users'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-white'
              ]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              People
            </button>
            <button
              @click="activeTab = 'posts'"
              :class="[
                'flex-1 px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                activeTab === 'posts'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-white'
              ]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
              </svg>
              Trending
            </button>
            <button
              @click="activeTab = 'hashtags'"
              :class="[
                'flex-1 px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                activeTab === 'hashtags'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-gray-400 hover:text-white'
              ]"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
              </svg>
              Tags
            </button>
          </nav>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="space-y-4">
        <div v-for="i in 3" :key="i" class="bg-darker-bg rounded-xl border border-gray-800 p-6 animate-pulse">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gray-700 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-700 rounded w-1/4"></div>
              <div class="h-3 bg-gray-700 rounded w-3/4"></div>
            </div>
            <div class="h-8 bg-gray-700 rounded w-20"></div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500/30 rounded-xl p-6 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-red-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-lg font-medium text-red-400 mb-2">{{ error }}</h3>
        <button
          @click="fetchDiscoverData"
          class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>

      <!-- Content -->
      <div v-else>
        <!-- Users Tab -->
        <div v-if="activeTab === 'users'">
          <div v-if="filteredUsers.length === 0" class="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-300 mb-2">
              {{ searchQuery ? 'No users found' : 'No suggested users' }}
            </h3>
            <p class="text-gray-400">
              {{ searchQuery ? 'Try a different search term' : 'Check back later for new suggestions!' }}
            </p>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="user in filteredUsers"
              :key="user._id"
              class="bg-darker-bg rounded-xl border border-gray-800 p-6 hover:border-gray-700 transition-colors"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <!-- Profile Picture -->
                  <div class="relative cursor-pointer" @click="viewProfile(user.username)">
                    <img
                      :src="user.profilePicture || getDefaultAvatar()"
                      :alt="user.username"
                      class="w-16 h-16 rounded-full object-cover"
                    />
                    <div v-if="user.isPremium" class="absolute -top-1 -right-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-dark-bg" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                  </div>

                  <!-- User Info -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center space-x-2 mb-1">
                      <h3 class="font-medium text-white truncate cursor-pointer hover:text-primary transition-colors" @click="viewProfile(user.username)">
                        {{ user.displayName || user.username }}
                      </h3>
                      <span v-if="user.isPremium" class="text-xs text-primary">NTA+</span>
                      <span v-if="user.isVerified" class="text-primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </span>
                    </div>
                    <p class="text-gray-400 text-sm cursor-pointer hover:text-gray-300 transition-colors" @click="viewProfile(user.username)">@{{ user.username }}</p>
                    <p v-if="user.bio" class="text-gray-300 text-sm mt-1 line-clamp-2">{{ user.bio }}</p>
                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                      <span>{{ user.followersCount || 0 }} followers</span>
                      <span>{{ user.followingCount || 0 }} following</span>
                      <span>{{ user.postsCount || 0 }} posts</span>
                    </div>
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center space-x-2">
                  <button
                    @click="startConversation(user.username)"
                    class="p-2 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-lg transition-colors"
                    title="Send Message"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </button>

                  <button
                    v-if="!user.isFollowing"
                    @click="followUser(user._id)"
                    :disabled="followingUsers"
                    class="px-4 py-2 bg-primary text-dark-bg rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors disabled:opacity-50"
                  >
                    Follow
                  </button>

                  <button
                    v-else
                    @click="unfollowUser(user._id)"
                    :disabled="followingUsers"
                    class="px-4 py-2 bg-gray-600 text-white rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
                  >
                    Following
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Posts Tab -->
        <div v-else-if="activeTab === 'posts'">
          <div v-if="filteredPosts.length === 0" class="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
            </svg>
            <h3 class="text-lg font-medium text-gray-300 mb-2">
              {{ searchQuery ? 'No posts found' : 'No trending posts' }}
            </h3>
            <p class="text-gray-400">
              {{ searchQuery ? 'Try a different search term' : 'Check back later for trending content!' }}
            </p>
          </div>

          <div v-else class="space-y-6">
            <div
              v-for="post in filteredPosts"
              :key="post._id"
              class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden hover:border-gray-700 transition-colors cursor-pointer"
              @click="viewPost(post._id)"
            >
              <!-- Post Header -->
              <div class="p-4 flex items-center">
                <div class="flex items-center cursor-pointer" @click.stop="viewProfile(post.author.username)">
                  <img
                    :src="post.author.profilePicture || getDefaultAvatar()"
                    :alt="post.author.username"
                    class="w-10 h-10 rounded-full object-cover mr-3"
                  />
                  <div>
                    <div class="font-medium flex items-center">
                      {{ post.author.username }}
                      <span v-if="post.author.isPremium" class="ml-1 text-xs text-primary">NTA+</span>
                    </div>
                    <div class="text-xs text-gray-400">{{ formatTime(post.createdAt) }}</div>
                  </div>
                </div>
              </div>

              <!-- Post Content -->
              <div class="px-4 pb-4">
                <p class="text-white mb-3">{{ post.content }}</p>

                <!-- Media -->
                <div v-if="post.media && post.media.length > 0" class="mb-3">
                  <img
                    v-if="post.media[0].type === 'image'"
                    :src="post.media[0].url"
                    :alt="post.content"
                    class="w-full max-h-64 object-cover rounded-lg"
                  />
                  <video
                    v-else
                    :src="post.media[0].url"
                    controls
                    class="w-full max-h-64 object-cover rounded-lg"
                  ></video>
                </div>

                <!-- AI Tags -->
                <div v-if="post.aiTags && post.aiTags.length > 0" class="flex flex-wrap gap-2 mb-3">
                  <span
                    v-for="tag in post.aiTags.slice(0, 3)"
                    :key="tag"
                    class="px-2 py-1 rounded-full text-xs bg-primary/10 text-primary border border-primary/30"
                  >
                    #{{ tag }}
                  </span>
                  <span v-if="post.aiTags.length > 3" class="text-xs text-gray-400">
                    +{{ post.aiTags.length - 3 }} more
                  </span>
                </div>

                <!-- Post Stats -->
                <div class="flex items-center justify-between text-sm text-gray-400">
                  <div class="flex items-center space-x-4">
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      {{ post.likes?.length || 0 }}
                    </span>
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      {{ post.comments?.length || 0 }}
                    </span>
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      {{ post.viewCount || 0 }}
                    </span>
                  </div>
                  <span class="text-xs">Trending</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Hashtags Tab -->
        <div v-else-if="activeTab === 'hashtags'">
          <div v-if="trendingHashtags.length === 0" class="text-center py-12">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
            </svg>
            <h3 class="text-lg font-medium text-gray-300 mb-2">No trending hashtags</h3>
            <p class="text-gray-400">Check back later for trending topics!</p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-for="hashtag in trendingHashtags"
              :key="hashtag.tag"
              class="bg-darker-bg rounded-xl border border-gray-800 p-6 hover:border-gray-700 transition-colors cursor-pointer"
              @click="router.push(`/search?q=${encodeURIComponent('#' + hashtag.tag)}`)"
            >
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-medium text-primary">#{{ hashtag.tag }}</h3>
                <span class="text-xs text-gray-400 bg-gray-800 px-2 py-1 rounded-full">
                  Trending
                </span>
              </div>
              <p class="text-gray-400 text-sm mb-2">{{ hashtag.postCount }} posts</p>
              <div class="text-xs text-gray-500">
                {{ hashtag.description || 'Trending topic on NeTuArk' }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
