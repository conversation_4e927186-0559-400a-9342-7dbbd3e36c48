import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useUserStore = defineStore('user', () => {
  // State
  const profile = ref(null);
  const blacklistedUsers = ref([]);
  const privacySettings = ref({
    postVisibility: 'public',
    allowFollowWithoutView: false,
    allowMentions: true,
    allowDirectMessages: true
  });
  const loading = ref(false);
  const error = ref(null);
  
  // Getters
  const userProfile = computed(() => profile.value);
  const userBlacklist = computed(() => blacklistedUsers.value);
  const userPrivacySettings = computed(() => privacySettings.value);
  
  // Check if a user is blacklisted
  const isUserBlacklisted = computed(() => (userId) => {
    return blacklistedUsers.value.some(user => user._id === userId);
  });
  
  // Actions
  
  // Fetch user profile by ID
  async function fetchUserProfile(userId) {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch(`/.netlify/functions/user/profile/${userId}`);
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch user profile');
      }
      
      profile.value = data.data.user;
      return data.data.user;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch blacklisted users
  async function fetchBlacklistedUsers() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/blacklist', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch blacklisted users');
      }
      
      blacklistedUsers.value = data.data.users;
      return data.data.users;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Add user to blacklist
  async function blacklistUser(userId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/blacklist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to blacklist user');
      }
      
      // Refresh blacklisted users
      await fetchBlacklistedUsers();
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Remove user from blacklist
  async function removeFromBlacklist(userId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/blacklist', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to remove user from blacklist');
      }
      
      // Refresh blacklisted users
      await fetchBlacklistedUsers();
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch privacy settings
  async function fetchPrivacySettings() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/privacy', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch privacy settings');
      }
      
      privacySettings.value = data.data.settings;
      return data.data.settings;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Update privacy settings
  async function updatePrivacySettings(settings) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/privacy', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ settings })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to update privacy settings');
      }
      
      privacySettings.value = data.data.settings;
      return data.data.settings;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Follow a user
  async function followUser(userId, viewContent = true) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/follow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userId, viewContent })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to follow user');
      }
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Unfollow a user
  async function unfollowUser(userId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/user/unfollow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to unfollow user');
      }
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  return {
    // State
    profile,
    blacklistedUsers,
    privacySettings,
    loading,
    error,
    
    // Getters
    userProfile,
    userBlacklist,
    userPrivacySettings,
    isUserBlacklisted,
    
    // Actions
    fetchUserProfile,
    fetchBlacklistedUsers,
    blacklistUser,
    removeFromBlacklist,
    fetchPrivacySettings,
    updatePrivacySettings,
    followUser,
    unfollowUser
  };
});
