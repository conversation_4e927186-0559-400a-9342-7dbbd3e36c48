<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/logo1.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="NeTuArk - The World Is Opening. A modern social platform built with Vue.js, featuring AI-driven content, real-time messaging, stories, and premium features." />
    <meta name="theme-color" content="#00d4ff" />
    <title>NeTuArk - The World Is Opening</title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/logo1.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/logo1.png" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="NeTuArk" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#00d4ff" />
    <meta name="msapplication-TileImage" content="/icon-144.svg" />

    <!-- Fallback icon -->
    <link rel="icon" type="image/png" sizes="32x32" href="/logo1.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/logo1.png" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://netuark.netlify.app/" />
    <meta property="og:title" content="NeTuArk - The World Is Opening" />
    <meta property="og:description" content="A modern social platform where the world is opening. Connect, share, and discover with AI-powered features." />
    <meta property="og:image" content="/logo2.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://netuark.netlify.app/" />
    <meta property="twitter:title" content="NeTuArk - The World Is Opening" />
    <meta property="twitter:description" content="A modern social platform where the world is opening. Connect, share, and discover with AI-powered features." />
    <meta property="twitter:image" content="/logo2.png" />

    <!-- Preconnect to external resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  </head>
  <body class="font-['Inter',sans-serif] bg-dark-bg">
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
