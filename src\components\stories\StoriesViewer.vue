<template>
  <div class="stories-viewer">
    <!-- Stories Carousel -->
    <div class="stories-carousel" v-if="!activeStory">
      <div class="carousel-header">
        <h2>Stories</h2>
        <button @click="openCreateStory" class="create-story-btn">
          <i class="fas fa-plus"></i> Create
        </button>
      </div>

      <div class="carousel-container">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading stories...</p>
        </div>

        <div v-else-if="error" class="error-container">
          <p>{{ error }}</p>
          <button @click="fetchStories" class="retry-button">Retry</button>
        </div>

        <div v-else-if="stories.length === 0" class="empty-stories">
          <p>No stories to display</p>
          <button @click="openCreateStory" class="create-story-btn">Create Your First Story</button>
        </div>

        <div v-else class="stories-list">
          <div
            v-for="(item, index) in carouselItems"
            :key="item.id"
            class="story-item"
            @click="item.type === 'story' ? viewStory(item.story) : null"
          >
            <!-- Ad item -->
            <div v-if="item.type === 'ad'" class="story-ad">
              <div class="ad-label">Ad</div>
              <img :src="item.ad.imageUrl" :alt="item.ad.title" class="story-thumbnail" />
              <div class="story-info">
                <div class="story-title">{{ item.ad.title }}</div>
              </div>
            </div>

            <!-- Story item -->
            <div v-else class="story-preview">
              <div class="story-ring" :class="{ 'viewed': item.story.viewed }">
                <img :src="item.story.thumbnailUrl || item.story.author.profilePicture" :alt="item.story.author.username" class="story-thumbnail" />
              </div>
              <div class="story-info">
                <div class="story-username">{{ item.story.author.username }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Story Viewer -->
    <div v-if="activeStory" class="story-viewer">
      <div class="story-header">
        <div class="story-author">
          <img :src="activeStory.author.profilePicture" :alt="activeStory.author.username" class="author-avatar" />
          <div class="author-info">
            <div class="author-username">{{ activeStory.author.username }}</div>
            <div class="story-time">{{ formatTime(activeStory.createdAt) }}</div>
          </div>
        </div>

        <div class="story-actions">
          <button @click="closeStory" class="close-story-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="story-progress">
        <div
          v-for="(story, index) in userStories"
          :key="story._id"
          class="progress-bar"
          :class="{ 'active': index === currentStoryIndex, 'completed': index < currentStoryIndex }"
        >
          <div class="progress-fill" :style="progressStyle"></div>
        </div>
      </div>

      <div class="story-content" @click="advanceStory">
        <img v-if="activeStory.mediaType === 'image'" :src="activeStory.mediaUrl" alt="Story" class="story-media" />
        <video v-else-if="activeStory.mediaType === 'video'" :src="activeStory.mediaUrl" autoplay muted class="story-media"></video>

        <div v-if="activeStory.caption" class="story-caption">
          {{ activeStory.caption }}
        </div>
      </div>

      <div class="story-navigation">
        <button
          @click.stop="previousStory"
          class="nav-btn prev-btn"
          :disabled="currentStoryIndex === 0"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        <button
          @click.stop="nextStory"
          class="nav-btn next-btn"
          :disabled="currentStoryIndex === userStories.length - 1"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Create Story Modal -->
    <div v-if="showCreateModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Create Story</h3>
          <button @click="closeCreateModal" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="create-story-form">
            <div class="media-upload">
              <label for="story-media-upload" class="media-upload-label" :class="{ 'uploading': isStoryUploading }">
                <div v-if="!storyMedia" class="upload-placeholder">
                  <i class="fas fa-cloud-upload-alt" v-if="!isStoryUploading"></i>
                  <i class="fas fa-spinner fa-spin" v-if="isStoryUploading"></i>
                  <span v-if="!isStoryUploading">Upload Photo or Video</span>
                  <span v-if="isStoryUploading">Processing...</span>
                  <div class="upload-info">
                    <small>Max 20MB • JPEG, PNG, GIF, WebP, MP4, WebM</small>
                  </div>
                </div>
                <div v-else class="media-preview">
                  <img v-if="storyMedia.type.startsWith('image/')" :src="storyMediaPreview" alt="Story preview" class="preview-image" />
                  <video v-else-if="storyMedia.type.startsWith('video/')" :src="storyMediaPreview" controls class="preview-video"></video>
                  <div class="media-info">
                    <span class="file-name">{{ storyMedia.name }}</span>
                    <span class="file-size">{{ formatFileSize(storyMedia.size) }}</span>
                    <button @click="removeStoryMedia" class="remove-media-btn" type="button">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </label>
              <input
                type="file"
                id="story-media-upload"
                accept="image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/webm"
                @change="handleStoryMediaUpload"
                class="media-upload-input"
                :disabled="isStoryUploading"
              />
            </div>

            <div class="story-caption-input">
              <textarea
                v-model="storyCaption"
                placeholder="Add a caption..."
                rows="2"
                maxlength="200"
              ></textarea>
              <div class="character-count">
                {{ storyCaption.length }}/200
              </div>
            </div>

            <div v-if="storyError" class="error-message">
              {{ storyError }}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeCreateModal" class="cancel-btn">Cancel</button>
          <button
            @click="createStory"
            class="create-btn"
            :disabled="isCreatingStory || !storyMedia"
          >
            <span v-if="isCreatingStory">
              <i class="fas fa-spinner fa-spin"></i> Creating...
            </span>
            <span v-else>Create Story</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useStoriesStore } from '@/stores/stories';
import { useAdsStore } from '@/stores/ads';
import { useSubscriptionStore } from '@/stores/subscription';

// Stores
const storiesStore = useStoriesStore();
const adsStore = useAdsStore();
const subscriptionStore = useSubscriptionStore();

// State
const stories = ref([]);
const loading = ref(true);
const error = ref(null);
const activeStory = ref(null);
const userStories = ref([]);
const currentStoryIndex = ref(0);
const storyProgress = ref(0);
const storyInterval = ref(null);
const storyDuration = 5000; // 5 seconds per story

// Create story state
const showCreateModal = ref(false);
const storyMedia = ref(null);
const storyMediaPreview = ref('');
const storyCaption = ref('');
const storyError = ref(null);
const isCreatingStory = ref(false);
const isStoryUploading = ref(false);
const maxStoryFileSize = 20 * 1024 * 1024; // 20MB

// Computed
const carouselItems = computed(() => {
  const items = [];

  // Group stories by user
  const userStoriesMap = {};

  stories.value.forEach(story => {
    const authorId = story.author._id;
    if (!userStoriesMap[authorId]) {
      userStoriesMap[authorId] = [];
    }
    userStoriesMap[authorId].push(story);
  });

  // Add user stories to items
  Object.values(userStoriesMap).forEach((userStories, index) => {
    // Sort stories by creation date (newest first)
    userStories.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Add the first story from each user
    items.push({
      id: `story-${userStories[0]._id}`,
      type: 'story',
      story: userStories[0]
    });

    // Add ad every 4 stories
    if ((index + 1) % 4 === 0) {
      const ad = adsStore.getAdForStory(index);
      if (ad) {
        items.push({
          id: `ad-${ad._id}`,
          type: 'ad',
          ad
        });
      }
    }
  });

  return items;
});

const progressStyle = computed(() => {
  return {
    width: `${storyProgress.value}%`
  };
});

// Methods
async function fetchStories() {
  loading.value = true;
  error.value = null;

  try {
    // Fetch stories
    const fetchedStories = await storiesStore.fetchStories();
    stories.value = fetchedStories;

    // Fetch ads
    await adsStore.fetchStoryAds();
  } catch (err) {
    error.value = err.message || 'Failed to load stories';
  } finally {
    loading.value = false;
  }
}

function viewStory(story) {
  // Find all stories from this user
  const authorId = story.author._id;
  userStories.value = stories.value.filter(s => s.author._id === authorId);

  // Sort by creation date (oldest first for viewing)
  userStories.value.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));

  // Find index of the selected story
  const storyIndex = userStories.value.findIndex(s => s._id === story._id);
  currentStoryIndex.value = storyIndex >= 0 ? storyIndex : 0;

  // Set active story
  activeStory.value = userStories.value[currentStoryIndex.value];

  // Start progress
  startStoryProgress();

  // Mark as viewed
  storiesStore.markAsViewed(activeStory.value._id);
}

function closeStory() {
  activeStory.value = null;
  userStories.value = [];
  currentStoryIndex.value = 0;
  storyProgress.value = 0;

  // Clear interval
  if (storyInterval.value) {
    clearInterval(storyInterval.value);
    storyInterval.value = null;
  }
}

function startStoryProgress() {
  // Clear any existing interval
  if (storyInterval.value) {
    clearInterval(storyInterval.value);
  }

  // Reset progress
  storyProgress.value = 0;

  // Start new interval
  const startTime = Date.now();
  storyInterval.value = setInterval(() => {
    const elapsed = Date.now() - startTime;
    storyProgress.value = (elapsed / storyDuration) * 100;

    if (storyProgress.value >= 100) {
      nextStory();
    }
  }, 100);
}

function previousStory() {
  if (currentStoryIndex.value > 0) {
    currentStoryIndex.value--;
    activeStory.value = userStories.value[currentStoryIndex.value];
    startStoryProgress();
    storiesStore.markAsViewed(activeStory.value._id);
  }
}

function nextStory() {
  if (currentStoryIndex.value < userStories.value.length - 1) {
    currentStoryIndex.value++;
    activeStory.value = userStories.value[currentStoryIndex.value];
    startStoryProgress();
    storiesStore.markAsViewed(activeStory.value._id);
  } else {
    closeStory();
  }
}

function advanceStory() {
  nextStory();
}

function formatTime(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now - date;

  // Less than a minute
  if (diff < 60000) {
    return 'Just now';
  }

  // Less than an hour
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}m ago`;
  }

  // Less than a day
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}h ago`;
  }

  // Format as date
  return date.toLocaleDateString();
}

function openCreateStory() {
  showCreateModal.value = true;
}

function closeCreateModal() {
  showCreateModal.value = false;
  storyMedia.value = null;
  storyMediaPreview.value = '';
  storyCaption.value = '';
  storyError.value = null;
}

async function handleStoryMediaUpload(event) {
  const file = event.target.files[0];

  if (!file) return;

  isStoryUploading.value = true;
  storyError.value = null;

  try {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/webm'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error(`Unsupported file type: ${file.type}. Allowed types: JPEG, PNG, GIF, WebP, MP4, QuickTime, WebM`);
    }

    // Check file size
    if (file.size > maxStoryFileSize) {
      throw new Error(`File size exceeds ${formatFileSize(maxStoryFileSize)} limit`);
    }

    // Create preview
    const preview = await createFilePreview(file);

    // Set story media
    storyMedia.value = {
      ...file,
      name: file.name,
      size: file.size,
      type: file.type
    };
    storyMediaPreview.value = preview;

  } catch (err) {
    storyError.value = err.message;
  } finally {
    isStoryUploading.value = false;
    // Reset input
    event.target.value = '';
  }
}

function createFilePreview(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeStoryMedia() {
  storyMedia.value = null;
  storyMediaPreview.value = '';
  storyError.value = null;
}

async function createStory() {
  if (!storyMedia.value || isCreatingStory.value) return;

  isCreatingStory.value = true;
  storyError.value = null;

  try {
    // Create story
    await storiesStore.createStory({
      media: {
        data: storyMediaPreview.value,
        type: storyMedia.value.type
      },
      caption: storyCaption.value
    });

    // Close modal
    closeCreateModal();

    // Refresh stories
    await fetchStories();
  } catch (err) {
    storyError.value = err.message || 'Failed to create story';
  } finally {
    isCreatingStory.value = false;
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchStories();
});

onUnmounted(() => {
  // Clear interval
  if (storyInterval.value) {
    clearInterval(storyInterval.value);
  }
});

// Watch for active story changes
watch(activeStory, (newStory) => {
  if (newStory) {
    // If it's a video, adjust the duration based on video length
    if (newStory.mediaType === 'video') {
      // This would need to be implemented with actual video duration
      // For now, we'll use a longer duration for videos
      storyDuration = 10000; // 10 seconds
    } else {
      storyDuration = 5000; // 5 seconds for images
    }
  }
});
</script>

<style scoped>
.stories-viewer {
  width: 100%;
}

.stories-carousel {
  margin-bottom: 1rem;
}

.carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.carousel-header h2 {
  margin: 0;
  font-size: 1.2rem;
}

.create-story-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.create-story-btn:hover {
  background-color: #2980b9;
}

.loading-container, .error-container, .empty-stories {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  text-align: center;
  padding: 1rem;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.stories-list {
  display: flex;
  overflow-x: auto;
  gap: 1rem;
  padding: 0.5rem 0;
  scrollbar-width: thin;
}

.stories-list::-webkit-scrollbar {
  height: 6px;
}

.stories-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.story-item {
  flex: 0 0 auto;
  width: 80px;
  cursor: pointer;
}

.story-preview, .story-ad {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.story-ring {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  padding: 2px;
  background: linear-gradient(45deg, #3498db, #8e44ad);
}

.story-ring.viewed {
  background: #95a5a6;
}

.story-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid white;
}

.story-info {
  margin-top: 0.5rem;
  text-align: center;
  width: 100%;
  overflow: hidden;
}

.story-username, .story-title {
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ad-label {
  font-size: 0.7rem;
  color: #777;
  margin-bottom: 0.25rem;
}

/* Story Viewer */
.story-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.story-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  color: white;
}

.story-author {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.author-username {
  font-weight: bold;
}

.story-time {
  font-size: 0.8rem;
  opacity: 0.8;
}

.close-story-btn {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

.story-progress {
  display: flex;
  gap: 0.25rem;
  padding: 0 1rem;
}

.progress-bar {
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  flex: 1;
  border-radius: 1.5px;
  overflow: hidden;
}

.progress-bar.active .progress-fill {
  height: 100%;
  background-color: white;
  transition: width 0.1s linear;
}

.progress-bar.completed {
  background-color: white;
}

.story-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.story-media {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.story-caption {
  position: absolute;
  bottom: 2rem;
  left: 1rem;
  right: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 1rem;
}

.story-navigation {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  pointer-events: none;
}

.nav-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  font-size: 2rem;
  cursor: pointer;
  padding: 1rem;
  pointer-events: auto;
}

.nav-btn:disabled {
  opacity: 0;
  pointer-events: none;
}

/* Create Story Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

.create-story-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.media-upload {
  width: 100%;
}

.media-upload-label {
  display: block;
  cursor: pointer;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  background-color: #f0f0f0;
  border: 2px dashed #ddd;
  border-radius: 8px;
  gap: 0.5rem;
}

.upload-placeholder i {
  font-size: 2rem;
  color: #777;
}

.media-preview {
  width: 100%;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image, .preview-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-color: #f0f0f0;
}

.media-upload-input {
  display: none;
}

.story-caption-input {
  position: relative;
}

.story-caption-input textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 1rem;
}

.character-count {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  font-size: 0.8rem;
  color: #777;
}

.error-message {
  color: #e74c3c;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #eee;
}

.cancel-btn, .create-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.create-btn {
  background-color: #3498db;
  color: white;
  border: none;
}

.create-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.create-btn:not(:disabled):hover {
  background-color: #2980b9;
}
</style>
