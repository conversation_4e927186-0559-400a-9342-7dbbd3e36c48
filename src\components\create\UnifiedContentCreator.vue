<template>
  <div class="unified-creator">
    <!-- Header with mode toggle -->
    <div class="creator-header">
      <div class="mode-toggle">
        <button
          @click="setMode('post')"
          :class="['mode-btn', { active: mode === 'post' }]"
        >
          <i class="fas fa-edit"></i>
          <span>Post</span>
        </button>
        <button
          @click="setMode('story')"
          :class="['mode-btn', { active: mode === 'story' }]"
        >
          <i class="fas fa-clock"></i>
          <span>Story</span>
        </button>
      </div>
      <div class="mode-indicator">
        <div class="indicator-line" :class="{ 'story-mode': mode === 'story' }"></div>
      </div>
    </div>

    <!-- Content creation area -->
    <div class="creator-body">
      <!-- Content input -->
      <div class="content-section">
        <div class="content-input-wrapper">
          <textarea
            v-model="content"
            :placeholder="mode === 'post' ? 'What\'s on your mind?' : 'Share your moment...'"
            :maxlength="mode === 'post' ? 500 : 200"
            class="content-input"
            rows="4"
          ></textarea>
          <div class="character-count">
            {{ content.length }}/{{ mode === 'post' ? 500 : 200 }}
          </div>
        </div>
      </div>

      <!-- Media upload section -->
      <div class="media-section">
        <div class="media-upload-area" :class="{ 'has-media': mediaFiles.length > 0 }">
          <label for="media-upload" class="upload-trigger" :class="{ 'uploading': isUploading }">
            <div class="upload-content">
              <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt" v-if="!isUploading"></i>
                <i class="fas fa-spinner fa-spin" v-if="isUploading"></i>
              </div>
              <div class="upload-text">
                <span v-if="!isUploading">{{ mediaFiles.length === 0 ? 'Add photos or videos' : 'Add more media' }}</span>
                <span v-if="isUploading">Processing...</span>
              </div>
              <div class="upload-info">
                <small>{{ mode === 'post' ? 'Up to 10 files, 10MB each' : 'Up to 1 file, 20MB max' }}</small>
              </div>
            </div>
          </label>
          <input
            type="file"
            id="media-upload"
            multiple
            :accept="acceptedFormats"
            @change="handleMediaUpload"
            class="upload-input"
            :disabled="isUploading || (mode === 'story' && mediaFiles.length >= 1)"
          />
        </div>

        <!-- Media preview -->
        <div v-if="mediaFiles.length > 0" class="media-preview">
          <div v-for="(media, index) in mediaFiles" :key="index" class="media-item">
            <div class="media-container">
              <img v-if="media.type.startsWith('image/')" :src="media.preview" alt="Preview" class="media-image" />
              <video v-else-if="media.type.startsWith('video/')" :src="media.preview" controls class="media-video"></video>
              <button @click="removeMedia(index)" class="remove-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div class="media-info">
              <span class="file-name">{{ media.name }}</span>
              <span class="file-size">{{ formatFileSize(media.size) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Options section -->
      <div class="options-section">
        <div class="visibility-control" v-if="mode === 'post'">
          <label class="option-label">
            <i class="fas fa-eye"></i>
            <span>Visibility</span>
          </label>
          <select v-model="visibility" class="visibility-select">
            <option value="public">🌍 Public</option>
            <option value="followers">👥 Followers Only</option>
            <option value="private">🔒 Private</option>
          </select>
        </div>

        <div class="story-duration" v-if="mode === 'story'">
          <label class="option-label">
            <i class="fas fa-clock"></i>
            <span>Duration</span>
          </label>
          <div class="duration-info">
            {{ isPremium ? '48 hours' : '24 hours' }}
            <span v-if="!isPremium" class="premium-hint">
              <i class="fas fa-crown"></i>
              Upgrade for 48h stories
            </span>
          </div>
        </div>
      </div>

      <!-- Error message -->
      <div v-if="error" class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
      </div>

      <!-- Action buttons -->
      <div class="action-section">
        <button @click="clearContent" class="clear-btn" :disabled="isSubmitting">
          <i class="fas fa-trash"></i>
          Clear
        </button>
        <button
          @click="createContent"
          class="create-btn"
          :disabled="!canSubmit || isSubmitting"
        >
          <span v-if="isSubmitting">
            <i class="fas fa-spinner fa-spin"></i>
            {{ mode === 'post' ? 'Creating Post...' : 'Creating Story...' }}
          </span>
          <span v-else>
            <i class="fas fa-paper-plane"></i>
            {{ mode === 'post' ? 'Create Post' : 'Share Story' }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { usePostsStore } from '@/stores/posts';
import { useStoriesStore } from '@/stores/stories';
import { useAuthStore } from '@/stores/auth';

// Emits
const emit = defineEmits(['content-created']);

// Stores
const postsStore = usePostsStore();
const storiesStore = useStoriesStore();
const authStore = useAuthStore();

// State
const mode = ref('post'); // 'post' or 'story'
const content = ref('');
const mediaFiles = ref([]);
const visibility = ref('public');
const isUploading = ref(false);
const isSubmitting = ref(false);
const error = ref(null);

// Computed
const isPremium = computed(() => authStore.user?.isPremium || false);

const acceptedFormats = computed(() => {
  return 'image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/webm';
});

const maxFiles = computed(() => mode.value === 'post' ? 10 : 1);
const maxFileSize = computed(() => mode.value === 'post' ? 10 * 1024 * 1024 : 20 * 1024 * 1024);

const canSubmit = computed(() => {
  const hasContent = content.value.trim().length > 0 || mediaFiles.value.length > 0;
  const hasRequiredMedia = mode.value === 'story' ? mediaFiles.value.length > 0 : true;
  return hasContent && hasRequiredMedia && !isUploading.value;
});

// Methods
function setMode(newMode) {
  if (newMode === mode.value) return;

  // Clear content when switching modes
  clearContent();
  mode.value = newMode;
}

function clearContent() {
  content.value = '';
  mediaFiles.value = [];
  error.value = null;
  visibility.value = 'public';
}

async function handleMediaUpload(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  // Check file limits
  if (mode.value === 'story' && mediaFiles.value.length >= 1) {
    error.value = 'Stories can only have one media file';
    return;
  }

  if (mode.value === 'post' && mediaFiles.value.length >= maxFiles.value) {
    error.value = `Maximum ${maxFiles.value} files allowed`;
    return;
  }

  isUploading.value = true;
  error.value = null;

  try {
    for (let i = 0; i < files.length && mediaFiles.value.length < maxFiles.value; i++) {
      const file = files[i];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/webm'];
      if (!allowedTypes.includes(file.type)) {
        error.value = `Unsupported file type: ${file.type}`;
        continue;
      }

      // Validate file size
      if (file.size > maxFileSize.value) {
        error.value = `File "${file.name}" exceeds ${formatFileSize(maxFileSize.value)} limit`;
        continue;
      }

      // Create preview
      const preview = await createFilePreview(file);

      mediaFiles.value.push({
        file,
        preview,
        type: file.type,
        name: file.name,
        size: file.size
      });

      // For stories, only allow one file
      if (mode.value === 'story') break;
    }
  } catch (err) {
    error.value = err.message || 'Failed to process files';
  } finally {
    isUploading.value = false;
    event.target.value = '';
  }
}

function createFilePreview(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

function removeMedia(index) {
  mediaFiles.value.splice(index, 1);
  error.value = null;
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

async function createContent() {
  if (!canSubmit.value || isSubmitting.value) return;

  isSubmitting.value = true;
  error.value = null;

  try {
    if (mode.value === 'post') {
      await createPost();
    } else {
      await createStory();
    }

    // Clear form on success
    clearContent();

    // Emit success event
    emit('content-created', { type: mode.value });

  } catch (err) {
    error.value = err.message || `Failed to create ${mode.value}`;
  } finally {
    isSubmitting.value = false;
  }
}

async function createPost() {
  const mediaData = mediaFiles.value.map(media => ({
    data: media.preview,
    type: media.type,
    name: media.name,
    size: media.size
  }));

  await postsStore.createPost({
    content: content.value,
    media: mediaData,
    visibility: visibility.value
  });
}

async function createStory() {
  if (mediaFiles.value.length === 0) {
    throw new Error('Stories require media content');
  }

  const media = mediaFiles.value[0];
  await storiesStore.createStory({
    media: {
      data: media.preview,
      type: media.type,
      name: media.name,
      size: media.size
    },
    caption: content.value
  });
}

// Lifecycle
onMounted(() => {
  // Initialize with post mode
  mode.value = 'post';
});
</script>

<style scoped>
.unified-creator {
  @apply bg-white rounded-2xl shadow-2xl overflow-hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(0, 212, 255, 0.1);
  transition: all 0.3s ease;
}

.unified-creator:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px -12px rgba(0, 212, 255, 0.25);
}

/* Header Styles */
.creator-header {
  @apply relative;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  padding: 1.5rem;
}

.mode-toggle {
  @apply flex space-x-4 relative z-10;
}

.mode-btn {
  @apply flex items-center space-x-2 px-6 py-3 rounded-xl font-semibold transition-all duration-300;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid transparent;
  backdrop-filter: blur(10px);
}

.mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.mode-btn.active {
  background: rgba(255, 255, 255, 0.95);
  color: #00d4ff;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.mode-indicator {
  @apply absolute bottom-0 left-0 right-0 h-1;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 50%, transparent 100%);
}

.indicator-line {
  @apply h-full transition-all duration-500;
  background: linear-gradient(90deg, #ffffff 0%, #f0f9ff 100%);
  width: 50%;
  transform: translateX(0);
}

.indicator-line.story-mode {
  transform: translateX(100%);
}

/* Body Styles */
.creator-body {
  @apply p-6 space-y-6;
}

/* Content Section */
.content-section {
  @apply relative;
}

.content-input-wrapper {
  @apply relative;
}

.content-input {
  @apply w-full p-4 border-2 border-gray-200 rounded-xl resize-none transition-all duration-300;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  font-size: 1.1rem;
  line-height: 1.6;
}

.content-input:focus {
  @apply outline-none border-blue-400;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
  background: #ffffff;
}

.character-count {
  @apply absolute bottom-3 right-3 text-sm text-gray-500 font-medium;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(5px);
}

/* Media Section */
.media-section {
  @apply space-y-4;
}

.media-upload-area {
  @apply relative;
}

.upload-trigger {
  @apply block w-full p-8 border-2 border-dashed border-gray-300 rounded-xl cursor-pointer transition-all duration-300;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
}

.upload-trigger:hover {
  @apply border-blue-400;
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
  transform: translateY(-2px);
}

.upload-trigger.uploading {
  @apply border-blue-500;
  background: linear-gradient(135deg, #dbeafe 0%, #f0f9ff 100%);
  animation: pulse 2s infinite;
}

.upload-content {
  @apply text-center;
}

.upload-icon {
  @apply text-4xl text-gray-400 mb-3;
}

.upload-trigger:hover .upload-icon {
  @apply text-blue-500;
}

.upload-text {
  @apply text-lg font-semibold text-gray-700 mb-2;
}

.upload-info {
  @apply text-gray-500;
}

.upload-input {
  @apply absolute inset-0 w-full h-full opacity-0 cursor-pointer;
}

/* Media Preview */
.media-preview {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
}

.media-item {
  @apply relative bg-white rounded-xl overflow-hidden shadow-lg transition-all duration-300;
  border: 2px solid rgba(0, 212, 255, 0.1);
}

.media-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.15);
}

.media-container {
  @apply relative aspect-square overflow-hidden;
}

.media-image,
.media-video {
  @apply w-full h-full object-cover;
}

.remove-btn {
  @apply absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center transition-all duration-300;
  backdrop-filter: blur(10px);
}

.remove-btn:hover {
  @apply bg-red-600 scale-110;
}

.media-info {
  @apply p-3 space-y-1;
}

.file-name {
  @apply block text-sm font-medium text-gray-700 truncate;
}

.file-size {
  @apply block text-xs text-gray-500;
}

/* Options Section */
.options-section {
  @apply flex flex-wrap items-center gap-6 p-4 bg-gray-50 rounded-xl;
}

.option-label {
  @apply flex items-center space-x-2 text-sm font-semibold text-gray-700;
}

.visibility-select {
  @apply px-4 py-2 border border-gray-300 rounded-lg bg-white transition-all duration-300;
}

.visibility-select:focus {
  @apply outline-none border-blue-400;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.duration-info {
  @apply text-sm font-medium text-gray-700;
}

.premium-hint {
  @apply text-xs text-yellow-600 ml-2;
}

/* Error Message */
.error-message {
  @apply flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-xl text-red-700;
}

/* Action Section */
.action-section {
  @apply flex items-center justify-between pt-4 border-t border-gray-200;
}

.clear-btn {
  @apply px-6 py-3 text-gray-600 font-semibold rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.clear-btn:hover:not(:disabled) {
  @apply text-gray-800;
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-2px);
}

.create-btn {
  @apply px-8 py-3 text-white font-bold rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.create-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #00b8e6 0%, #0088bb 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.create-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: none;
  box-shadow: none;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .creator-header {
    padding: 1rem;
  }

  .mode-toggle {
    @apply space-x-2;
  }

  .mode-btn {
    @apply px-4 py-2 text-sm;
  }

  .creator-body {
    @apply p-4 space-y-4;
  }

  .upload-trigger {
    @apply p-6;
  }

  .upload-icon {
    @apply text-3xl;
  }

  .media-preview {
    @apply grid-cols-1;
  }

  .options-section {
    @apply flex-col items-start gap-4;
  }

  .action-section {
    @apply flex-col space-y-3;
  }

  .clear-btn,
  .create-btn {
    @apply w-full;
  }
}
</style>
