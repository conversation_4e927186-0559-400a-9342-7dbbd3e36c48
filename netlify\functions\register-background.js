const { ObjectId } = require('mongodb');
const { findOne, insertOne } = require('./utils/db');
const { generateToken, hashPassword } = require('./utils/auth-utils');
const { success, error } = require('./utils/response');

// Background function for user registration
// This runs with a 15-minute timeout instead of 10 seconds
exports.handler = async (event, context) => {
  try {
    console.log('Background registration process started');
    
    // Parse request body
    const body = JSON.parse(event.body || '{}');
    const { email, username, password } = body;
    
    // Basic validation
    if (!email || !username || !password) {
      return {
        statusCode: 400,
        body: JSON.stringify({ success: false, error: 'Missing required fields' })
      };
    }
    
    // Check if email or username already exists
    try {
      const [existingEmail, existingUsername] = await Promise.all([
        findOne('users', { email }),
        findOne('users', { username })
      ]);
      
      if (existingEmail) {
        return {
          statusCode: 409,
          body: JSON.stringify({ success: false, error: 'Email already in use' })
        };
      }
      
      if (existingUsername) {
        return {
          statusCode: 409,
          body: JSON.stringify({ success: false, error: 'Username already taken' })
        };
      }
    } catch (dbError) {
      console.error('Error checking existing user:', dbError);
    }
    
    // Hash password with minimal salt rounds for speed
    const hashedPassword = await hashPassword(password);
    
    // Create minimal user object
    const user = {
      email,
      username,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
      profilePicture: null,
      isPremium: false,
      privacySettings: {
        defaultPostVisibility: 'public',
        allowFollowersToViewContent: true,
        allowMessagesFromNonFollowers: true
      },
      following: [],
      followers: [],
      aiRequestsRemaining: 3,
      aiRequestsResetTime: new Date(Date.now() + 3600000),
      lastLoginAt: new Date(),
      isActive: true,
      role: 'user'
    };
    
    // Insert user into database
    const result = await insertOne('users', user);
    
    if (!result || !result.insertedId) {
      return {
        statusCode: 500,
        body: JSON.stringify({ success: false, error: 'Failed to create user account' })
      };
    }
    
    // Create minimal user object for token
    const newUser = {
      _id: result.insertedId,
      email,
      username,
      isPremium: false,
      createdAt: new Date()
    };
    
    // Generate JWT token
    const token = generateToken(newUser);
    
    // Return success response
    return {
      statusCode: 200,
      body: JSON.stringify({
        success: true,
        data: {
          user: {
            _id: newUser._id,
            email: newUser.email,
            username: newUser.username,
            profilePicture: null,
            isPremium: false,
            createdAt: newUser.createdAt
          },
          token
        }
      })
    };
  } catch (err) {
    console.error('Error in background registration:', err);
    return {
      statusCode: 500,
      body: JSON.stringify({ success: false, error: 'Registration failed' })
    };
  }
};
