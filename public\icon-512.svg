<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient512" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow512" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="252" fill="url(#bgGradient512)" filter="url(#shadow512)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(256, 256)">
    <!-- Central N -->
    <text x="0" y="51.2" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="204.8" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-102.4" y1="-51.2" x2="102.4" y2="51.2" 
          stroke="#0a0a0a" stroke-width="10.24" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-76.8" cy="76.8" r="10.24" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="76.8" cy="76.8" r="10.24" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="102.4" r="7.68" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>