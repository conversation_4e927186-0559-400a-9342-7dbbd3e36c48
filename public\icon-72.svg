<svg width="72" height="72" viewBox="0 0 72 72" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient72" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow72" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="36" cy="36" r="32" fill="url(#bgGradient72)" filter="url(#shadow72)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(36, 36)">
    <!-- Central N -->
    <text x="0" y="7.2" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="28.8" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-14.4" y1="-7.2" x2="14.4" y2="7.2" 
          stroke="#0a0a0a" stroke-width="1.44" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-10.799999999999999" cy="10.799999999999999" r="1.44" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="10.799999999999999" cy="10.799999999999999" r="1.44" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="14.4" r="1.08" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>