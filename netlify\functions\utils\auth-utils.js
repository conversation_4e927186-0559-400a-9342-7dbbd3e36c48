const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// Generate JWT token with proper security practices
function generateToken(user) {
  // Use a default secret for development if not set
  const jwtSecret = process.env.JWT_SECRET || 'development-secret-key-for-testing-only';

  // Handle case where user might be null or undefined
  if (!user) {
    console.error('Cannot generate token for null/undefined user');
    throw new Error('Invalid user object');
  }

  // Ensure user._id exists and convert to string safely
  let userId;
  try {
    userId = user._id ? user._id.toString() : `temp-${Date.now()}`;
  } catch (e) {
    userId = `temp-${Date.now()}`;
    console.warn('Could not convert user._id to string, using temporary ID');
  }

  // Remove sensitive information and include only necessary data
  const userInfo = {
    _id: userId,
    email: user.email || '<EMAIL>',
    username: user.username || 'unknown_user',
    isPremium: user.isPremium || false,
    role: user.role || 'user'
  };

  // Set appropriate token expiration
  const expiresIn = user.isPremium ? '30d' : '7d'; // Premium users get longer sessions

  try {
    // Generate token with proper algorithm and expiration
    return jwt.sign(userInfo, jwtSecret, {
      expiresIn: expiresIn,
      algorithm: 'HS256', // Explicitly set algorithm
      issuer: 'netuark-api', // Add issuer for additional security
      audience: 'netuark-client', // Add audience for additional security
      subject: userId // Add subject for additional security
    });
  } catch (error) {
    console.error('Error signing JWT token:', error);
    throw new Error('Failed to generate authentication token');
  }
}

// Verify JWT token with proper error handling
function verifyToken(token) {
  // Use a default secret for development if not set
  const jwtSecret = process.env.JWT_SECRET || 'development-secret-key-for-testing-only';

  // Handle invalid token input
  if (!token || typeof token !== 'string') {
    console.error('Invalid token provided to verifyToken:', token);
    return null;
  }

  try {
    // Verify token with explicit options
    const decoded = jwt.verify(token, jwtSecret, {
      algorithms: ['HS256'], // Only accept HS256 algorithm
      issuer: 'netuark-api', // Verify issuer
      audience: 'netuark-client' // Verify audience
    });

    // Additional validation of decoded token
    if (!decoded || !decoded._id) {
      console.error('Token verified but missing required fields');
      return null;
    }

    return decoded;
  } catch (error) {
    // Log different types of errors for debugging
    if (error.name === 'TokenExpiredError') {
      console.log('Token expired:', error.expiredAt);
    } else if (error.name === 'JsonWebTokenError') {
      console.log('JWT error:', error.message);
    } else {
      console.error('Unexpected JWT verification error:', error);
    }
    return null;
  }
}

// Hash password with optimized performance for serverless environment
async function hashPassword(password) {
  try {
    // Use salt rounds of 8 for much better performance in serverless environments
    // This is a compromise for Netlify Functions which have a 10-second timeout
    // 8 rounds is still secure enough for most applications
    console.time('password-hash');
    const salt = await bcrypt.genSalt(8);
    const hash = await bcrypt.hash(password, salt);
    console.timeEnd('password-hash');
    return hash;
  } catch (error) {
    console.error('Error hashing password:', error);
    throw new Error('Password hashing failed');
  }
}

// Compare password with hash using constant-time comparison
async function comparePassword(password, hash) {
  try {
    console.time('password-compare');
    // bcrypt.compare uses a constant-time algorithm to prevent timing attacks
    const result = await Promise.race([
      bcrypt.compare(password, hash),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Password comparison timed out')), 2500)
      )
    ]);
    console.timeEnd('password-compare');
    return result;
  } catch (error) {
    console.error('Error comparing passwords:', error);
    console.timeEnd('password-compare');
    return false; // Return false on error for security
  }
}

// Verify password with hash - alias for comparePassword for consistency
async function verifyPassword(password, hash) {
  return comparePassword(password, hash);
}

// Middleware to verify authentication
function requireAuth(handler) {
  return async (event, context) => {
    try {
      // Get authorization header - safely handle undefined headers
      const headers = event.headers || {};
      const authHeader = headers.authorization || headers.Authorization || '';

      // Check if token exists
      if (!authHeader || typeof authHeader !== 'string' || !authHeader.startsWith('Bearer ')) {
        return {
          statusCode: 401,
          body: JSON.stringify({ error: 'Unauthorized - Invalid authorization header' })
        };
      }

      // Extract token
      const token = authHeader.split(' ')[1];

      if (!token) {
        return {
          statusCode: 401,
          body: JSON.stringify({ error: 'Unauthorized - No token provided' })
        };
      }

      // Verify token
      const user = verifyToken(token);

      if (!user) {
        return {
          statusCode: 401,
          body: JSON.stringify({ error: 'Invalid or expired token' })
        };
      }

      // Add user to event object
      event.user = user;

      // Call the original handler
      return handler(event, context);
    } catch (err) {
      console.error('Authentication error:', err);
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Internal server error during authentication' })
      };
    }
  };
}

// Middleware to verify premium subscription
function requirePremium(handler) {
  return requireAuth(async (event, context) => {
    // Check if user is premium
    if (!event.user.isPremium) {
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Premium subscription required' })
      };
    }

    // Call the original handler
    return handler(event, context);
  });
}

module.exports = {
  generateToken,
  verifyToken,
  hashPassword,
  comparePassword,
  verifyPassword,
  requireAuth,
  requirePremium
};
