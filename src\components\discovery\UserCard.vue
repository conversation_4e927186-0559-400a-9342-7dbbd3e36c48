<template>
  <div class="user-card">
    <div class="card-header">
      <div class="user-avatar">
        <img :src="user.profilePicture || defaultAvatar" :alt="user.username" class="avatar-image" />
        <div v-if="user.isPremium" class="premium-badge" title="NTA+ Premium User">
          <i class="fas fa-crown"></i>
        </div>
      </div>

      <div v-if="showTrendingRank" class="trending-rank">
        #{{ trendingRank }}
      </div>
    </div>

    <div class="card-body">
      <div class="user-info">
        <h3 class="username">{{ user.username }}</h3>
        <p v-if="user.displayName" class="display-name">{{ user.displayName }}</p>
        <p v-if="user.bio" class="user-bio">{{ user.bio }}</p>
      </div>

      <div class="user-stats">
        <div v-if="user.followerCount !== undefined" class="stat">
          <span class="stat-value">{{ formatNumber(user.followerCount) }}</span>
          <span class="stat-label">Followers</span>
        </div>
        <div v-if="user.postCount !== undefined" class="stat">
          <span class="stat-value">{{ formatNumber(user.postCount) }}</span>
          <span class="stat-label">Posts</span>
        </div>
        <div v-if="showRecommendationScore && user.recommendationScore" class="stat">
          <span class="stat-value">{{ Math.round(user.recommendationScore) }}%</span>
          <span class="stat-label">Match</span>
        </div>
      </div>

      <div v-if="user.commonInterests && user.commonInterests.length > 0" class="common-interests">
        <div class="interests-label">Common interests:</div>
        <div class="interests-list">
          <span
            v-for="interest in user.commonInterests.slice(0, 3)"
            :key="interest"
            class="interest-tag"
          >
            {{ interest }}
          </span>
          <span v-if="user.commonInterests.length > 3" class="more-interests">
            +{{ user.commonInterests.length - 3 }} more
          </span>
        </div>
      </div>
    </div>

    <div class="card-footer">
      <div class="action-buttons">
        <button
          v-if="!user.isFollowing"
          @click="followUser"
          class="follow-btn"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <i class="fas fa-spinner fa-spin"></i>
          </span>
          <span v-else>
            <i class="fas fa-user-plus"></i> Follow
          </span>
        </button>

        <button
          v-else
          @click="unfollowUser"
          class="unfollow-btn"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <i class="fas fa-spinner fa-spin"></i>
          </span>
          <span v-else>
            <i class="fas fa-user-check"></i> Following
          </span>
        </button>

        <button @click="viewProfile" class="view-profile-btn">
          <i class="fas fa-eye"></i> View
        </button>

        <button @click="sendMessage" class="message-btn" title="Send Message">
          <i class="fas fa-envelope"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

// Props
const props = defineProps({
  user: {
    type: Object,
    required: true
  },
  showRecommendationScore: {
    type: Boolean,
    default: false
  },
  showTrendingRank: {
    type: Boolean,
    default: false
  },
  trendingRank: {
    type: Number,
    default: 0
  }
});

// Emits
const emit = defineEmits(['follow', 'unfollow']);

// Router
const router = useRouter();

// State
const isLoading = ref(false);

// Computed
const defaultAvatar = '/default-avatar.svg';

// Methods
function formatNumber(num) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

async function followUser() {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    emit('follow', props.user);
  } catch (err) {
    console.error('Failed to follow user:', err);
  } finally {
    isLoading.value = false;
  }
}

async function unfollowUser() {
  if (isLoading.value) return;

  isLoading.value = true;

  try {
    emit('unfollow', props.user);
  } catch (err) {
    console.error('Failed to unfollow user:', err);
  } finally {
    isLoading.value = false;
  }
}

function viewProfile() {
  router.push(`/profile/${props.user.username}`);
}

function sendMessage() {
  router.push(`/messages?user=${props.user._id}`);
}
</script>

<style scoped>
.user-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.user-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  position: relative;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.user-avatar {
  position: relative;
  display: inline-block;
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid white;
  object-fit: cover;
}

.premium-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #f39c12;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  border: 2px solid white;
}

.trending-rank {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  font-weight: bold;
}

.card-body {
  padding: 1rem;
}

.user-info {
  text-align: center;
  margin-bottom: 1rem;
}

.username {
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
}

.display-name {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #777;
}

.user-bio {
  margin: 0;
  font-size: 0.9rem;
  color: #555;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.user-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 1rem;
  padding: 0.5rem 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.1rem;
  font-weight: bold;
  color: #3498db;
}

.stat-label {
  font-size: 0.8rem;
  color: #777;
}

.common-interests {
  margin-bottom: 1rem;
}

.interests-label {
  font-size: 0.8rem;
  color: #777;
  margin-bottom: 0.5rem;
}

.interests-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.interest-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 12px;
  white-space: nowrap;
}

.more-interests {
  font-size: 0.7rem;
  color: #777;
  font-style: italic;
}

.card-footer {
  padding: 1rem;
  background-color: #f8f9fa;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.follow-btn, .unfollow-btn, .view-profile-btn, .message-btn {
  flex: 1;
  padding: 0.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  transition: background-color 0.2s;
}

.follow-btn {
  background-color: #3498db;
  color: white;
}

.follow-btn:hover {
  background-color: #2980b9;
}

.follow-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.unfollow-btn {
  background-color: #2ecc71;
  color: white;
}

.unfollow-btn:hover {
  background-color: #27ae60;
}

.unfollow-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.view-profile-btn {
  background-color: #f0f0f0;
  color: #333;
  flex: 0.8;
}

.view-profile-btn:hover {
  background-color: #e0e0e0;
}

.message-btn {
  background-color: #9b59b6;
  color: white;
  flex: 0.6;
}

.message-btn:hover {
  background-color: #8e44ad;
}

@media (max-width: 480px) {
  .action-buttons {
    flex-direction: column;
  }

  .follow-btn, .unfollow-btn, .view-profile-btn, .message-btn {
    flex: none;
  }

  .user-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .stat-value, .stat-label {
    display: inline;
  }
}
</style>
