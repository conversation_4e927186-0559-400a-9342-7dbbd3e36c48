<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { usePostsStore } from '../../stores/posts';
import { useStoriesStore } from '../../stores/stories';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const postsStore = usePostsStore();
const storiesStore = useStoriesStore();
const authStore = useAuthStore();

// State
const isLoading = ref(false);
const isLoadingMore = ref(false);
const observer = ref(null);



// Load initial data
onMounted(async () => {
  isLoading.value = true;

  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      console.error('User not authenticated, redirecting to login');
      router.push('/login');
      return;
    }

    // Fetch user profile to ensure token is valid
    try {
      await authStore.fetchUserProfile();
    } catch (authError) {
      console.error('Authentication error:', authError);
      authStore.logout();
      router.push('/login');
      return;
    }

    // Fetch posts from the real API
    await postsStore.fetchPosts(true);

    // Fetch stories from the real API
    await storiesStore.fetchStories();

    // Setup infinite scroll after data is loaded
    setupInfiniteScroll();
  } catch (error) {
    console.error('Error loading feed:', error);
    // Show error message to user
    alert(`Error loading feed: ${error.message || 'Unknown error'}`);
  } finally {
    isLoading.value = false;
  }
});

// Clean up observer on component unmount
onUnmounted(() => {
  if (observer.value) {
    observer.value.disconnect();
  }
});

// Set up infinite scroll
const setupInfiniteScroll = () => {
  // Create intersection observer for infinite scroll
  observer.value = new IntersectionObserver(
    (entries) => {
      if (entries[0].isIntersecting && !isLoadingMore.value && postsStore.hasMore) {
        loadMorePosts();
      }
    },
    { threshold: 0.5 }
  );

  // Observe the load more trigger element
  const loadMoreTrigger = document.getElementById('load-more-trigger');
  if (loadMoreTrigger) {
    observer.value.observe(loadMoreTrigger);
  }
};

// Load more posts
const loadMorePosts = async () => {
  if (isLoadingMore.value || !postsStore.hasMore) return;

  isLoadingMore.value = true;

  try {
    // Fetch more posts from the real API
    await postsStore.fetchPosts(false);
  } catch (error) {
    console.error('Error loading more posts:', error);
  } finally {
    isLoadingMore.value = false;
  }
};

// Navigate to create content
const navigateToCreatePost = () => {
  router.push('/create');
};

// Navigate to story viewer
const viewStories = (storyGroup) => {
  storiesStore.setCurrentStoryGroup(storyGroup);
};

// Like/unlike post
const toggleLike = async (postId, isLiked) => {
  try {
    if (isLiked) {
      await postsStore.unlikePost(postId);
    } else {
      await postsStore.likePost(postId);
    }
  } catch (error) {
    console.error('Error toggling like:', error);
  }
};

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
};
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <img src="/logo1.png" alt="NeTuArk Logo" class="h-8" />
        </div>
        <div class="flex items-center gap-3">
          <button @click="navigateToCreatePost" class="p-2 text-neon-blue hover:bg-neon-blue/10 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
          </button>
          <router-link to="/messages" class="p-2 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </router-link>
          <router-link :to="`/profile/${authStore.user?.username}`" class="p-2 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </router-link>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Stories -->
      <div v-if="storiesStore.allStories.length > 0" class="mb-6 overflow-x-auto pb-2">
        <div class="flex space-x-4">
          <!-- Story items -->
          <div
            v-for="storyGroup in storiesStore.allStories"
            :key="storyGroup._id"
            @click="viewStories(storyGroup)"
            class="flex flex-col items-center cursor-pointer"
          >
            <div class="w-16 h-16 rounded-full p-0.5" :class="storyGroup.stories.some(s => !s.viewed) ? 'bg-gradient-to-tr from-neon-purple to-neon-blue' : 'bg-gray-700'">
              <div class="w-full h-full rounded-full overflow-hidden border-2 border-darker-bg">
                <img :src="storyGroup.author.profilePicture || 'https://via.placeholder.com/150'" :alt="storyGroup.author.username" class="w-full h-full object-cover" />
              </div>
            </div>
            <span class="text-xs mt-1 text-gray-300 truncate w-16 text-center">{{ storyGroup.author.username }}</span>
          </div>
        </div>
      </div>

      <!-- Posts -->
      <div v-if="isLoading" class="flex justify-center py-10">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-neon-blue"></div>
      </div>

      <div v-else-if="postsStore.allPosts.length === 0" class="flex flex-col items-center justify-center py-10">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
        <p class="text-gray-400 text-lg">No posts yet</p>
        <button @click="navigateToCreatePost" class="mt-4 neon-button py-2 px-4">Create First Post</button>
      </div>

      <div v-else class="space-y-6">
        <div
          v-for="post in postsStore.allPosts"
          :key="post._id"
          class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden"
        >
          <!-- Post header -->
          <div class="p-4 flex items-center">
            <router-link :to="`/profile/${post.author.username}`" class="flex items-center">
              <div class="w-10 h-10 rounded-full overflow-hidden mr-3">
                <img :src="post.author.profilePicture || 'https://via.placeholder.com/150'" :alt="post.author.username" class="w-full h-full object-cover" />
              </div>
              <div>
                <div class="font-medium flex items-center">
                  {{ post.author.username }}
                  <span v-if="post.author.isPremium" class="ml-1 text-xs text-neon-blue">NTA+</span>
                </div>
                <div class="text-xs text-gray-400">{{ formatDate(post.createdAt) }}</div>
              </div>
            </router-link>
          </div>

          <!-- Post content -->
          <router-link :to="`/post/${post._id}`">
            <!-- Media -->
            <div v-if="post.media && post.media.length > 0" class="relative">
              <img
                v-if="post.media[0].type === 'image'"
                :src="post.media[0].url"
                :alt="post.content"
                class="w-full max-h-[500px] object-cover"
              />
              <video
                v-else
                :src="post.media[0].url"
                controls
                class="w-full max-h-[500px] object-cover"
              ></video>
            </div>

            <!-- Text content -->
            <div class="p-4">
              <p class="text-white">{{ post.content }}</p>

              <!-- AI Tags -->
              <div v-if="post.aiTags && post.aiTags.length > 0" class="mt-3 flex flex-wrap gap-2">
                <span
                  v-for="tag in post.aiTags"
                  :key="tag"
                  class="px-2 py-1 rounded-full text-xs bg-neon-blue/10 text-neon-blue border border-neon-blue/30"
                >
                  #{{ tag }}
                </span>
              </div>
            </div>
          </router-link>

          <!-- Post actions -->
          <div class="px-4 py-3 border-t border-gray-800 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button @click="toggleLike(post._id, post.isLiked)" class="flex items-center text-gray-400 hover:text-neon-pink">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" :fill="post.isLiked ? 'currentColor' : 'none'" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                <span class="ml-1">{{ post.likes }}</span>
              </button>

              <router-link :to="`/post/${post._id}`" class="flex items-center text-gray-400 hover:text-neon-blue">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <span class="ml-1">{{ post.comments }}</span>
              </router-link>
            </div>

            <button class="text-gray-400 hover:text-neon-blue">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Load more trigger -->
        <div id="load-more-trigger" class="h-10 flex justify-center items-center">
          <div v-if="isLoadingMore" class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-neon-blue"></div>
          <p v-else-if="!postsStore.hasMore" class="text-gray-500 text-sm">No more posts</p>
        </div>
      </div>
    </div>
  </div>
</template>
