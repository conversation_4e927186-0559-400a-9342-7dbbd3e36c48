const { MongoClient } = require('mongodb');

exports.handler = async (event, context) => {
  try {
    console.log('Testing minimal MongoDB connection...');
    
    // Get MongoDB URI
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://soham:<EMAIL>/?retryWrites=true&w=majority&appName=netuark';
    
    console.log('Environment variables:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- MONGODB_URI exists:', !!process.env.MONGODB_URI);
    console.log('- MongoDB URI (first 50 chars):', mongoUri ? mongoUri.substring(0, 50) + '...' : 'undefined');
    
    if (!mongoUri) {
      throw new Error('MongoDB URI is required');
    }
    
    // Minimal connection options
    const options = {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      serverSelectionTimeoutMS: 10000,
    };
    
    console.log('Creating MongoDB client with minimal options...');
    const client = new MongoClient(mongoUri, options);
    
    console.log('Attempting to connect...');
    await client.connect();
    
    console.log('Connected! Testing ping...');
    await client.db("admin").command({ ping: 1 });
    
    console.log('Ping successful! Getting database...');
    const db = client.db(process.env.DB_NAME || 'netuark');
    
    console.log('Testing collections...');
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log('Collections found:', collectionNames);
    
    // Test user count
    const userCount = await db.collection('users').countDocuments();
    console.log('User count:', userCount);
    
    // Close connection
    await client.close();
    console.log('Connection closed successfully');
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        success: true,
        message: 'Minimal MongoDB connection test successful!',
        data: {
          environment: process.env.NODE_ENV,
          hasMongoUri: !!process.env.MONGODB_URI,
          dbName: process.env.DB_NAME || 'netuark',
          collections: collectionNames,
          userCount,
          timestamp: new Date().toISOString()
        }
      })
    };
    
  } catch (error) {
    console.error('Minimal DB test failed:', error);
    console.error('Error stack:', error.stack);
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      body: JSON.stringify({
        success: false,
        error: `Minimal MongoDB test failed: ${error.message}`,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
