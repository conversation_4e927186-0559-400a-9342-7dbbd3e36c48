<script setup>
import { ref, computed, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  initialFilters: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['close', 'apply', 'clear']);

// Filter state
const filters = ref({
  contentType: 'all', // all, posts, users, stories, emojis
  dateRange: 'all', // all, today, week, month, year
  customDateFrom: '',
  customDateTo: '',
  sortBy: 'relevance', // relevance, newest, oldest, popular
  userType: 'all', // all, verified, premium, regular
  hasMedia: false,
  hasLinks: false,
  language: 'all',
  minLikes: '',
  maxLikes: '',
  minComments: '',
  maxComments: '',
  tags: [],
  excludeWords: '',
  exactPhrase: '',
  location: ''
});

const newTag = ref('');

// Computed
const hasActiveFilters = computed(() => {
  return filters.value.contentType !== 'all' ||
         filters.value.dateRange !== 'all' ||
         filters.value.sortBy !== 'relevance' ||
         filters.value.userType !== 'all' ||
         filters.value.hasMedia ||
         filters.value.hasLinks ||
         filters.value.language !== 'all' ||
         filters.value.minLikes ||
         filters.value.maxLikes ||
         filters.value.minComments ||
         filters.value.maxComments ||
         filters.value.tags.length > 0 ||
         filters.value.excludeWords ||
         filters.value.exactPhrase ||
         filters.value.location;
});

// Watch for prop changes
watch(() => props.initialFilters, (newFilters) => {
  if (newFilters) {
    filters.value = { ...filters.value, ...newFilters };
  }
}, { immediate: true });

// Add tag
const addTag = () => {
  const tag = newTag.value.trim();
  if (tag && !filters.value.tags.includes(tag)) {
    filters.value.tags.push(tag);
    newTag.value = '';
  }
};

// Remove tag
const removeTag = (index) => {
  filters.value.tags.splice(index, 1);
};

// Apply filters
const applyFilters = () => {
  emit('apply', { ...filters.value });
  emit('close');
};

// Clear all filters
const clearFilters = () => {
  filters.value = {
    contentType: 'all',
    dateRange: 'all',
    customDateFrom: '',
    customDateTo: '',
    sortBy: 'relevance',
    userType: 'all',
    hasMedia: false,
    hasLinks: false,
    language: 'all',
    minLikes: '',
    maxLikes: '',
    minComments: '',
    maxComments: '',
    tags: [],
    excludeWords: '',
    exactPhrase: '',
    location: ''
  };
  emit('clear');
};

// Close modal
const closeModal = () => {
  emit('close');
};
</script>

<template>
  <div
    v-if="show"
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    @click="closeModal"
  >
    <div
      class="bg-darker-bg rounded-xl border border-gray-800 w-full max-w-2xl max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- Header -->
      <div class="p-6 border-b border-gray-800 flex justify-between items-center">
        <h2 class="text-xl font-semibold">Advanced Search Filters</h2>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-white transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 overflow-y-auto max-h-[60vh] space-y-6">
        <!-- Content Type -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Content Type</label>
          <select v-model="filters.contentType" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
            <option value="all">All Content</option>
            <option value="posts">Posts</option>
            <option value="users">Users</option>
            <option value="stories">Stories</option>
            <option value="emojis">Emojis</option>
          </select>
        </div>

        <!-- Date Range -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
          <select v-model="filters.dateRange" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
            <option value="all">All Time</option>
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
            <option value="custom">Custom Range</option>
          </select>

          <!-- Custom Date Range -->
          <div v-if="filters.dateRange === 'custom'" class="mt-3 grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs text-gray-400 mb-1">From</label>
              <input
                v-model="filters.customDateFrom"
                type="date"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-400 mb-1">To</label>
              <input
                v-model="filters.customDateTo"
                type="date"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- Sort By -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Sort By</label>
          <select v-model="filters.sortBy" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
            <option value="relevance">Relevance</option>
            <option value="newest">Newest First</option>
            <option value="oldest">Oldest First</option>
            <option value="popular">Most Popular</option>
          </select>
        </div>

        <!-- User Type -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">User Type</label>
          <select v-model="filters.userType" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
            <option value="all">All Users</option>
            <option value="verified">Verified Users</option>
            <option value="premium">NTA+ Users</option>
            <option value="regular">Regular Users</option>
          </select>
        </div>

        <!-- Content Filters -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-3">Content Filters</label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input v-model="filters.hasMedia" type="checkbox" class="sr-only peer">
              <div class="w-5 h-5 border-2 border-gray-600 rounded peer-checked:bg-neon-blue peer-checked:border-neon-blue flex items-center justify-center">
                <svg v-if="filters.hasMedia" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-dark-bg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="ml-3 text-sm">Has Media (Images/Videos)</span>
            </label>

            <label class="flex items-center">
              <input v-model="filters.hasLinks" type="checkbox" class="sr-only peer">
              <div class="w-5 h-5 border-2 border-gray-600 rounded peer-checked:bg-neon-blue peer-checked:border-neon-blue flex items-center justify-center">
                <svg v-if="filters.hasLinks" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-dark-bg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
              <span class="ml-3 text-sm">Has Links</span>
            </label>
          </div>
        </div>

        <!-- Engagement Filters -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-3">Engagement</label>
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label class="block text-xs text-gray-400 mb-1">Min Likes</label>
              <input
                v-model="filters.minLikes"
                type="number"
                min="0"
                placeholder="0"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-400 mb-1">Max Likes</label>
              <input
                v-model="filters.maxLikes"
                type="number"
                min="0"
                placeholder="∞"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-400 mb-1">Min Comments</label>
              <input
                v-model="filters.minComments"
                type="number"
                min="0"
                placeholder="0"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
            <div>
              <label class="block text-xs text-gray-400 mb-1">Max Comments</label>
              <input
                v-model="filters.maxComments"
                type="number"
                min="0"
                placeholder="∞"
                class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
              />
            </div>
          </div>
        </div>

        <!-- Tags -->
        <div>
          <label class="block text-sm font-medium text-gray-300 mb-2">Tags</label>
          <div class="flex gap-2 mb-2">
            <input
              v-model="newTag"
              @keyup.enter="addTag"
              type="text"
              placeholder="Add tag..."
              class="flex-1 bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
            />
            <button
              @click="addTag"
              class="px-4 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80"
            >
              Add
            </button>
          </div>
          <div v-if="filters.tags.length > 0" class="flex flex-wrap gap-2">
            <span
              v-for="(tag, index) in filters.tags"
              :key="index"
              class="inline-flex items-center gap-1 px-3 py-1 bg-neon-blue/20 text-neon-blue rounded-full text-sm"
            >
              {{ tag }}
              <button @click="removeTag(index)" class="hover:text-red-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </span>
          </div>
        </div>

        <!-- Advanced Text Filters -->
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Exact Phrase</label>
            <input
              v-model="filters.exactPhrase"
              type="text"
              placeholder="Search for exact phrase..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Exclude Words</label>
            <input
              v-model="filters.excludeWords"
              type="text"
              placeholder="Words to exclude (space separated)..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Location</label>
            <input
              v-model="filters.location"
              type="text"
              placeholder="Search by location..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-3 py-2 text-white focus:border-neon-blue focus:outline-none"
            />
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="p-6 border-t border-gray-800 flex justify-between items-center">
        <button
          @click="clearFilters"
          :disabled="!hasActiveFilters"
          class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Clear All
        </button>
        
        <div class="flex gap-3">
          <button
            @click="closeModal"
            class="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="applyFilters"
            class="px-6 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
