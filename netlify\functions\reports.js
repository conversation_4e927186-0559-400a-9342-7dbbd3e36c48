const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne } = require('./utils/db');
const { verifyToken } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');



exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const db = await connectToDatabase();
    const authHeader = event.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization token required' })
      };
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    if (!decoded) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token' })
      };
    }

    const userId = decoded.userId;
    const { httpMethod, path } = event;

    // POST /reports - Submit a report
    if (httpMethod === 'POST') {
      const {
        targetType,
        targetId,
        reason,
        additionalInfo,
        targetInfo
      } = JSON.parse(event.body);

      // Validate required fields
      if (!targetType || !targetId || !reason) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({
            error: 'Missing required fields: targetType, targetId, reason'
          })
        };
      }

      // Check if user has already reported this content
      const existingReport = await db.collection('reports').findOne({
        reportedBy: new ObjectId(userId),
        targetType,
        targetId: new ObjectId(targetId)
      });

      if (existingReport) {
        return {
          statusCode: 409,
          headers,
          body: JSON.stringify({
            error: 'You have already reported this content'
          })
        };
      }

      // Create the report
      const report = {
        reportedBy: new ObjectId(userId),
        targetType,
        targetId: new ObjectId(targetId),
        reason,
        additionalInfo: additionalInfo || '',
        targetInfo: targetInfo || {},
        status: 'pending',
        createdAt: new Date(),
        reviewedAt: null,
        reviewedBy: null,
        action: null
      };

      const result = await db.collection('reports').insertOne(report);

      // Populate reporter info for response
      const reportWithUser = await db.collection('reports').aggregate([
        { $match: { _id: result.insertedId } },
        {
          $lookup: {
            from: 'users',
            localField: 'reportedBy',
            foreignField: '_id',
            as: 'reportedBy'
          }
        },
        {
          $unwind: '$reportedBy'
        },
        {
          $project: {
            'reportedBy.password': 0,
            'reportedBy.email': 0
          }
        }
      ]).toArray();

      return {
        statusCode: 201,
        headers,
        body: JSON.stringify({
          success: true,
          message: 'Report submitted successfully',
          data: {
            report: reportWithUser[0]
          }
        })
      };
    }

    // GET /reports - Get user's reports (for users) or all reports (for admins)
    if (httpMethod === 'GET') {
      const user = await db.collection('users').findOne({ _id: new ObjectId(userId) });

      if (!user) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'User not found' })
        };
      }

      const { page = 1, limit = 20, status } = event.queryStringParameters || {};
      const skip = (parseInt(page) - 1) * parseInt(limit);

      let query = {};

      // If not admin, only show user's own reports
      if (!user.isAdmin) {
        query.reportedBy = new ObjectId(userId);
      }

      // Filter by status if provided
      if (status) {
        query.status = status;
      }

      const reports = await db.collection('reports').aggregate([
        { $match: query },
        {
          $lookup: {
            from: 'users',
            localField: 'reportedBy',
            foreignField: '_id',
            as: 'reportedBy'
          }
        },
        {
          $unwind: '$reportedBy'
        },
        {
          $lookup: {
            from: 'users',
            localField: 'reviewedBy',
            foreignField: '_id',
            as: 'reviewedBy'
          }
        },
        {
          $unwind: {
            path: '$reviewedBy',
            preserveNullAndEmptyArrays: true
          }
        },
        {
          $project: {
            'reportedBy.password': 0,
            'reportedBy.email': 0,
            'reviewedBy.password': 0,
            'reviewedBy.email': 0
          }
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: parseInt(limit) }
      ]).toArray();

      const totalCount = await db.collection('reports').countDocuments(query);
      const hasMore = skip + reports.length < totalCount;

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          data: {
            reports,
            hasMore,
            page: parseInt(page),
            totalCount
          }
        })
      };
    }

    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };

  } catch (error) {
    console.error('Reports function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        details: error.message
      })
    };
  }
};
