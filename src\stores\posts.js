import { defineStore } from 'pinia';
import { ref, computed, onUnmounted } from 'vue';
import { useAuthStore } from './auth';
import { useSubscriptionStore } from './subscription';

export const usePostsStore = defineStore('posts', () => {
  // State
  const posts = ref([]);
  const currentPost = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const page = ref(1);
  const hasMore = ref(true);
  const viewTimeTracker = ref(null);
  const currentViewStartTime = ref(null);
  const accumulatedViewTime = ref(0);

  // Getters
  const allPosts = computed(() => posts.value);
  const currentPostDetails = computed(() => currentPost.value);

  // Actions
  async function fetchPosts(refresh = false, options = {}) {
    const authStore = useAuthStore();
    if (!authStore.token) {
      console.error('No authentication token available');
      throw new Error('User not authenticated');
    }

    loading.value = true;
    error.value = null;

    try {
      // Reset page if refreshing
      if (refresh) {
        page.value = 1;
        posts.value = [];
        hasMore.value = true;
      }

      // Don't fetch if no more posts
      if (!hasMore.value && !refresh) {
        loading.value = false;
        return [];
      }

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page.value.toString(),
        limit: (options.limit || 10).toString()
      });

      // Add optional filters
      if (options.tag) {
        queryParams.append('tag', options.tag);
      }

      if (options.sortBy) {
        queryParams.append('sortBy', options.sortBy);
      }

      // Log the request for debugging
      console.log(`Fetching posts with token: ${authStore.token ? 'Token exists' : 'No token'}`);
      console.log(`Request URL: /.netlify/functions/posts/feed?${queryParams.toString()}`);

      // Make API request with proper error handling
      let response;
      try {
        response = await fetch(`/.netlify/functions/posts/feed?${queryParams.toString()}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authStore.token}`,
            'Content-Type': 'application/json'
          }
        });
      } catch (networkError) {
        console.error('Network error:', networkError);
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Log response status for debugging
      console.log(`Response status: ${response.status}`);

      // Parse response
      let data;
      try {
        data = await response.json();
        console.log('Response data:', data);
      } catch (parseError) {
        console.error('Parse error:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Handle error responses
      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
          console.error('Authentication error:', data.error);
          authStore.logout();
          throw new Error('Your session has expired. Please log in again.');
        }

        throw new Error(data.error || 'Failed to fetch posts');
      }

      // Validate response data
      if (!data.data || !Array.isArray(data.data.posts)) {
        console.error('Invalid response data:', data);
        throw new Error('Invalid response data from server');
      }

      // Update posts
      if (refresh) {
        posts.value = data.data.posts;
      } else {
        posts.value = [...posts.value, ...data.data.posts];
      }

      // Update pagination info
      hasMore.value = data.data.hasMore || false;

      // Increment page for next fetch
      if (hasMore.value) {
        page.value++;
      }

      return data.data.posts;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchPostById(postId) {
    const authStore = useAuthStore();

    loading.value = true;
    error.value = null;
    currentPost.value = null;

    try {
      // Validate postId
      if (!postId) {
        throw new Error('Post ID is required');
      }

      // Make API request with proper error handling
      let response;
      try {
        response = await fetch(`/.netlify/functions/posts/post?id=${encodeURIComponent(postId)}`, {
          method: 'GET',
          headers: authStore.token ? {
            'Authorization': `Bearer ${authStore.token}`
          } : {}
        });
      } catch (networkError) {
        console.error('Network error:', networkError);
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Parse response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Parse error:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Handle error responses
      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
          authStore.logout();
          throw new Error('Your session has expired. Please log in again.');
        }

        // Handle not found
        if (response.status === 404) {
          throw new Error('Post not found');
        }

        throw new Error(data.error || 'Failed to fetch post');
      }

      // Validate response data
      if (!data.data || !data.data.post) {
        throw new Error('Invalid response data from server');
      }

      // Set current post
      currentPost.value = data.data.post;

      // Track view count
      if (authStore.token) {
        try {
          // Increment view count on the server
          await fetch('/.netlify/functions/posts/view', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${authStore.token}`
            },
            body: JSON.stringify({ postId })
          });

          // Start tracking view time
          startViewTracking(postId);

          // Set up cleanup when component is unmounted
          onUnmounted(() => {
            stopViewTracking();
          });
        } catch (trackError) {
          // Don't fail the main request if tracking fails
          console.error('Error tracking post view:', trackError);
        }
      }

      return data.data.post;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function createPost(postData) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      // Validate post data
      if (!postData.content && (!postData.media || postData.media.length === 0)) {
        throw new Error('Post must contain content or media');
      }

      // Validate content length
      if (postData.content && postData.content.length > 5000) {
        throw new Error('Content exceeds maximum length of 5000 characters');
      }

      // Validate media count
      if (postData.media && postData.media.length > 10) {
        throw new Error('Maximum of 10 media items allowed per post');
      }

      // Validate media size
      if (postData.media && postData.media.length > 0) {
        for (const item of postData.media) {
          // Validate media type
          const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/quicktime'];
          if (!allowedTypes.includes(item.type)) {
            throw new Error(`Unsupported media type: ${item.type}. Allowed types: jpeg, png, gif, mp4, quicktime`);
          }

          // Validate media size (10MB limit)
          const sizeInMB = (item.data.length * 0.75) / 1024 / 1024; // Approximate size of base64 data
          if (sizeInMB > 10) {
            throw new Error('Media file exceeds 10MB size limit');
          }
        }
      }

      // Make API request with proper error handling
      let response;
      try {
        response = await fetch('/.netlify/functions/posts/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          },
          body: JSON.stringify(postData)
        });
      } catch (networkError) {
        console.error('Network error:', networkError);
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Parse response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Parse error:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Handle error responses
      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
          authStore.logout();
          throw new Error('Your session has expired. Please log in again.');
        }

        throw new Error(data.error || 'Failed to create post');
      }

      // Validate response data
      if (!data.data || !data.data.post) {
        throw new Error('Invalid response data from server');
      }

      // Add new post to the beginning of the posts array
      posts.value = [data.data.post, ...posts.value];

      return data.data.post;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function likePost(postId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    try {
      // Validate postId
      if (!postId) {
        throw new Error('Post ID is required');
      }

      // Make API request with proper error handling
      let response;
      try {
        response = await fetch('/.netlify/functions/posts/like', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          },
          body: JSON.stringify({ postId })
        });
      } catch (networkError) {
        console.error('Network error:', networkError);
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Parse response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Parse error:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Handle error responses
      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
          authStore.logout();
          throw new Error('Your session has expired. Please log in again.');
        }

        // Handle not found
        if (response.status === 404) {
          throw new Error('Post not found');
        }

        throw new Error(data.error || 'Failed to like post');
      }

      // Update post in the posts array
      const postIndex = posts.value.findIndex(p => p._id === postId);
      if (postIndex !== -1) {
        // Update like count
        posts.value[postIndex].likes = (posts.value[postIndex].likes || 0) + 1;
        // Set isLiked flag
        posts.value[postIndex].isLiked = true;
      }

      // Update current post if it's the one being liked
      if (currentPost.value && currentPost.value._id === postId) {
        // Update like count
        currentPost.value.likes = (currentPost.value.likes || 0) + 1;
        // Set isLiked flag
        currentPost.value.isLiked = true;
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  }

  async function unlikePost(postId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    try {
      // Validate postId
      if (!postId) {
        throw new Error('Post ID is required');
      }

      // Make API request with proper error handling
      let response;
      try {
        response = await fetch('/.netlify/functions/posts/unlike', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authStore.token}`
          },
          body: JSON.stringify({ postId })
        });
      } catch (networkError) {
        console.error('Network error:', networkError);
        throw new Error('Network error. Please check your connection and try again.');
      }

      // Parse response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error('Parse error:', parseError);
        throw new Error('Failed to parse server response');
      }

      // Handle error responses
      if (!response.ok) {
        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
          authStore.logout();
          throw new Error('Your session has expired. Please log in again.');
        }

        // Handle not found
        if (response.status === 404) {
          throw new Error('Post not found');
        }

        throw new Error(data.error || 'Failed to unlike post');
      }

      // Update post in the posts array
      const postIndex = posts.value.findIndex(p => p._id === postId);
      if (postIndex !== -1 && posts.value[postIndex].likes > 0) {
        // Update like count
        posts.value[postIndex].likes--;
        // Set isLiked flag
        posts.value[postIndex].isLiked = false;
      }

      // Update current post if it's the one being unliked
      if (currentPost.value && currentPost.value._id === postId && currentPost.value.likes > 0) {
        // Update like count
        currentPost.value.likes--;
        // Set isLiked flag
        currentPost.value.isLiked = false;
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  }

  // Start tracking view time for a post
  function startViewTracking(postId) {
    if (!postId) return;

    // Clear any existing tracker
    stopViewTracking();

    // Set the current post being viewed
    currentViewStartTime.value = Date.now();

    // Set up interval to periodically update view time (every 5 seconds)
    viewTimeTracker.value = setInterval(async () => {
      if (!currentViewStartTime.value) return;

      const viewDuration = Math.floor((Date.now() - currentViewStartTime.value) / 1000);
      accumulatedViewTime.value += viewDuration;

      // Reset start time for next interval
      currentViewStartTime.value = Date.now();

      // Only send updates to server after significant time (10+ seconds)
      if (accumulatedViewTime.value >= 10) {
        await updateViewTime(postId, accumulatedViewTime.value);
        accumulatedViewTime.value = 0;
      }
    }, 5000);
  }

  // Stop tracking view time
  function stopViewTracking() {
    if (viewTimeTracker.value) {
      clearInterval(viewTimeTracker.value);
      viewTimeTracker.value = null;
    }

    // If we have accumulated time, send a final update
    if (currentViewStartTime.value && currentPost.value) {
      const finalViewDuration = Math.floor((Date.now() - currentViewStartTime.value) / 1000);
      const totalTime = accumulatedViewTime.value + finalViewDuration;

      if (totalTime > 0) {
        updateViewTime(currentPost.value._id, totalTime);
      }
    }

    // Reset tracking variables
    currentViewStartTime.value = null;
    accumulatedViewTime.value = 0;
  }

  // Update view time on the server
  async function updateViewTime(postId, seconds) {
    const authStore = useAuthStore();
    if (!authStore.token || !postId || seconds <= 0) return;

    try {
      await fetch('/.netlify/functions/posts/view-time', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ postId, seconds })
      });
    } catch (err) {
      console.error('Failed to update view time:', err);
      // Don't throw error to avoid disrupting user experience
    }
  }

  // Share a post using Web Share API or fallback to clipboard
  async function sharePost(post) {
    if (!post) return { success: false, message: 'No post to share' };

    try {
      // Check if Web Share API is available
      if (navigator.share) {
        await navigator.share({
          title: `Post by ${post.author.username} on NeTuArk`,
          text: post.content.substring(0, 100) + (post.content.length > 100 ? '...' : ''),
          url: `${window.location.origin}/post/${post._id}`
        });

        // Update share count on success
        try {
          await fetch('/.netlify/functions/posts/share', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ postId: post._id })
          });

          // Update local share count
          const postIndex = posts.value.findIndex(p => p._id === post._id);
          if (postIndex !== -1) {
            posts.value[postIndex].shareCount = (posts.value[postIndex].shareCount || 0) + 1;
          }

          if (currentPost.value && currentPost.value._id === post._id) {
            currentPost.value.shareCount = (currentPost.value.shareCount || 0) + 1;
          }
        } catch (err) {
          console.error('Failed to update share count:', err);
        }

        return { success: true, message: 'Post shared successfully' };
      } else {
        // Fallback to clipboard
        const shareUrl = `${window.location.origin}/post/${post._id}`;
        await navigator.clipboard.writeText(shareUrl);
        return { success: true, message: 'Link copied to clipboard' };
      }
    } catch (err) {
      console.error('Share failed:', err);

      // Try clipboard as fallback if sharing failed
      try {
        const shareUrl = `${window.location.origin}/post/${post._id}`;
        await navigator.clipboard.writeText(shareUrl);
        return { success: true, message: 'Link copied to clipboard' };
      } catch (clipboardErr) {
        console.error('Clipboard fallback failed:', clipboardErr);
        return { success: false, message: 'Failed to share post' };
      }
    }
  }

  // Add a comment to a post
  async function addComment(postId, content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    if (!postId) throw new Error('Post ID is required');
    if (!content || content.trim() === '') throw new Error('Comment content is required');

    loading.value = true;
    error.value = null;

    try {
      // Extract @mentions from content
      const mentionRegex = /@(\w+)/g;
      const mentions = [];
      let match;

      while ((match = mentionRegex.exec(content)) !== null) {
        mentions.push(match[1]); // Extract username without @
      }

      // Make API request
      const response = await fetch('/.netlify/functions/posts/comment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          postId,
          content,
          mentions
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to add comment');
      }

      // Update current post with new comment
      if (currentPost.value && currentPost.value._id === postId) {
        // If comments array doesn't exist, create it
        if (!currentPost.value.comments) {
          currentPost.value.comments = [];
        }

        // Add new comment to the array
        currentPost.value.comments.push(data.data.comment);
      }

      return data.data.comment;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  // Delete a comment
  async function deleteComment(postId, commentId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    if (!postId) throw new Error('Post ID is required');
    if (!commentId) throw new Error('Comment ID is required');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/posts/delete-comment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          postId,
          commentId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete comment');
      }

      // Update current post by removing the deleted comment
      if (currentPost.value && currentPost.value._id === postId && currentPost.value.comments) {
        currentPost.value.comments = currentPost.value.comments.filter(
          comment => comment._id !== commentId
        );
      }

      return { success: true };
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  return {
    // State
    posts,
    currentPost,
    loading,
    error,
    page,
    hasMore,

    // Getters
    allPosts,
    currentPostDetails,

    // Actions
    fetchPosts,
    fetchPostById,
    createPost,
    likePost,
    unlikePost,
    startViewTracking,
    stopViewTracking,
    sharePost,
    addComment,
    deleteComment
  };
});
