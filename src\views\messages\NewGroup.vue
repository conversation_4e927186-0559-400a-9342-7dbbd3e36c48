<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// Reactive data
const loading = ref(false);
const error = ref(null);
const groupName = ref('');
const groupDescription = ref('');
const selectedUsers = ref([]);
const searchQuery = ref('');
const availableUsers = ref([]);
const creatingGroup = ref(false);

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Computed
const filteredUsers = computed(() => {
  if (!searchQuery.value) return availableUsers.value;
  
  return availableUsers.value.filter(user =>
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.displayName?.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const isFormValid = computed(() => {
  return groupName.value.trim().length > 0 && selectedUsers.value.length > 0;
});

// Methods
const fetchUsers = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await fetch('/.netlify/functions/discover/users', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch users');
    }

    availableUsers.value = data.data.users || [];

  } catch (err) {
    console.error('Error fetching users:', err);
    error.value = 'Failed to load users. Please try again.';
  } finally {
    loading.value = false;
  }
};

const toggleUserSelection = (user) => {
  const index = selectedUsers.value.findIndex(u => u._id === user._id);
  
  if (index > -1) {
    selectedUsers.value.splice(index, 1);
  } else {
    selectedUsers.value.push(user);
  }
};

const isUserSelected = (user) => {
  return selectedUsers.value.some(u => u._id === user._id);
};

const removeSelectedUser = (user) => {
  const index = selectedUsers.value.findIndex(u => u._id === user._id);
  if (index > -1) {
    selectedUsers.value.splice(index, 1);
  }
};

const createGroup = async () => {
  if (!isFormValid.value || creatingGroup.value) return;

  creatingGroup.value = true;
  error.value = null;

  try {
    const response = await fetch('/.netlify/functions/messages/create-group', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({
        name: groupName.value.trim(),
        description: groupDescription.value.trim(),
        members: selectedUsers.value.map(user => user._id)
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to create group');
    }

    // Navigate to the new group conversation
    router.push(`/messages/${data.data.conversationId}`);

  } catch (err) {
    console.error('Error creating group:', err);
    error.value = err.message;
  } finally {
    creatingGroup.value = false;
  }
};

// Lifecycle
onMounted(() => {
  fetchUsers();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">New Group</h1>
        </div>

        <button
          @click="createGroup"
          :disabled="!isFormValid || creatingGroup"
          class="bg-primary text-dark-bg px-4 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="creatingGroup">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating...
          </span>
          <span v-else>Create Group</span>
        </button>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Group Details -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6 mb-6">
        <h2 class="text-lg font-medium mb-4">Group Details</h2>
        
        <div class="space-y-4">
          <div>
            <label for="groupName" class="block text-sm font-medium text-gray-300 mb-2">
              Group Name *
            </label>
            <input
              id="groupName"
              v-model="groupName"
              type="text"
              placeholder="Enter group name..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors"
              maxlength="50"
            />
          </div>

          <div>
            <label for="groupDescription" class="block text-sm font-medium text-gray-300 mb-2">
              Description (Optional)
            </label>
            <textarea
              id="groupDescription"
              v-model="groupDescription"
              placeholder="Enter group description..."
              rows="3"
              class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors resize-none"
              maxlength="200"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- Selected Members -->
      <div v-if="selectedUsers.length > 0" class="bg-darker-bg rounded-xl border border-gray-800 p-6 mb-6">
        <h2 class="text-lg font-medium mb-4">Selected Members ({{ selectedUsers.length }})</h2>
        
        <div class="flex flex-wrap gap-2">
          <div
            v-for="user in selectedUsers"
            :key="user._id"
            class="flex items-center gap-2 bg-primary/10 border border-primary/30 rounded-lg px-3 py-2"
          >
            <img
              :src="user.profilePicture || getDefaultAvatar()"
              :alt="user.username"
              class="w-6 h-6 rounded-full object-cover"
            />
            <span class="text-sm text-primary">{{ user.username }}</span>
            <button
              @click="removeSelectedUser(user)"
              class="text-primary hover:text-red-400 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Add Members -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-lg font-medium mb-4">Add Members</h2>
        
        <!-- Search Bar -->
        <div class="mb-4">
          <div class="relative">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search users..."
              class="w-full bg-dark-bg border border-gray-700 rounded-lg pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-primary transition-colors"
            />
          </div>
        </div>

        <!-- Error State -->
        <div v-if="error" class="bg-red-900/20 border border-red-500/30 rounded-lg p-4 mb-4">
          <p class="text-red-400 text-sm">{{ error }}</p>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="space-y-3">
          <div v-for="i in 3" :key="i" class="flex items-center space-x-3 p-3 animate-pulse">
            <div class="w-10 h-10 bg-gray-700 rounded-full"></div>
            <div class="flex-1 space-y-2">
              <div class="h-4 bg-gray-700 rounded w-1/4"></div>
              <div class="h-3 bg-gray-700 rounded w-1/2"></div>
            </div>
            <div class="w-16 h-8 bg-gray-700 rounded"></div>
          </div>
        </div>

        <!-- Users List -->
        <div v-else-if="filteredUsers.length > 0" class="space-y-2 max-h-96 overflow-y-auto">
          <div
            v-for="user in filteredUsers"
            :key="user._id"
            class="flex items-center justify-between p-3 hover:bg-gray-800 rounded-lg transition-colors"
          >
            <div class="flex items-center space-x-3">
              <img
                :src="user.profilePicture || getDefaultAvatar()"
                :alt="user.username"
                class="w-10 h-10 rounded-full object-cover"
              />
              <div>
                <div class="font-medium text-white">{{ user.displayName || user.username }}</div>
                <div class="text-sm text-gray-400">@{{ user.username }}</div>
              </div>
            </div>

            <button
              @click="toggleUserSelection(user)"
              :class="[
                'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                isUserSelected(user)
                  ? 'bg-red-600 text-white hover:bg-red-700'
                  : 'bg-primary text-dark-bg hover:bg-primary/90'
              ]"
            >
              {{ isUserSelected(user) ? 'Remove' : 'Add' }}
            </button>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          <h3 class="text-lg font-medium text-gray-300 mb-2">
            {{ searchQuery ? 'No users found' : 'No users available' }}
          </h3>
          <p class="text-gray-400">
            {{ searchQuery ? 'Try a different search term' : 'Check back later!' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
