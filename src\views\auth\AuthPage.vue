<script setup>
import { ref, computed } from 'vue';
import { useRouter, useRoute, RouterLink } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { useToastStore } from '../../stores/toast';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const toastStore = useToastStore();

// State
const isLogin = ref(true);
const email = ref('');
const username = ref('');
const password = ref('');
const confirmPassword = ref('');
const rememberMe = ref(false);
const agreeTerms = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// Computed properties
const isFormValid = computed(() => {
  if (isLogin.value) {
    return email.value && password.value;
  } else {
    return email.value && username.value && password.value &&
           confirmPassword.value && password.value === confirmPassword.value && agreeTerms.value;
  }
});

const passwordsMatch = computed(() => {
  return password.value === confirmPassword.value;
});

// Toggle between login and register
const toggleMode = () => {
  isLogin.value = !isLogin.value;
  errorMessage.value = '';
  successMessage.value = '';
};

// Handle form submission
const handleSubmit = async () => {
  if (!isFormValid.value || isSubmitting.value) return;

  errorMessage.value = '';
  successMessage.value = '';
  isSubmitting.value = true;

  try {
    if (isLogin.value) {
      await authStore.login({
        email: email.value,
        password: password.value
      });

      // Redirect to the intended page or feed
      const redirectPath = route.query.redirect || '/feed';
      successMessage.value = 'Login successful! Redirecting...';
      toastStore.success('Welcome back to NeTuArk!');

      setTimeout(() => {
        router.push(redirectPath);
      }, 1000);
    } else {
      const result = await authStore.register({
        username: username.value,
        email: email.value,
        password: password.value
      });

      if (result.status === 'processing') {
        successMessage.value = 'Your account is being created. Please try logging in after a few moments.';
        toastStore.info('Account creation in progress...');
        setTimeout(() => {
          isLogin.value = true;
        }, 3000);
      } else {
        successMessage.value = 'Registration successful! Welcome to NeTuArk!';
        toastStore.success('Welcome to NeTuArk! Your account has been created.');
        setTimeout(() => {
          router.push('/feed');
        }, 1500);
      }
    }
  } catch (error) {
    console.error('Auth error:', error);
    if (error && typeof error.message === 'string' && error.message.trim() !== '') {
      errorMessage.value = error.message;
    } else if (typeof error === 'string' && error.trim() !== '') {
      errorMessage.value = error;
    } else {
      errorMessage.value = isLogin.value ? 'Login failed. Please try again.' : 'Registration failed. Please try again.';
    }
    // console.error('Auth error:', error); // This line is already present in the original code
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<template>
  <div class="min-h-screen flex flex-col justify-center items-center px-4 py-12 bg-dark-bg">
    <div class="w-full max-w-md">
      <!-- Logo -->
      <div class="flex justify-center mb-8">
        <img src="/logo2.png" alt="NeTuArk Logo" class="h-12" />
      </div>

      <!-- Auth Form -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 shadow-lg p-8">
        <h2 class="text-2xl font-bold mb-6 text-center">
          {{ isLogin ? 'Welcome Back' : 'Join NeTuArk' }}
        </h2>

        <!-- Success message -->
        <div v-if="successMessage" class="mb-6 p-3 bg-green-900/30 border border-green-800 rounded-md text-green-200 text-sm">
          {{ successMessage }}
        </div>

        <!-- Error message -->
        <div v-if="errorMessage" class="mb-6 p-3 bg-red-900/30 border border-red-800 rounded-md text-red-200 text-sm">
          {{ errorMessage }}
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Username (Register only) -->
          <div v-if="!isLogin">
            <label for="username" class="block text-sm font-medium text-gray-300 mb-1">Username</label>
            <input
              id="username"
              v-model="username"
              type="text"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="Choose a username"
            />
          </div>

          <!-- Email -->
          <div>
            <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Password -->
          <div>
            <label for="password" class="block text-sm font-medium text-gray-300 mb-1">Password</label>
            <input
              id="password"
              v-model="password"
              type="password"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              placeholder="••••••••"
            />
          </div>

          <!-- Confirm Password (Register only) -->
          <div v-if="!isLogin">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-1">Confirm Password</label>
            <input
              id="confirmPassword"
              v-model="confirmPassword"
              type="password"
              required
              class="w-full px-4 py-3 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
              :class="{ 'border-red-500': confirmPassword && !passwordsMatch }"
              placeholder="••••••••"
            />
            <p v-if="confirmPassword && !passwordsMatch" class="mt-1 text-sm text-red-400">
              Passwords do not match
            </p>
          </div>

          <!-- Remember Me (Login only) -->
          <div v-if="isLogin" class="flex items-center">
            <input
              id="rememberMe"
              v-model="rememberMe"
              type="checkbox"
              class="h-4 w-4 text-neon-blue focus:ring-neon-blue border-gray-700 rounded bg-dark-bg"
            />
            <label for="rememberMe" class="ml-2 block text-sm text-gray-300">
              Remember me
            </label>
          </div>

          <!-- Terms Agreement (Register only) -->
          <div v-if="!isLogin" class="flex items-start">
            <input
              id="agreeTerms"
              v-model="agreeTerms"
              type="checkbox"
              required
              class="h-4 w-4 text-neon-blue focus:ring-neon-blue border-gray-700 rounded bg-dark-bg mt-1"
            />
            <label for="agreeTerms" class="ml-2 block text-sm text-gray-300">
              I agree to the <router-link to="/terms-of-service" class="text-neon-blue hover:underline">Terms of Service</router-link> and
              <router-link to="/privacy-policy" class="text-neon-blue hover:underline">Privacy Policy</router-link>
            </label>
          </div>

          <!-- Submit button -->
          <div>
            <button
              type="submit"
              :disabled="!isFormValid || isSubmitting"
              class="w-full neon-button py-3 px-4 flex justify-center rounded-full transition-all duration-200 transform hover:scale-105"
              :class="{ 'opacity-70 cursor-not-allowed': !isFormValid || isSubmitting }"
            >
              <span v-if="isSubmitting">{{ isLogin ? 'Logging in...' : 'Creating account...' }}</span>
              <span v-else>{{ isLogin ? 'Log in' : 'Create Account' }}</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Toggle Mode -->
      <div class="mt-6 text-center">
        <p class="text-gray-400">
          {{ isLogin ? "Don't have an account?" : "Already have an account?" }}
        </p>
        <button
          @click="toggleMode"
          class="mt-3 inline-block px-6 py-2 rounded-full bg-neon-purple hover:bg-neon-purple/80 text-white font-medium transition-all duration-200 transform hover:scale-105"
        >
          {{ isLogin ? 'Create Account' : 'Log In' }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.neon-button {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  color: #0a0a0a;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.neon-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #00d4ff, #0099cc);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  transform: translateY(-2px);
}
</style>
