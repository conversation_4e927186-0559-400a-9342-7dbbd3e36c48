import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useStoriesStore = defineStore('stories', () => {
  // State
  const stories = ref([]);
  const myStories = ref([]);
  const currentStoryGroup = ref(null);
  const currentStoryIndex = ref(0);
  const loading = ref(false);
  const error = ref(null);
  
  // Getters
  const allStories = computed(() => stories.value);
  const userStories = computed(() => myStories.value);
  const activeStories = computed(() => stories.value.filter(group => 
    group.stories.some(story => !story.viewed)
  ));
  const currentStory = computed(() => {
    if (!currentStoryGroup.value) return null;
    return currentStoryGroup.value.stories[currentStoryIndex.value] || null;
  });
  
  // Actions
  async function fetchStories() {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/stories/feed', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch stories');
      }
      
      // Update stories
      stories.value = data.data.stories;
      
      return data.data.stories;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchMyStories() {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/stories/my-stories', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch my stories');
      }
      
      // Update my stories
      myStories.value = data.data.stories;
      
      return data.data.stories;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function createStory(storyData) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/stories/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(storyData)
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create story');
      }
      
      // Refresh stories
      await fetchMyStories();
      await fetchStories();
      
      return data.data.story;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function viewStory(storyId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    try {
      const response = await fetch('/.netlify/functions/stories/view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ storyId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to mark story as viewed');
      }
      
      // Update story in the stories array
      stories.value = stories.value.map(group => {
        const updatedStories = group.stories.map(story => {
          if (story._id === storyId) {
            return { ...story, viewed: true };
          }
          return story;
        });
        
        return { ...group, stories: updatedStories };
      });
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    }
  }
  
  function setCurrentStoryGroup(storyGroup) {
    currentStoryGroup.value = storyGroup;
    currentStoryIndex.value = 0;
    
    // Mark the first story as viewed
    if (storyGroup && storyGroup.stories.length > 0) {
      viewStory(storyGroup.stories[0]._id);
    }
  }
  
  function nextStory() {
    if (!currentStoryGroup.value) return;
    
    if (currentStoryIndex.value < currentStoryGroup.value.stories.length - 1) {
      currentStoryIndex.value++;
      
      // Mark the story as viewed
      viewStory(currentStoryGroup.value.stories[currentStoryIndex.value]._id);
    } else {
      // Move to the next story group
      const currentGroupIndex = stories.value.findIndex(
        group => group.author._id === currentStoryGroup.value.author._id
      );
      
      if (currentGroupIndex !== -1 && currentGroupIndex < stories.value.length - 1) {
        setCurrentStoryGroup(stories.value[currentGroupIndex + 1]);
      } else {
        // No more story groups
        currentStoryGroup.value = null;
      }
    }
  }
  
  function previousStory() {
    if (!currentStoryGroup.value) return;
    
    if (currentStoryIndex.value > 0) {
      currentStoryIndex.value--;
    } else {
      // Move to the previous story group
      const currentGroupIndex = stories.value.findIndex(
        group => group.author._id === currentStoryGroup.value.author._id
      );
      
      if (currentGroupIndex > 0) {
        const prevGroup = stories.value[currentGroupIndex - 1];
        currentStoryGroup.value = prevGroup;
        currentStoryIndex.value = prevGroup.stories.length - 1;
      }
    }
  }
  
  function closeStories() {
    currentStoryGroup.value = null;
    currentStoryIndex.value = 0;
  }
  
  return {
    // State
    stories,
    myStories,
    currentStoryGroup,
    currentStoryIndex,
    loading,
    error,
    
    // Getters
    allStories,
    userStories,
    activeStories,
    currentStory,
    
    // Actions
    fetchStories,
    fetchMyStories,
    createStory,
    viewStory,
    setCurrentStoryGroup,
    nextStory,
    previousStory,
    closeStories
  };
});
