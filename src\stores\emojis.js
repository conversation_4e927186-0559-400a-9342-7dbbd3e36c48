import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useEmojisStore = defineStore('emojis', () => {
  // State
  const userEmojis = ref([]);
  const favoriteEmojis = ref([]);
  const popularEmojis = ref([]);
  const emojiCount = ref(0);
  const emojiLimit = ref(10);
  const favoriteCount = ref(0);
  const favoriteLimit = ref(15);
  const loading = ref(false);
  const error = ref(null);
  
  // Getters
  const allUserEmojis = computed(() => userEmojis.value);
  const allFavoriteEmojis = computed(() => favoriteEmojis.value);
  const allPopularEmojis = computed(() => popularEmojis.value);
  const hasReachedEmojiLimit = computed(() => {
    const authStore = useAuthStore();
    return !authStore.isPremium && emojiCount.value >= emojiLimit.value;
  });
  const hasReachedFavoriteLimit = computed(() => {
    const authStore = useAuthStore();
    return !authStore.isPremium && favoriteCount.value >= favoriteLimit.value;
  });
  const remainingEmojis = computed(() => {
    const authStore = useAuthStore();
    return authStore.isPremium ? 'unlimited' : (emojiLimit.value - emojiCount.value);
  });
  
  // Actions
  async function fetchUserEmojis() {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/user', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch user emojis');
      }
      
      // Update user emojis
      userEmojis.value = data.data.emojis;
      emojiCount.value = data.data.emojis.length;
      
      return data.data.emojis;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchFavoriteEmojis() {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/favorites', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch favorite emojis');
      }
      
      // Update favorite emojis
      favoriteEmojis.value = data.data.emojis;
      favoriteCount.value = data.data.emojis.length;
      
      return data.data.emojis;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function fetchPopularEmojis(page = 1, limit = 20) {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch(`/.netlify/functions/emojis/popular?page=${page}&limit=${limit}`, {
        method: 'GET'
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch popular emojis');
      }
      
      // Update popular emojis
      popularEmojis.value = data.data.emojis;
      
      return data.data.emojis;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function createEmoji(emojiData) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(emojiData)
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create emoji');
      }
      
      // Add new emoji to user emojis
      userEmojis.value = [data.data.emoji, ...userEmojis.value];
      
      // Update emoji count and limit
      emojiCount.value = data.data.emojiCount;
      if (data.data.emojiLimit !== 'unlimited') {
        emojiLimit.value = data.data.emojiLimit;
      }
      
      return data.data.emoji;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function favoriteEmoji(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to favorite emoji');
      }
      
      // Update favorite count and limit
      favoriteCount.value = data.data.favoriteCount;
      if (data.data.favoriteLimit !== 'unlimited') {
        favoriteLimit.value = data.data.favoriteLimit;
      }
      
      // Refresh favorite emojis
      await fetchFavoriteEmojis();
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function unfavoriteEmoji(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/unfavorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to unfavorite emoji');
      }
      
      // Remove emoji from favorites
      favoriteEmojis.value = favoriteEmojis.value.filter(emoji => emoji._id !== emojiId);
      favoriteCount.value--;
      
      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  async function useEmoji(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;
    
    try {
      const response = await fetch('/.netlify/functions/emojis/use', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        console.error(data.error || 'Failed to record emoji usage');
      }
      
      return data.data;
    } catch (err) {
      console.error(err.message);
    }
  }
  
  // Initialize by fetching emojis if user is authenticated
  const authStore = useAuthStore();
  if (authStore.isAuthenticated) {
    fetchUserEmojis();
    fetchFavoriteEmojis();
  }
  
  // Always fetch popular emojis
  fetchPopularEmojis();
  
  return {
    // State
    userEmojis,
    favoriteEmojis,
    popularEmojis,
    emojiCount,
    emojiLimit,
    favoriteCount,
    favoriteLimit,
    loading,
    error,
    
    // Getters
    allUserEmojis,
    allFavoriteEmojis,
    allPopularEmojis,
    hasReachedEmojiLimit,
    hasReachedFavoriteLimit,
    remainingEmojis,
    
    // Actions
    fetchUserEmojis,
    fetchFavoriteEmojis,
    fetchPopularEmojis,
    createEmoji,
    favoriteEmoji,
    unfavoriteEmoji,
    useEmoji
  };
});
