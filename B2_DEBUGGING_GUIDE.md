# 🔧 Backblaze B2 Debugging Guide for NeTuArk

## 🚨 **Current Issue**
Posts creation still returning 500 Internal Server Error despite B2 environment variables being set.

## 🔍 **Debugging Steps**

### **1. Test B2 Configuration**
First, test if B2 is properly configured:

```bash
# Visit this URL to test B2 configuration
https://netuark.netlify.app/.netlify/functions/test-b2
```

This endpoint will:
- ✅ Check all required environment variables
- ✅ Test B2 authorization
- ✅ Test upload URL generation
- ✅ Test bucket access

### **2. Check Netlify Environment Variables**
Ensure these are set in Netlify Dashboard → Site Settings → Environment Variables:

```
B2_KEY_ID=your_backblaze_key_id
B2_APP_KEY=your_backblaze_application_key
B2_BUCKET_ID=your_bucket_id
B2_BUCKET_NAME=your_bucket_name
```

### **3. Verify B2 Bucket Configuration**
In your Backblaze B2 console:
- ✅ Bucket exists and is accessible
- ✅ Application key has read/write permissions
- ✅ Bucket is set to "Public" or has proper CORS settings
- ✅ File name prefix restrictions (if any) allow "posts/" prefix

### **4. Test Posts Without Media**
Try creating a text-only post first:

```javascript
// In the browser console on /create page
fetch('/.netlify/functions/posts/create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  },
  body: JSON.stringify({
    content: 'Test post without media',
    visibility: 'public'
  })
})
.then(r => r.json())
.then(console.log)
.catch(console.error);
```

### **5. Check Netlify Function Logs**
1. Go to Netlify Dashboard → Functions
2. Click on the failing function
3. Check the logs for detailed error messages

## 🛠 **Enhanced Error Handling**

The storage utility now includes comprehensive logging:

```javascript
// Storage logs will show:
Storage: Getting B2 authorization...
Storage: Initializing B2 client...
Storage: Authorizing with B2...
Storage: B2 authorization successful
Storage: Getting upload URL...
Storage: Requesting upload URL for bucket [BUCKET_ID]...
Storage: Upload URL obtained successfully
Storage: Starting upload for [filename], size: [bytes], type: [content-type]
Storage: Uploading file to B2...
Storage: Upload successful - [file_url]
```

## 🔧 **Common Issues & Solutions**

### **Issue 1: Invalid Credentials**
```
Error: B2 authorization failed: unauthorized
```
**Solution**: Double-check B2_KEY_ID and B2_APP_KEY in Netlify environment variables

### **Issue 2: Bucket Not Found**
```
Error: Failed to get upload URL: bucket not found
```
**Solution**: Verify B2_BUCKET_ID is correct and bucket exists

### **Issue 3: Permission Denied**
```
Error: File upload failed: access denied
```
**Solution**: Ensure application key has read/write permissions for the bucket

### **Issue 4: CORS Issues**
```
Error: CORS policy blocked
```
**Solution**: Configure CORS in B2 bucket settings:
```json
[
  {
    "corsRuleName": "downloadFromAnyOrigin",
    "allowedOrigins": ["*"],
    "allowedHeaders": ["*"],
    "allowedOperations": ["b2_download_file_by_id", "b2_download_file_by_name"],
    "maxAgeSeconds": 3600
  }
]
```

### **Issue 5: File Size Limits**
```
Error: File too large
```
**Solution**: Check if B2 bucket has file size restrictions

## 📊 **Environment Variables Checklist**

| Variable | Required | Description | Example |
|----------|----------|-------------|---------|
| `B2_KEY_ID` | ✅ | Application Key ID | `0123456789abcdef` |
| `B2_APP_KEY` | ✅ | Application Key | `K0123456789abcdef...` |
| `B2_BUCKET_ID` | ✅ | Bucket ID | `a1b2c3d4e5f6g7h8` |
| `B2_BUCKET_NAME` | ✅ | Bucket Name | `netuark-media` |

## 🚀 **Testing Workflow**

1. **Test B2 Configuration**: Visit `/test-b2` endpoint
2. **Test Text Post**: Create post without media
3. **Test Small Image**: Upload small image (< 1MB)
4. **Test Large Image**: Upload larger image (< 10MB)
5. **Test Video**: Upload video file

## 📝 **Debug Commands**

### **Check Environment Variables**
```javascript
// In Netlify Function
console.log('Environment check:', {
  B2_KEY_ID: process.env.B2_KEY_ID ? 'SET' : 'NOT_SET',
  B2_APP_KEY: process.env.B2_APP_KEY ? 'SET' : 'NOT_SET',
  B2_BUCKET_ID: process.env.B2_BUCKET_ID ? 'SET' : 'NOT_SET',
  B2_BUCKET_NAME: process.env.B2_BUCKET_NAME ? 'SET' : 'NOT_SET'
});
```

### **Test B2 Authorization**
```javascript
// In test-b2.js function
const { getAuthorizedB2 } = require('./utils/storage');
const b2 = await getAuthorizedB2();
console.log('B2 authorized:', !!b2);
```

## 🎯 **Next Steps**

1. **Run B2 Test**: Visit the test endpoint to verify configuration
2. **Check Logs**: Review Netlify function logs for specific errors
3. **Test Incrementally**: Start with text posts, then add media
4. **Verify Permissions**: Ensure B2 application key has proper permissions
5. **Check CORS**: Configure bucket CORS if needed

## 📞 **Support Information**

If issues persist:
1. Check Netlify function logs for specific error messages
2. Verify B2 bucket permissions and settings
3. Test with smaller file sizes first
4. Ensure all environment variables are properly set

The enhanced error handling will provide detailed logs to help identify the exact point of failure in the upload process.
