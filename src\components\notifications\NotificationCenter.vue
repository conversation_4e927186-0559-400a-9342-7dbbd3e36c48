<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useNotificationsStore } from '../../stores/notifications';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const notificationsStore = useNotificationsStore();
const authStore = useAuthStore();

const showDropdown = ref(false);
const showNotificationsPage = ref(false);

// Computed properties
const hasUnread = computed(() => notificationsStore.unreadCount > 0);
const unreadCount = computed(() => notificationsStore.unreadCount);
const recentNotifications = computed(() => 
  notificationsStore.allNotifications.slice(0, 5)
);

// Toggle dropdown
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
  if (showDropdown.value) {
    notificationsStore.fetchNotifications(true);
  }
};

// Close dropdown
const closeDropdown = () => {
  showDropdown.value = false;
};

// Handle notification click
const handleNotificationClick = async (notification) => {
  // Mark as read
  if (!notification.read) {
    await notificationsStore.markAsRead(notification._id);
  }

  // Navigate based on notification type
  switch (notification.type) {
    case 'like':
    case 'comment':
      if (notification.postId) {
        router.push(`/post/${notification.postId}`);
      }
      break;
    case 'follow':
      if (notification.fromUser?.username) {
        router.push(`/profile/${notification.fromUser.username}`);
      }
      break;
    case 'message':
      router.push('/messages');
      break;
    case 'story':
      router.push('/stories');
      break;
    default:
      break;
  }

  closeDropdown();
};

// Show all notifications
const showAllNotifications = () => {
  showNotificationsPage.value = true;
  closeDropdown();
};

// Mark all as read
const markAllAsRead = async () => {
  await notificationsStore.markAllAsRead();
};

onMounted(() => {
  // Fetch initial notifications
  notificationsStore.fetchNotifications(true);
});
</script>

<template>
  <div class="relative">
    <!-- Notification Bell Button -->
    <button
      @click="toggleDropdown"
      class="relative p-2 text-gray-400 hover:text-white transition-colors"
      :class="{ 'text-neon-blue': hasUnread }"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>
      
      <!-- Unread count badge -->
      <span
        v-if="hasUnread"
        class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
      >
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="showDropdown"
      class="absolute right-0 mt-2 w-80 bg-darker-bg border border-gray-800 rounded-xl shadow-xl z-50"
      @click.stop
    >
      <!-- Header -->
      <div class="p-4 border-b border-gray-800 flex justify-between items-center">
        <h3 class="text-lg font-semibold">Notifications</h3>
        <div class="flex items-center gap-2">
          <button
            v-if="hasUnread"
            @click="markAllAsRead"
            class="text-sm text-neon-blue hover:text-neon-blue/80"
          >
            Mark all read
          </button>
          <button
            @click="closeDropdown"
            class="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Notifications List -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="notificationsStore.loading" class="p-4 text-center">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-neon-blue mx-auto"></div>
        </div>

        <div v-else-if="recentNotifications.length === 0" class="p-8 text-center text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
          </svg>
          <p>No notifications yet</p>
        </div>

        <div v-else>
          <div
            v-for="notification in recentNotifications"
            :key="notification._id"
            @click="handleNotificationClick(notification)"
            class="p-4 border-b border-gray-800 hover:bg-dark-bg cursor-pointer transition-colors"
            :class="{ 'bg-neon-blue/5': !notification.read }"
          >
            <div class="flex items-start gap-3">
              <!-- Notification Icon -->
              <div class="flex-shrink-0 w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-sm">
                {{ notificationsStore.getNotificationIcon(notification.type) }}
              </div>

              <!-- Notification Content -->
              <div class="flex-1 min-w-0">
                <p class="text-sm text-gray-300">
                  <span class="font-medium">{{ notification.fromUser?.username || 'System' }}</span>
                  {{ notification.message }}
                </p>
                <p class="text-xs text-gray-500 mt-1">
                  {{ notificationsStore.formatNotificationTime(notification.createdAt) }}
                </p>
              </div>

              <!-- Unread indicator -->
              <div v-if="!notification.read" class="flex-shrink-0 w-2 h-2 bg-neon-blue rounded-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="p-4 border-t border-gray-800">
        <button
          @click="showAllNotifications"
          class="w-full text-center text-sm text-neon-blue hover:text-neon-blue/80"
        >
          View all notifications
        </button>
      </div>
    </div>

    <!-- Full Notifications Page Modal -->
    <div
      v-if="showNotificationsPage"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      @click="showNotificationsPage = false"
    >
      <div
        class="bg-darker-bg rounded-xl border border-gray-800 w-full max-w-2xl max-h-[80vh] overflow-hidden"
        @click.stop
      >
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-800 flex justify-between items-center">
          <h2 class="text-xl font-semibold">All Notifications</h2>
          <button
            @click="showNotificationsPage = false"
            class="text-gray-400 hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>

        <!-- Modal Content -->
        <div class="overflow-y-auto max-h-[60vh]">
          <div v-if="notificationsStore.allNotifications.length === 0" class="p-8 text-center text-gray-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            <p>No notifications yet</p>
          </div>

          <div v-else>
            <div
              v-for="notification in notificationsStore.allNotifications"
              :key="notification._id"
              @click="handleNotificationClick(notification)"
              class="p-4 border-b border-gray-800 hover:bg-dark-bg cursor-pointer transition-colors"
              :class="{ 'bg-neon-blue/5': !notification.read }"
            >
              <div class="flex items-start gap-3">
                <!-- Notification Icon -->
                <div class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center">
                  {{ notificationsStore.getNotificationIcon(notification.type) }}
                </div>

                <!-- Notification Content -->
                <div class="flex-1 min-w-0">
                  <p class="text-gray-300">
                    <span class="font-medium">{{ notification.fromUser?.username || 'System' }}</span>
                    {{ notification.message }}
                  </p>
                  <p class="text-sm text-gray-500 mt-1">
                    {{ notificationsStore.formatNotificationTime(notification.createdAt) }}
                  </p>
                </div>

                <!-- Actions -->
                <div class="flex items-center gap-2">
                  <button
                    v-if="!notification.read"
                    @click.stop="notificationsStore.markAsRead(notification._id)"
                    class="text-xs text-neon-blue hover:text-neon-blue/80"
                  >
                    Mark read
                  </button>
                  <button
                    @click.stop="notificationsStore.deleteNotification(notification._id)"
                    class="text-xs text-red-400 hover:text-red-300"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="p-6 border-t border-gray-800 flex justify-between">
          <button
            v-if="hasUnread"
            @click="markAllAsRead"
            class="px-4 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80"
          >
            Mark all read
          </button>
          <button
            @click="notificationsStore.clearAllNotifications"
            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Clear all
          </button>
        </div>
      </div>
    </div>

    <!-- Click outside to close dropdown -->
    <div
      v-if="showDropdown"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>
