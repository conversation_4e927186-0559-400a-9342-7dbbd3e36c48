const { success, error, handleAsync } = require('./utils/response');
const { getAuthorizedB2 } = require('./utils/storage');

// Test B2 configuration and connection
exports.handler = handleAsync(async (event) => {
  console.log('Testing B2 configuration...');

  try {
    // Check environment variables
    const envVars = {
      B2_KEY_ID: !!process.env.B2_KEY_ID,
      B2_APP_KEY: !!process.env.B2_APP_KEY,
      B2_BUCKET_ID: !!process.env.B2_BUCKET_ID,
      B2_BUCKET_NAME: !!process.env.B2_BUCKET_NAME
    };

    console.log('Environment variables status:', envVars);

    // Check for missing environment variables
    const missingVars = Object.entries(envVars)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missingVars.length > 0) {
      return error(`Missing environment variables: ${missingVars.join(', ')}`, 500);
    }

    // Test B2 authorization
    console.log('Testing B2 authorization...');
    const b2 = await getAuthorizedB2();

    if (!b2) {
      return error('Failed to get B2 authorization', 500);
    }

    // Test getting upload URL
    console.log('Testing upload URL generation...');
    const uploadUrlResponse = await b2.getUploadUrl({
      bucketId: process.env.B2_BUCKET_ID
    });

    if (!uploadUrlResponse || !uploadUrlResponse.data) {
      return error('Failed to get upload URL', 500);
    }

    // Test listing bucket contents (optional)
    console.log('Testing bucket access...');
    try {
      const listResponse = await b2.listFileNames({
        bucketId: process.env.B2_BUCKET_ID,
        maxFileCount: 1
      });

      console.log('Bucket access test successful');
    } catch (listError) {
      console.warn('Bucket listing failed (this might be normal):', listError.message);
    }

    // Test download capability for private buckets
    console.log('Testing download capability for private bucket...');
    let downloadCapable = false;
    try {
      // Try to get download authorization (needed for private buckets)
      const downloadAuth = await b2.getDownloadAuthorization({
        bucketId: process.env.B2_BUCKET_ID,
        fileNamePrefix: '',
        validDurationInSeconds: 3600
      });

      if (downloadAuth && downloadAuth.data) {
        downloadCapable = true;
        console.log('Download authorization test successful');
      }
    } catch (downloadError) {
      console.warn('Download authorization failed:', downloadError.message);
    }

    return success({
      message: 'B2 configuration test successful',
      environmentVariables: envVars,
      bucketId: process.env.B2_BUCKET_ID,
      bucketName: process.env.B2_BUCKET_NAME,
      uploadUrlGenerated: true,
      downloadCapable: downloadCapable,
      privateBucketSupport: downloadCapable,
      mediaProxyUrl: `${process.env.URL || 'https://netuark.netlify.app'}/.netlify/functions/media-proxy`,
      timestamp: new Date().toISOString()
    });

  } catch (testError) {
    console.error('B2 configuration test failed:', {
      message: testError.message,
      stack: testError.stack
    });

    return error(`B2 configuration test failed: ${testError.message}`, 500);
  }
});
