// Standard response format for success
function success(data = {}, statusCode = 200) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*', // CORS header
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
    },
    body: JSON.stringify({
      success: true,
      data
    })
  };
}

// Standard response format for errors
function error(message, statusCode = 400) {
  return {
    statusCode,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*', // CORS header
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
    },
    body: JSON.stringify({
      success: false,
      error: message
    })
  };
}

// Handle CORS preflight requests
function handleOptions() {
  return {
    statusCode: 204, // No content
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Max-Age': '86400' // 24 hours
    }
  };
}

// Utility to handle async function errors
function handleAsync(fn) {
  return async (event, context) => {
    // Handle OPTIONS requests for CORS
    if (event.httpMethod === 'OPTIONS') {
      return handleOptions();
    }
    
    try {
      return await fn(event, context);
    } catch (err) {
      console.error('Function error:', err);
      return error(err.message || 'Internal server error', 500);
    }
  };
}

module.exports = {
  success,
  error,
  handleOptions,
  handleAsync
};
