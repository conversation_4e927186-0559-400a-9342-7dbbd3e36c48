import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { useSubscriptionStore } from './subscription';

export const useEmojiStore = defineStore('emoji', () => {
  // State
  const userEmojis = ref([]);
  const favoriteEmojis = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const emojiAnalytics = ref({});
  
  // Getters
  const allUserEmojis = computed(() => userEmojis.value);
  const allFavoriteEmojis = computed(() => favoriteEmojis.value);
  
  // Get emoji limits based on subscription status
  const emojiLimits = computed(() => {
    const subscriptionStore = useSubscriptionStore();
    return {
      creation: subscriptionStore.isPremium ? Infinity : 10,
      favorites: subscriptionStore.isPremium ? Infinity : 15
    };
  });
  
  // Check if user has reached emoji creation limit
  const hasReachedCreationLimit = computed(() => {
    return userEmojis.value.length >= emojiLimits.value.creation;
  });
  
  // Check if user has reached favorites limit
  const hasReachedFavoritesLimit = computed(() => {
    return favoriteEmojis.value.length >= emojiLimits.value.favorites;
  });
  
  // Actions
  
  // Fetch user's custom emojis
  async function fetchUserEmojis() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/user-emojis', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch user emojis');
      }
      
      userEmojis.value = data.data.emojis;
      return data.data.emojis;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch user's favorite emojis
  async function fetchFavoriteEmojis() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/favorites', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch favorite emojis');
      }
      
      favoriteEmojis.value = data.data.emojis;
      return data.data.emojis;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Create a new custom emoji
  async function createEmoji(name, imageData, isAnimated = false) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    // Check if user has reached limit
    if (hasReachedCreationLimit.value) {
      throw new Error('You have reached your emoji creation limit. Upgrade to NTA+ for unlimited emoji creation.');
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          name,
          imageData,
          isAnimated
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create emoji');
      }
      
      // Add new emoji to user emojis
      userEmojis.value.push(data.data.emoji);
      
      return data.data.emoji;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Add an emoji to favorites
  async function addToFavorites(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    // Check if user has reached favorites limit
    if (hasReachedFavoritesLimit.value) {
      throw new Error('You have reached your favorites limit. Upgrade to NTA+ for unlimited favorites.');
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/add-favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to add emoji to favorites');
      }
      
      // Add emoji to favorites
      favoriteEmojis.value.push(data.data.emoji);
      
      return data.data.emoji;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Remove an emoji from favorites
  async function removeFromFavorites(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/remove-favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to remove emoji from favorites');
      }
      
      // Remove emoji from favorites
      favoriteEmojis.value = favoriteEmojis.value.filter(emoji => emoji._id !== emojiId);
      
      return { success: true };
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Delete a custom emoji
  async function deleteEmoji(emojiId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ emojiId })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete emoji');
      }
      
      // Remove emoji from user emojis
      userEmojis.value = userEmojis.value.filter(emoji => emoji._id !== emojiId);
      
      // Also remove from favorites if it's there
      favoriteEmojis.value = favoriteEmojis.value.filter(emoji => emoji._id !== emojiId);
      
      return { success: true };
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Get emoji analytics
  async function fetchEmojiAnalytics() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/emoji/analytics', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch emoji analytics');
      }
      
      emojiAnalytics.value = data.data.analytics;
      return data.data.analytics;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  return {
    // State
    userEmojis,
    favoriteEmojis,
    loading,
    error,
    emojiAnalytics,
    
    // Getters
    allUserEmojis,
    allFavoriteEmojis,
    emojiLimits,
    hasReachedCreationLimit,
    hasReachedFavoritesLimit,
    
    // Actions
    fetchUserEmojis,
    fetchFavoriteEmojis,
    createEmoji,
    addToFavorites,
    removeFromFavorites,
    deleteEmoji,
    fetchEmojiAnalytics
  };
});
