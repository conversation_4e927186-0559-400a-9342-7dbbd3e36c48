/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'primary': '#00d4ff',
        'neon-blue': '#00f3ff',
        'neon-purple': '#9d00ff',
        'neon-pink': '#ff00f7',
        'dark-bg': '#0a0a0a',
        'darker-bg': '#050505',
      },
      animation: {
        'glow': 'glow 2s ease-in-out infinite alternate',
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        glow: {
          '0%': { textShadow: '0 0 5px #fff, 0 0 10px #fff, 0 0 15px #00f3ff, 0 0 20px #00f3ff, 0 0 25px #00f3ff, 0 0 30px #00f3ff, 0 0 35px #00f3ff' },
          '100%': { textShadow: '0 0 10px #fff, 0 0 20px #fff, 0 0 30px #9d00ff, 0 0 40px #9d00ff, 0 0 50px #9d00ff, 0 0 60px #9d00ff, 0 0 70px #9d00ff' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' }
        }
      },
      boxShadow: {
        'neon-blue': '0 0 5px #00f3ff, 0 0 10px #00f3ff, 0 0 15px #00f3ff',
        'neon-purple': '0 0 5px #9d00ff, 0 0 10px #9d00ff, 0 0 15px #9d00ff',
        'neon-pink': '0 0 5px #ff00f7, 0 0 10px #ff00f7, 0 0 15px #ff00f7',
      }
    },
  },
  plugins: [],
}
