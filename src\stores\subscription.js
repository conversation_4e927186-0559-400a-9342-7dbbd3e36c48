import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';

export const useSubscriptionStore = defineStore('subscription', () => {
  // State
  const subscriptionStatus = ref(null);
  const loading = ref(false);
  const error = ref(null);
  const premiumFeatures = ref([
    {
      id: 'ad-free',
      name: 'Ad-Free Experience',
      description: 'Enjoy NeTuArk without any advertisements.'
    },
    {
      id: 'extended-stories',
      name: 'Extended Story Duration',
      description: 'Your stories stay visible for 48 hours instead of 24 hours.'
    },
    {
      id: 'premium-avatars',
      name: 'Premium Avatars',
      description: 'Use animated GIFs and MP4s as your profile picture.'
    },
    {
      id: 'unlimited-emojis',
      name: 'Advanced Emoji Tools',
      description: 'Create unlimited custom emojis and save unlimited favorites.'
    },
    {
      id: 'unlimited-ai',
      name: 'Unlimited AI Requests',
      description: 'No hourly limits when using the Kraizer AI assistant.'
    },
    {
      id: 'search-priority',
      name: 'Priority in Search Results',
      description: 'Your profile appears higher in search results.'
    }
  ]);

  // Getters
  const isPremium = computed(() => subscriptionStatus.value?.isPremium || false);
  const expiryDate = computed(() => subscriptionStatus.value?.premiumExpiry || null);
  const isCancelled = computed(() => subscriptionStatus.value?.subscriptionCancelled || false);
  const allPremiumFeatures = computed(() => premiumFeatures.value);

  // Actions
  async function fetchSubscriptionStatus() {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/subscription/status', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch subscription status');
      }

      // Update subscription status
      subscriptionStatus.value = data.data;

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function subscribe(paymentDetails) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/subscription/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify(paymentDetails)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe');
      }

      // Update subscription status
      await fetchSubscriptionStatus();

      // Update user in auth store
      await authStore.fetchCurrentUser();

      return data.data.subscription;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function cancelSubscription() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/subscription/cancel', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to cancel subscription');
      }

      // Update subscription status
      await fetchSubscriptionStatus();

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function reactivateSubscription() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/subscription/reactivate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to reactivate subscription');
      }

      // Update subscription status
      await fetchSubscriptionStatus();

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  // Initialize by fetching subscription status if user is authenticated
  const authStore = useAuthStore();
  if (authStore.isAuthenticated) {
    fetchSubscriptionStatus();
  }

  // Get subscription price
  const subscriptionPrice = computed(() => 3.00); // $3 per month

  // Check if a feature is available
  function hasFeature(featureId) {
    return isPremium.value;
  }

  return {
    // State
    subscriptionStatus,
    loading,
    error,
    premiumFeatures,

    // Getters
    isPremium,
    expiryDate,
    isCancelled,
    allPremiumFeatures,
    subscriptionPrice,

    // Actions
    fetchSubscriptionStatus,
    subscribe,
    cancelSubscription,
    reactivateSubscription,
    hasFeature
  };
});
