const { ObjectId } = require('mongodb');
const { findOne, find, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

/**
 * Advanced Analytics System for NeTuArk
 * Comprehensive insights and metrics for users and content
 */

// Get user analytics dashboard
exports.getUserAnalytics = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const timeframe = event.queryStringParameters?.timeframe || '30d';
    
    const timeframeDays = parseInt(timeframe.replace('d', ''));
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Get user's posts analytics
    const postAnalytics = await aggregate('posts', [
      {
        $match: {
          author: new ObjectId(userId),
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: null,
          totalPosts: { $sum: 1 },
          totalViews: { $sum: '$viewCount' },
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } },
          totalShares: { $sum: '$shareCount' },
          avgViewsPerPost: { $avg: '$viewCount' },
          avgLikesPerPost: { $avg: { $size: '$likes' } },
          avgCommentsPerPost: { $avg: { $size: '$comments' } }
        }
      }
    ]);

    // Get engagement over time
    const engagementOverTime = await aggregate('posts', [
      {
        $match: {
          author: new ObjectId(userId),
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          posts: { $sum: 1 },
          views: { $sum: '$viewCount' },
          likes: { $sum: { $size: '$likes' } },
          comments: { $sum: { $size: '$comments' } },
          shares: { $sum: '$shareCount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Get top performing posts
    const topPosts = await aggregate('posts', [
      {
        $match: {
          author: new ObjectId(userId),
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true }
        }
      },
      {
        $addFields: {
          engagementScore: {
            $add: [
              { $size: '$likes' },
              { $multiply: [{ $size: '$comments' }, 2] },
              { $multiply: ['$shareCount', 3] },
              { $divide: ['$viewCount', 10] }
            ]
          }
        }
      },
      { $sort: { engagementScore: -1 } },
      { $limit: 10 },
      {
        $project: {
          _id: 1,
          content: { $substr: ['$content', 0, 100] },
          createdAt: 1,
          viewCount: 1,
          likes: { $size: '$likes' },
          comments: { $size: '$comments' },
          shareCount: 1,
          engagementScore: 1,
          aiTags: 1
        }
      }
    ]);

    // Get hashtag performance
    const hashtagPerformance = await aggregate('posts', [
      {
        $match: {
          author: new ObjectId(userId),
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true },
          aiTags: { $exists: true, $ne: [] }
        }
      },
      { $unwind: '$aiTags' },
      {
        $group: {
          _id: '$aiTags',
          posts: { $sum: 1 },
          totalViews: { $sum: '$viewCount' },
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } },
          avgEngagement: {
            $avg: {
              $add: [
                { $size: '$likes' },
                { $multiply: [{ $size: '$comments' }, 2] }
              ]
            }
          }
        }
      },
      { $sort: { avgEngagement: -1 } },
      { $limit: 20 }
    ]);

    // Get follower growth (if tracking data exists)
    const followerGrowth = await getFollowerGrowth(userId, startDate);

    const analytics = postAnalytics[0] || {
      totalPosts: 0,
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      avgViewsPerPost: 0,
      avgLikesPerPost: 0,
      avgCommentsPerPost: 0
    };

    return success({
      analytics,
      engagementOverTime,
      topPosts,
      hashtagPerformance,
      followerGrowth,
      timeframe,
      metadata: {
        generatedAt: new Date(),
        period: `${timeframeDays} days`
      }
    });

  } catch (err) {
    console.error('Error getting user analytics:', err);
    return error('Failed to get analytics', 500);
  }
}));

// Get content insights for a specific post
exports.getPostInsights = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const postId = event.queryStringParameters?.postId;

    if (!postId) {
      return error('Post ID is required', 400);
    }

    // Get post details
    const post = await findOne('posts', { 
      _id: new ObjectId(postId),
      author: new ObjectId(userId) // Ensure user owns the post
    });

    if (!post) {
      return error('Post not found or access denied', 404);
    }

    // Get interaction timeline
    const interactionTimeline = await aggregate('user_interactions', [
      { $match: { postId: new ObjectId(postId) } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
            hour: { $hour: '$createdAt' }
          },
          views: { $sum: { $cond: [{ $eq: ['$type', 'view'] }, 1, 0] } },
          likes: { $sum: { $cond: [{ $eq: ['$type', 'like'] }, 1, 0] } },
          comments: { $sum: { $cond: [{ $eq: ['$type', 'comment'] }, 1, 0] } },
          shares: { $sum: { $cond: [{ $eq: ['$type', 'share'] }, 1, 0] } }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.hour': 1 } }
    ]);

    // Get audience demographics
    const audienceDemographics = await aggregate('user_interactions', [
      { $match: { postId: new ObjectId(postId) } },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $group: {
          _id: null,
          uniqueUsers: { $addToSet: '$userId' },
          verifiedUsers: { $sum: { $cond: ['$user.isVerified', 1, 0] } },
          premiumUsers: { $sum: { $cond: ['$user.isPremium', 1, 0] } },
          avgFollowerCount: { $avg: '$user.followerCount' }
        }
      }
    ]);

    // Get engagement rate compared to user's average
    const userAvgEngagement = await aggregate('posts', [
      {
        $match: {
          author: new ObjectId(userId),
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: null,
          avgLikes: { $avg: { $size: '$likes' } },
          avgComments: { $avg: { $size: '$comments' } },
          avgViews: { $avg: '$viewCount' }
        }
      }
    ]);

    const currentEngagement = {
      likes: post.likes?.length || 0,
      comments: post.comments?.length || 0,
      views: post.viewCount || 0,
      shares: post.shareCount || 0
    };

    const avgEngagement = userAvgEngagement[0] || {
      avgLikes: 0,
      avgComments: 0,
      avgViews: 0
    };

    const performanceComparison = {
      likesVsAvg: avgEngagement.avgLikes > 0 ? (currentEngagement.likes / avgEngagement.avgLikes) : 1,
      commentsVsAvg: avgEngagement.avgComments > 0 ? (currentEngagement.comments / avgEngagement.avgComments) : 1,
      viewsVsAvg: avgEngagement.avgViews > 0 ? (currentEngagement.views / avgEngagement.avgViews) : 1
    };

    return success({
      post: {
        _id: post._id,
        content: post.content,
        createdAt: post.createdAt,
        aiTags: post.aiTags,
        media: post.media
      },
      currentEngagement,
      performanceComparison,
      interactionTimeline,
      audienceDemographics: audienceDemographics[0] || {},
      insights: generatePostInsights(post, currentEngagement, performanceComparison)
    });

  } catch (err) {
    console.error('Error getting post insights:', err);
    return error('Failed to get post insights', 500);
  }
}));

// Get platform-wide trending analytics
exports.getTrendingAnalytics = handleAsync(async (event) => {
  try {
    const timeframe = event.queryStringParameters?.timeframe || '24h';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);

    const timeframeDays = timeframe === '24h' ? 1 : parseInt(timeframe.replace('d', ''));
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Get trending hashtags with engagement metrics
    const trendingHashtags = await aggregate('posts', [
      {
        $match: {
          createdAt: { $gte: startDate },
          aiTags: { $exists: true, $ne: [] },
          isDeleted: { $ne: true }
        }
      },
      { $unwind: '$aiTags' },
      {
        $group: {
          _id: '$aiTags',
          postCount: { $sum: 1 },
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } },
          totalViews: { $sum: '$viewCount' },
          uniqueAuthors: <AUTHORS>
        }
      },
      {
        $addFields: {
          authorCount: { $size: '$uniqueAuthors' },
          engagementRate: {
            $divide: [
              { $add: ['$totalLikes', '$totalComments'] },
              { $max: ['$totalViews', 1] }
            ]
          },
          trendingScore: {
            $multiply: [
              '$postCount',
              { $add: [1, '$engagementRate'] },
              { $add: [1, { $divide: ['$authorCount', 10] }] }
            ]
          }
        }
      },
      { $sort: { trendingScore: -1 } },
      { $limit: limit }
    ]);

    // Get top creators by engagement
    const topCreators = await aggregate('posts', [
      {
        $match: {
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: '$author',
          postCount: { $sum: 1 },
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } },
          totalViews: { $sum: '$viewCount' },
          avgEngagement: {
            $avg: {
              $add: [
                { $size: '$likes' },
                { $multiply: [{ $size: '$comments' }, 2] }
              ]
            }
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      { $sort: { avgEngagement: -1 } },
      { $limit: limit },
      {
        $project: {
          username: '$user.username',
          displayName: '$user.displayName',
          profilePicture: '$user.profilePicture',
          isVerified: '$user.isVerified',
          postCount: 1,
          totalLikes: 1,
          totalComments: 1,
          totalViews: 1,
          avgEngagement: 1
        }
      }
    ]);

    // Get platform engagement metrics
    const platformMetrics = await aggregate('posts', [
      {
        $match: {
          createdAt: { $gte: startDate },
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: null,
          totalPosts: { $sum: 1 },
          totalViews: { $sum: '$viewCount' },
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } },
          totalShares: { $sum: '$shareCount' },
          uniqueAuthors: <AUTHORS>
        }
      },
      {
        $addFields: {
          activeCreators: { $size: '$uniqueAuthors' },
          avgEngagementRate: {
            $divide: [
              { $add: ['$totalLikes', '$totalComments'] },
              { $max: ['$totalViews', 1] }
            ]
          }
        }
      }
    ]);

    return success({
      trendingHashtags,
      topCreators,
      platformMetrics: platformMetrics[0] || {},
      timeframe,
      metadata: {
        generatedAt: new Date(),
        period: `${timeframeDays} ${timeframeDays === 1 ? 'day' : 'days'}`
      }
    });

  } catch (err) {
    console.error('Error getting trending analytics:', err);
    return error('Failed to get trending analytics', 500);
  }
}));

// Helper functions
async function getFollowerGrowth(userId, startDate) {
  // This would require a follower_history collection to track changes over time
  // For now, return empty array - implement when follower tracking is added
  return [];
}

function generatePostInsights(post, engagement, comparison) {
  const insights = [];

  // Performance insights
  if (comparison.likesVsAvg > 1.5) {
    insights.push({
      type: 'positive',
      message: `This post received ${Math.round((comparison.likesVsAvg - 1) * 100)}% more likes than your average`
    });
  } else if (comparison.likesVsAvg < 0.5) {
    insights.push({
      type: 'suggestion',
      message: 'Consider using more engaging content or popular hashtags to increase likes'
    });
  }

  if (comparison.commentsVsAvg > 1.5) {
    insights.push({
      type: 'positive',
      message: 'This post generated great discussion with above-average comments'
    });
  }

  // Content insights
  if (post.aiTags && post.aiTags.length > 0) {
    insights.push({
      type: 'info',
      message: `Tagged with ${post.aiTags.length} hashtags: ${post.aiTags.slice(0, 3).join(', ')}`
    });
  }

  // Timing insights
  const postHour = new Date(post.createdAt).getHours();
  if (postHour >= 9 && postHour <= 11) {
    insights.push({
      type: 'info',
      message: 'Posted during peak morning engagement hours (9-11 AM)'
    });
  } else if (postHour >= 19 && postHour <= 21) {
    insights.push({
      type: 'info',
      message: 'Posted during peak evening engagement hours (7-9 PM)'
    });
  }

  return insights;
}

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/analytics', '');
  
  switch (true) {
    case path === '/user' && event.httpMethod === 'GET':
      return exports.getUserAnalytics(event, context);
    case path === '/post' && event.httpMethod === 'GET':
      return exports.getPostInsights(event, context);
    case path === '/trending' && event.httpMethod === 'GET':
      return exports.getTrendingAnalytics(event, context);
    default:
      return error('Not found', 404);
  }
};
