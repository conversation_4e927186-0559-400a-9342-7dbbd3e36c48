// Simple test script to verify database connection
const { MongoClient } = require('mongodb');

async function testDatabase() {
  console.log('Testing MongoDB Connection...\n');

  const uri = 'mongodb+srv://soham:<EMAIL>/?retryWrites=true&w=majority&appName=netuark';
  const dbName = 'netuark';

  try {
    console.log('Connecting to MongoDB...');
    const client = new MongoClient(uri, {
      serverSelectionTimeoutMS: 5000, // 5 second timeout
      connectTimeoutMS: 5000,
    });

    await client.connect();
    console.log('✅ Connected to MongoDB successfully!');

    const db = client.db(dbName);
    console.log(`✅ Connected to database: ${dbName}`);

    // Test a simple operation
    const collections = await db.listCollections().toArray();
    console.log('Available collections:', collections.map(c => c.name));

    // Test users collection
    const usersCollection = db.collection('users');
    const userCount = await usersCollection.countDocuments();
    console.log(`Users in database: ${userCount}`);

    await client.close();
    console.log('✅ Database connection test completed successfully!');

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Error details:', error);
  }
}

// Run the test
testDatabase();
