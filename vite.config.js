import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  optimizeDeps: {
    exclude: [
      'firebase-admin',
      'mongodb',
      'bcryptjs',
      'jsonwebtoken',
      'backblaze-b2',
      'node-fetch',
      'ml-matrix',
      'natural',
      'sentiment',
      'stopword',
      'lodash',
      'redis',
      'node-cron',
      'compromise'
    ]
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      external: [
        'firebase-admin',
        'mongodb',
        'bcryptjs',
        'jsonwebtoken',
        'backblaze-b2',
        'node-fetch',
        'ml-matrix',
        'natural',
        'sentiment',
        'stopword',
        'lodash',
        'redis',
        'node-cron',
        'compromise'
      ]
    }
  },
  define: {
    global: 'globalThis'
  }
})
