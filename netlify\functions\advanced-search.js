const { ObjectId } = require('mongodb');
const { findOne, find, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const natural = require('natural');

/**
 * Advanced Search System for NeTuArk
 * Multi-faceted search with intelligent ranking
 */

// Universal search endpoint
exports.universalSearch = handleAsync(async (event) => {
  try {
    const query = event.queryStringParameters?.q;
    const type = event.queryStringParameters?.type || 'all'; // all, posts, users, hashtags
    const sortBy = event.queryStringParameters?.sortBy || 'relevance';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);
    const page = Math.max(parseInt(event.queryStringParameters?.page) || 1, 1);
    const skip = (page - 1) * limit;

    if (!query || query.trim().length < 2) {
      return error('Search query must be at least 2 characters long', 400);
    }

    const searchQuery = query.trim();
    const results = {};

    // Search posts
    if (type === 'all' || type === 'posts') {
      results.posts = await searchPosts(searchQuery, sortBy, limit, skip);
    }

    // Search users
    if (type === 'all' || type === 'users') {
      results.users = await searchUsers(searchQuery, sortBy, limit, skip);
    }

    // Search hashtags
    if (type === 'all' || type === 'hashtags') {
      results.hashtags = await searchHashtags(searchQuery, limit);
    }

    // If searching all, limit results per category
    if (type === 'all') {
      results.posts = results.posts?.slice(0, 10) || [];
      results.users = results.users?.slice(0, 10) || [];
      results.hashtags = results.hashtags?.slice(0, 10) || [];
    }

    return success({
      query: searchQuery,
      results,
      metadata: {
        type,
        sortBy,
        page,
        limit
      }
    });

  } catch (err) {
    console.error('Error in universal search:', err);
    return error('Search failed', 500);
  }
});

// Search posts with advanced filtering
exports.searchPosts = handleAsync(async (event) => {
  try {
    const query = event.queryStringParameters?.q;
    const author = event.queryStringParameters?.author;
    const hashtag = event.queryStringParameters?.hashtag;
    const dateFrom = event.queryStringParameters?.dateFrom;
    const dateTo = event.queryStringParameters?.dateTo;
    const mediaType = event.queryStringParameters?.mediaType; // image, video, none
    const sortBy = event.queryStringParameters?.sortBy || 'relevance';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);
    const page = Math.max(parseInt(event.queryStringParameters?.page) || 1, 1);
    const skip = (page - 1) * limit;

    const posts = await searchPosts(query, sortBy, limit, skip, {
      author,
      hashtag,
      dateFrom,
      dateTo,
      mediaType
    });

    return success({
      posts,
      query,
      filters: { author, hashtag, dateFrom, dateTo, mediaType },
      metadata: { sortBy, page, limit, total: posts.length }
    });

  } catch (err) {
    console.error('Error searching posts:', err);
    return error('Post search failed', 500);
  }
});

// Search users with filters
exports.searchUsers = handleAsync(async (event) => {
  try {
    const query = event.queryStringParameters?.q;
    const verified = event.queryStringParameters?.verified === 'true';
    const premium = event.queryStringParameters?.premium === 'true';
    const location = event.queryStringParameters?.location;
    const sortBy = event.queryStringParameters?.sortBy || 'relevance';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);
    const page = Math.max(parseInt(event.queryStringParameters?.page) || 1, 1);
    const skip = (page - 1) * limit;

    const users = await searchUsers(query, sortBy, limit, skip, {
      verified,
      premium,
      location
    });

    return success({
      users,
      query,
      filters: { verified, premium, location },
      metadata: { sortBy, page, limit, total: users.length }
    });

  } catch (err) {
    console.error('Error searching users:', err);
    return error('User search failed', 500);
  }
});

// Get trending hashtags
exports.getTrendingHashtags = handleAsync(async (event) => {
  try {
    const timeframe = event.queryStringParameters?.timeframe || '24h';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);

    const timeframeDays = timeframe === '24h' ? 1 : parseInt(timeframe.replace('d', ''));
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    const trendingHashtags = await aggregate('posts', [
      {
        $match: {
          createdAt: { $gte: startDate },
          aiTags: { $exists: true, $ne: [] },
          isDeleted: { $ne: true }
        }
      },
      { $unwind: '$aiTags' },
      {
        $group: {
          _id: '$aiTags',
          count: { $sum: 1 },
          uniqueAuthors: <AUTHORS>
          totalLikes: { $sum: { $size: '$likes' } },
          totalComments: { $sum: { $size: '$comments' } }
        }
      },
      {
        $addFields: {
          uniqueAuthorCount: { $size: '$uniqueAuthors' },
          engagementScore: { $add: ['$totalLikes', { $multiply: ['$totalComments', 2] }] },
          trendingScore: {
            $multiply: [
              '$count',
              { $add: [1, { $divide: ['$engagementScore', 100] }] }
            ]
          }
        }
      },
      { $sort: { trendingScore: -1 } },
      { $limit: limit },
      {
        $project: {
          hashtag: '$_id',
          postCount: '$count',
          uniqueAuthors: <AUTHORS>
          engagementScore: 1,
          trendingScore: 1
        }
      }
    ]);

    return success({
      hashtags: trendingHashtags,
      timeframe,
      metadata: { limit, total: trendingHashtags.length }
    });

  } catch (err) {
    console.error('Error getting trending hashtags:', err);
    return error('Failed to get trending hashtags', 500);
  }
});

// Get search suggestions
exports.getSearchSuggestions = handleAsync(async (event) => {
  try {
    const query = event.queryStringParameters?.q;
    const type = event.queryStringParameters?.type || 'all';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 10, 20);

    if (!query || query.length < 2) {
      return success({ suggestions: [] });
    }

    const suggestions = [];

    // User suggestions
    if (type === 'all' || type === 'users') {
      const userSuggestions = await find('users', {
        $or: [
          { username: { $regex: query, $options: 'i' } },
          { displayName: { $regex: query, $options: 'i' } }
        ],
        isActive: { $ne: false }
      }, {
        limit: 5,
        projection: { username: 1, displayName: 1, profilePicture: 1, isVerified: 1 }
      });

      suggestions.push(...userSuggestions.map(user => ({
        type: 'user',
        text: user.username,
        displayText: user.displayName || user.username,
        avatar: user.profilePicture,
        verified: user.isVerified
      })));
    }

    // Hashtag suggestions
    if (type === 'all' || type === 'hashtags') {
      const hashtagSuggestions = await aggregate('posts', [
        { $unwind: '$aiTags' },
        {
          $match: {
            aiTags: { $regex: query, $options: 'i' },
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days
          }
        },
        {
          $group: {
            _id: '$aiTags',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]);

      suggestions.push(...hashtagSuggestions.map(tag => ({
        type: 'hashtag',
        text: tag._id,
        displayText: `#${tag._id}`,
        count: tag.count
      })));
    }

    return success({
      suggestions: suggestions.slice(0, limit),
      query
    });

  } catch (err) {
    console.error('Error getting search suggestions:', err);
    return error('Failed to get suggestions', 500);
  }
});

// Helper functions
async function searchPosts(query, sortBy, limit, skip, filters = {}) {
  const matchCriteria = {
    isDeleted: { $ne: true },
    visibility: 'public'
  };

  // Text search
  if (query) {
    matchCriteria.$or = [
      { content: { $regex: query, $options: 'i' } },
      { aiTags: { $in: [new RegExp(query, 'i')] } }
    ];
  }

  // Apply filters
  if (filters.author) {
    const author = await findOne('users', { username: filters.author });
    if (author) {
      matchCriteria.author = author._id;
    }
  }

  if (filters.hashtag) {
    matchCriteria.aiTags = filters.hashtag;
  }

  if (filters.dateFrom || filters.dateTo) {
    matchCriteria.createdAt = {};
    if (filters.dateFrom) matchCriteria.createdAt.$gte = new Date(filters.dateFrom);
    if (filters.dateTo) matchCriteria.createdAt.$lte = new Date(filters.dateTo);
  }

  if (filters.mediaType) {
    if (filters.mediaType === 'none') {
      matchCriteria.$or = [
        { media: { $exists: false } },
        { media: { $size: 0 } }
      ];
    } else {
      matchCriteria['media.type'] = { $regex: filters.mediaType, $options: 'i' };
    }
  }

  // Sort criteria
  let sortCriteria = {};
  switch (sortBy) {
    case 'recent':
      sortCriteria = { createdAt: -1 };
      break;
    case 'popular':
      sortCriteria = { viewCount: -1, createdAt: -1 };
      break;
    case 'engagement':
      sortCriteria = { 'engagementScore': -1, createdAt: -1 };
      break;
    case 'relevance':
    default:
      sortCriteria = { score: { $meta: 'textScore' }, createdAt: -1 };
  }

  const pipeline = [
    { $match: matchCriteria },
    {
      $addFields: {
        engagementScore: {
          $add: [
            { $size: '$likes' },
            { $multiply: [{ $size: '$comments' }, 2] },
            { $multiply: ['$shareCount', 3] }
          ]
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'authorDetails'
      }
    },
    { $unwind: '$authorDetails' },
    {
      $project: {
        _id: 1,
        content: 1,
        media: 1,
        aiTags: 1,
        createdAt: 1,
        viewCount: 1,
        likes: { $size: '$likes' },
        comments: { $size: '$comments' },
        shareCount: 1,
        engagementScore: 1,
        author: {
          _id: '$authorDetails._id',
          username: '$authorDetails.username',
          displayName: '$authorDetails.displayName',
          profilePicture: '$authorDetails.profilePicture',
          isVerified: '$authorDetails.isVerified'
        }
      }
    },
    { $sort: sortCriteria },
    { $skip: skip },
    { $limit: limit }
  ];

  return await aggregate('posts', pipeline);
}

async function searchUsers(query, sortBy, limit, skip, filters = {}) {
  const matchCriteria = {
    isActive: { $ne: false }
  };

  // Text search
  if (query) {
    matchCriteria.$or = [
      { username: { $regex: query, $options: 'i' } },
      { displayName: { $regex: query, $options: 'i' } },
      { bio: { $regex: query, $options: 'i' } }
    ];
  }

  // Apply filters
  if (filters.verified) {
    matchCriteria.isVerified = true;
  }

  if (filters.premium) {
    matchCriteria.isPremium = true;
  }

  if (filters.location) {
    matchCriteria.location = { $regex: filters.location, $options: 'i' };
  }

  // Sort criteria
  let sortCriteria = {};
  switch (sortBy) {
    case 'followers':
      sortCriteria = { followerCount: -1 };
      break;
    case 'recent':
      sortCriteria = { createdAt: -1 };
      break;
    case 'activity':
      sortCriteria = { lastActivityAt: -1 };
      break;
    case 'relevance':
    default:
      sortCriteria = { followerCount: -1, createdAt: -1 };
  }

  return await find('users', matchCriteria, {
    sort: sortCriteria,
    skip,
    limit,
    projection: {
      username: 1,
      displayName: 1,
      bio: 1,
      profilePicture: 1,
      isVerified: 1,
      isPremium: 1,
      followerCount: 1,
      location: 1,
      createdAt: 1
    }
  });
}

async function searchHashtags(query, limit) {
  return await aggregate('posts', [
    { $unwind: '$aiTags' },
    {
      $match: {
        aiTags: { $regex: query, $options: 'i' },
        createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
      }
    },
    {
      $group: {
        _id: '$aiTags',
        count: { $sum: 1 },
        recentPosts: { $sum: { $cond: [{ $gte: ['$createdAt', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] }, 1, 0] } }
      }
    },
    { $sort: { count: -1 } },
    { $limit: limit },
    {
      $project: {
        hashtag: '$_id',
        totalPosts: '$count',
        recentPosts: 1
      }
    }
  ]);
}

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/advanced-search', '');
  
  switch (true) {
    case path === '' && event.httpMethod === 'GET':
      return exports.universalSearch(event, context);
    case path === '/posts' && event.httpMethod === 'GET':
      return exports.searchPosts(event, context);
    case path === '/users' && event.httpMethod === 'GET':
      return exports.searchUsers(event, context);
    case path === '/hashtags/trending' && event.httpMethod === 'GET':
      return exports.getTrendingHashtags(event, context);
    case path === '/suggestions' && event.httpMethod === 'GET':
      return exports.getSearchSuggestions(event, context);
    default:
      return error('Not found', 404);
  }
};
