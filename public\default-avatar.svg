<svg width="150" height="150" viewBox="0 0 150 150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for 3D effect -->
    <radialGradient id="backgroundGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" style="stop-color:#4F46E5;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#3730A3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E1B4B;stop-opacity:1" />
    </radialGradient>
    
    <linearGradient id="faceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F3F4F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#E5E7EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D1D5DB;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="bodyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6B7280;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4B5563;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filters -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
    
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur stdDeviation="2" result="offset-blur"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
  </defs>
  
  <!-- Background circle with 3D effect -->
  <circle cx="75" cy="75" r="75" fill="url(#backgroundGradient)" filter="url(#shadow)"/>
  
  <!-- Inner circle for depth -->
  <circle cx="75" cy="75" r="70" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
  
  <!-- Person silhouette with 3D styling -->
  <g transform="translate(75, 75)">
    <!-- Head -->
    <circle cx="0" cy="-15" r="22" fill="url(#faceGradient)" filter="url(#innerShadow)"/>
    
    <!-- Head highlight -->
    <ellipse cx="-6" cy="-20" rx="8" ry="6" fill="rgba(255,255,255,0.3)" opacity="0.7"/>
    
    <!-- Body/Shoulders -->
    <path d="M -35 25 Q -35 5 -25 0 Q -15 -5 0 -5 Q 15 -5 25 0 Q 35 5 35 25 L 35 45 L -35 45 Z" 
          fill="url(#bodyGradient)" filter="url(#innerShadow)"/>
    
    <!-- Body highlight -->
    <path d="M -30 20 Q -30 8 -22 5 Q -12 0 0 0 Q 8 0 15 3 Q 25 8 25 15 L 25 25 L -30 25 Z" 
          fill="rgba(255,255,255,0.2)" opacity="0.6"/>
    
    <!-- Subtle facial features -->
    <g opacity="0.4">
      <!-- Eyes -->
      <circle cx="-7" cy="-18" r="1.5" fill="#374151"/>
      <circle cx="7" cy="-18" r="1.5" fill="#374151"/>
      
      <!-- Nose -->
      <ellipse cx="0" cy="-12" rx="1" ry="2" fill="rgba(55,65,81,0.3)"/>
      
      <!-- Mouth -->
      <path d="M -3 -8 Q 0 -6 3 -8" stroke="#374151" stroke-width="1" fill="none" stroke-linecap="round"/>
    </g>
  </g>
  
  <!-- Outer ring for extra depth -->
  <circle cx="75" cy="75" r="73" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="2"/>
  
  <!-- Glossy overlay effect -->
  <path d="M 20 30 Q 75 10 130 30 Q 120 20 75 15 Q 30 20 20 30 Z" 
        fill="url(#backgroundGradient)" opacity="0.3"/>
</svg>
