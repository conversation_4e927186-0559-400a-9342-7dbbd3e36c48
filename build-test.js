#!/usr/bin/env node

// Simple build test script to verify the build process
console.log('🔧 Testing NeTuArk build process...');

const fs = require('fs');
const path = require('path');

// Check if required files exist
const requiredFiles = [
  'package.json',
  'index.html',
  'src/main.js',
  'src/App.vue',
  'vite.config.js',
  'netlify.toml'
];

console.log('📁 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
    allFilesExist = false;
  }
});

// Check package.json structure
console.log('\n📦 Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (packageJson.scripts && packageJson.scripts.build) {
    console.log(`✅ Build script: ${packageJson.scripts.build}`);
  } else {
    console.log('❌ No build script found');
    allFilesExist = false;
  }
  
  if (packageJson.dependencies && packageJson.dependencies.vue) {
    console.log(`✅ Vue.js: ${packageJson.dependencies.vue}`);
  } else {
    console.log('❌ Vue.js dependency missing');
    allFilesExist = false;
  }
  
  if (packageJson.dependencies && packageJson.dependencies.vite) {
    console.log(`✅ Vite: ${packageJson.dependencies.vite}`);
  } else {
    console.log('❌ Vite dependency missing');
    allFilesExist = false;
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  allFilesExist = false;
}

// Check netlify.toml
console.log('\n🌐 Checking netlify.toml...');
try {
  const netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
  
  if (netlifyConfig.includes('publish = "dist"')) {
    console.log('✅ Publish directory set to "dist"');
  } else {
    console.log('❌ Publish directory not set correctly');
  }
  
  if (netlifyConfig.includes('functions = "netlify/functions"')) {
    console.log('✅ Functions directory configured');
  } else {
    console.log('❌ Functions directory not configured');
  }
} catch (error) {
  console.log('❌ Error reading netlify.toml:', error.message);
  allFilesExist = false;
}

// Final result
console.log('\n🎯 Build Test Results:');
if (allFilesExist) {
  console.log('✅ All checks passed! Build should work.');
  process.exit(0);
} else {
  console.log('❌ Some checks failed. Please fix the issues above.');
  process.exit(1);
}
