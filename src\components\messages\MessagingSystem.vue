<template>
  <div class="messaging-system">
    <div class="messaging-container">
      <!-- Conversations List -->
      <div class="conversations-panel">
        <div class="panel-header">
          <h2>Messages</h2>
          <button @click="showNewMessageModal = true" class="new-message-btn">
            <i class="fas fa-edit"></i>
          </button>
        </div>
        
        <div class="search-container">
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder="Search conversations..." 
            class="search-input"
          />
        </div>
        
        <div v-if="loadingConversations" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Loading conversations...</p>
        </div>
        
        <div v-else-if="conversationsError" class="error-container">
          <p>{{ conversationsError }}</p>
          <button @click="fetchConversations" class="retry-button">Retry</button>
        </div>
        
        <div v-else-if="filteredConversations.length === 0" class="empty-conversations">
          <p>No conversations found</p>
          <button @click="showNewMessageModal = true" class="new-message-btn">Start a Conversation</button>
        </div>
        
        <div v-else class="conversations-list">
          <div 
            v-for="conversation in filteredConversations" 
            :key="conversation._id" 
            class="conversation-item"
            :class="{ 'active': currentConversation && currentConversation._id === conversation._id }"
            @click="selectConversation(conversation)"
          >
            <div class="conversation-avatar">
              <img :src="conversation.participant.profilePicture" :alt="conversation.participant.username" class="avatar-image" />
              <div 
                class="status-indicator" 
                :class="{ 'online': isUserOnline(conversation.participant._id) }"
              ></div>
            </div>
            
            <div class="conversation-info">
              <div class="conversation-header">
                <div class="conversation-name">{{ conversation.participant.username }}</div>
                <div class="conversation-time">{{ formatTime(conversation.lastMessage?.createdAt) }}</div>
              </div>
              
              <div class="conversation-preview">
                <div class="message-preview">
                  {{ getMessagePreview(conversation) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Message Thread -->
      <div class="message-panel">
        <div v-if="!currentConversation" class="no-conversation">
          <div class="no-conversation-content">
            <i class="fas fa-comments no-conversation-icon"></i>
            <h3>Select a conversation</h3>
            <p>Choose a conversation from the list or start a new one.</p>
            <button @click="showNewMessageModal = true" class="new-message-btn">New Message</button>
          </div>
        </div>
        
        <template v-else>
          <div class="panel-header">
            <div class="conversation-info">
              <img :src="currentConversation.participant.profilePicture" :alt="currentConversation.participant.username" class="avatar-image small" />
              <div class="conversation-details">
                <div class="conversation-name">{{ currentConversation.participant.username }}</div>
                <div class="conversation-status">
                  {{ isUserOnline(currentConversation.participant._id) ? 'Online' : 'Offline' }}
                </div>
              </div>
            </div>
          </div>
          
          <div class="messages-container" ref="messagesContainer">
            <div v-if="loadingMessages" class="loading-container">
              <div class="loading-spinner"></div>
              <p>Loading messages...</p>
            </div>
            
            <div v-else-if="messagesError" class="error-container">
              <p>{{ messagesError }}</p>
              <button @click="loadMessages" class="retry-button">Retry</button>
            </div>
            
            <div v-else-if="messages.length === 0" class="empty-messages">
              <p>No messages yet</p>
              <p>Send a message to start the conversation.</p>
            </div>
            
            <div v-else class="messages-list">
              <div v-if="hasMoreMessages" class="load-more-messages">
                <button 
                  @click="loadMoreMessages" 
                  class="load-more-btn"
                  :disabled="loadingMoreMessages"
                >
                  <span v-if="loadingMoreMessages">
                    <i class="fas fa-spinner fa-spin"></i> Loading...
                  </span>
                  <span v-else>Load earlier messages</span>
                </button>
              </div>
              
              <div 
                v-for="(message, index) in messages" 
                :key="message._id" 
                class="message-item"
                :class="{ 
                  'outgoing': isCurrentUser(message.sender._id),
                  'incoming': !isCurrentUser(message.sender._id),
                  'pending': message.isPending
                }"
              >
                <div class="message-content">
                  <div class="message-text">
                    {{ message.content }}
                    <span v-if="message.isDeleted" class="deleted-message">(deleted)</span>
                    <span v-if="message.isPending" class="pending-indicator">
                      <i class="fas fa-clock"></i>
                    </span>
                  </div>
                  <div class="message-time">
                    {{ formatMessageTime(message.createdAt) }}
                  </div>
                </div>
                
                <div class="message-actions" v-if="isCurrentUser(message.sender._id) && !message.isDeleted">
                  <button @click="editMessage(message)" class="action-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="deleteMessage(message)" class="action-btn">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="message-input-container">
            <div v-if="editingMessage" class="editing-indicator">
              <span>Editing message</span>
              <button @click="cancelEdit" class="cancel-edit-btn">
                <i class="fas fa-times"></i>
              </button>
            </div>
            
            <div class="message-input">
              <textarea 
                v-model="messageText" 
                placeholder="Type a message..." 
                @keydown.enter.prevent="sendMessage"
                ref="messageInput"
                rows="1"
              ></textarea>
              <button 
                @click="sendMessage" 
                class="send-btn"
                :disabled="!messageText.trim()"
              >
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </template>
      </div>
    </div>
    
    <!-- New Message Modal -->
    <div v-if="showNewMessageModal" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>New Message</h3>
          <button @click="closeNewMessageModal" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="user-search">
            <input 
              type="text" 
              v-model="userSearchQuery" 
              placeholder="Search for a user..." 
              class="search-input"
              @input="searchUsers"
            />
            
            <div v-if="searchingUsers" class="loading-container small">
              <div class="loading-spinner small"></div>
              <p>Searching...</p>
            </div>
            
            <div v-else-if="userSearchError" class="error-container small">
              <p>{{ userSearchError }}</p>
            </div>
            
            <div v-else-if="userSearchResults.length === 0 && userSearchQuery.trim().length > 0" class="empty-results">
              <p>No users found</p>
            </div>
            
            <div v-else-if="userSearchResults.length > 0" class="user-results">
              <div 
                v-for="user in userSearchResults" 
                :key="user._id" 
                class="user-result-item"
                @click="startConversation(user)"
              >
                <img :src="user.profilePicture" :alt="user.username" class="avatar-image small" />
                <div class="user-info">
                  <div class="user-name">{{ user.username }}</div>
                  <div v-if="user.isPremium" class="user-premium">Premium</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useMessagesStore } from '@/stores/messages';
import { useAuthStore } from '@/stores/auth';
import { useDiscoveryStore } from '@/stores/discovery';

// Stores
const messagesStore = useMessagesStore();
const authStore = useAuthStore();
const discoveryStore = useDiscoveryStore();

// State
const searchQuery = ref('');
const currentConversation = ref(null);
const messages = ref([]);
const messageText = ref('');
const editingMessage = ref(null);
const loadingConversations = ref(true);
const loadingMessages = ref(false);
const loadingMoreMessages = ref(false);
const conversationsError = ref(null);
const messagesError = ref(null);
const hasMoreMessages = ref(false);
const messagesContainer = ref(null);

// New message modal
const showNewMessageModal = ref(false);
const userSearchQuery = ref('');
const userSearchResults = ref([]);
const searchingUsers = ref(false);
const userSearchError = ref(null);

// Computed
const filteredConversations = computed(() => {
  const query = searchQuery.value.toLowerCase().trim();
  
  if (!query) {
    return messagesStore.allConversations;
  }
  
  return messagesStore.allConversations.filter(conversation => {
    return conversation.participant.username.toLowerCase().includes(query);
  });
});

// Methods
async function fetchConversations() {
  loadingConversations.value = true;
  conversationsError.value = null;
  
  try {
    await messagesStore.fetchConversations();
  } catch (err) {
    conversationsError.value = err.message || 'Failed to load conversations';
  } finally {
    loadingConversations.value = false;
  }
}

function selectConversation(conversation) {
  currentConversation.value = conversation;
  messagesStore.setCurrentConversation(conversation);
  messages.value = messagesStore.currentMessages;
  hasMoreMessages.value = messagesStore.hasMore;
  
  // Clear editing state
  editingMessage.value = null;
  messageText.value = '';
  
  // Focus message input
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
    
    if (messageInput.value) {
      messageInput.value.focus();
    }
  });
}

async function loadMessages() {
  if (!currentConversation.value) return;
  
  loadingMessages.value = true;
  messagesError.value = null;
  
  try {
    await messagesStore.fetchMessages(currentConversation.value._id, true);
    messages.value = messagesStore.currentMessages;
    hasMoreMessages.value = messagesStore.hasMore;
    
    // Scroll to bottom
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
      }
    });
  } catch (err) {
    messagesError.value = err.message || 'Failed to load messages';
  } finally {
    loadingMessages.value = false;
  }
}

async function loadMoreMessages() {
  if (!currentConversation.value || !hasMoreMessages.value || loadingMoreMessages.value) return;
  
  loadingMoreMessages.value = true;
  
  try {
    const scrollHeight = messagesContainer.value.scrollHeight;
    
    await messagesStore.fetchMessages(currentConversation.value._id, false);
    messages.value = messagesStore.currentMessages;
    hasMoreMessages.value = messagesStore.hasMore;
    
    // Maintain scroll position
    nextTick(() => {
      if (messagesContainer.value) {
        const newScrollHeight = messagesContainer.value.scrollHeight;
        messagesContainer.value.scrollTop = newScrollHeight - scrollHeight;
      }
    });
  } catch (err) {
    console.error('Failed to load more messages:', err);
  } finally {
    loadingMoreMessages.value = false;
  }
}

async function sendMessage() {
  if (!messageText.value.trim()) return;
  
  try {
    if (editingMessage.value) {
      // Edit existing message
      await messagesStore.editMessage(editingMessage.value._id, messageText.value);
      editingMessage.value = null;
    } else {
      // Send new message
      await messagesStore.sendMessage(messageText.value);
    }
    
    // Clear input
    messageText.value = '';
    
    // Update messages
    messages.value = messagesStore.currentMessages;
    
    // Scroll to bottom
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
      }
    });
  } catch (err) {
    console.error('Failed to send message:', err);
  }
}

function editMessage(message) {
  editingMessage.value = message;
  messageText.value = message.content;
  
  // Focus input
  nextTick(() => {
    if (messageInput.value) {
      messageInput.value.focus();
    }
  });
}

function cancelEdit() {
  editingMessage.value = null;
  messageText.value = '';
}

async function deleteMessage(message) {
  if (!confirm('Are you sure you want to delete this message?')) return;
  
  try {
    await messagesStore.deleteMessage(message._id);
    messages.value = messagesStore.currentMessages;
  } catch (err) {
    console.error('Failed to delete message:', err);
  }
}

function isCurrentUser(userId) {
  return userId === authStore.user._id;
}

function isUserOnline(userId) {
  return messagesStore.isUserOnline(userId);
}

function getMessagePreview(conversation) {
  if (!conversation.lastMessage) return 'No messages yet';
  
  if (conversation.lastMessage.isDeleted) {
    return 'This message was deleted';
  }
  
  // Truncate message if too long
  const content = conversation.lastMessage.content;
  if (content.length > 30) {
    return content.substring(0, 30) + '...';
  }
  
  return content;
}

function formatTime(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const now = new Date();
  const diff = now - date;
  
  // Today
  if (diff < 86400000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }
  
  // Yesterday
  if (diff < 172800000 && date.getDate() === now.getDate() - 1) {
    return 'Yesterday';
  }
  
  // Within a week
  if (diff < 604800000) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    return days[date.getDay()];
  }
  
  // Older
  return date.toLocaleDateString();
}

function formatMessageTime(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

async function searchUsers() {
  if (userSearchQuery.value.trim().length < 2) {
    userSearchResults.value = [];
    return;
  }
  
  searchingUsers.value = true;
  userSearchError.value = null;
  
  try {
    const results = await discoveryStore.searchUsers(userSearchQuery.value);
    userSearchResults.value = results;
  } catch (err) {
    userSearchError.value = err.message || 'Failed to search users';
  } finally {
    searchingUsers.value = false;
  }
}

async function startConversation(user) {
  try {
    const conversation = await messagesStore.getOrCreateConversation(user._id);
    closeNewMessageModal();
    selectConversation(conversation);
  } catch (err) {
    console.error('Failed to start conversation:', err);
  }
}

function closeNewMessageModal() {
  showNewMessageModal.value = false;
  userSearchQuery.value = '';
  userSearchResults.value = [];
  userSearchError.value = null;
}

// Update online status
function updateOnlineStatus() {
  messagesStore.updateUserStatus('online');
}

// Lifecycle hooks
onMounted(() => {
  fetchConversations();
  updateOnlineStatus();
  
  // Set up online status update interval
  const statusInterval = setInterval(updateOnlineStatus, 60000); // Every minute
  
  // Clean up on unmount
  onUnmounted(() => {
    clearInterval(statusInterval);
  });
});

// Watch for changes in messages
watch(() => messagesStore.currentMessages, (newMessages) => {
  messages.value = newMessages;
  
  // Scroll to bottom if new message is added
  if (messages.value.length > 0 && messagesContainer.value) {
    nextTick(() => {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    });
  }
});
</script>

<style scoped>
.messaging-system {
  height: 100%;
  width: 100%;
}

.messaging-container {
  display: flex;
  height: 100%;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.conversations-panel {
  width: 300px;
  border-right: 1px solid #ddd;
  display: flex;
  flex-direction: column;
}

.message-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 1rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h2 {
  margin: 0;
  font-size: 1.2rem;
}

.new-message-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.new-message-btn:hover {
  background-color: #2980b9;
}

.search-container {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #ddd;
}

.search-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.loading-container, .error-container, .empty-conversations, .empty-messages, .no-conversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  height: 100%;
}

.loading-container.small, .error-container.small {
  padding: 1rem;
  height: auto;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

.loading-spinner.small {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  margin-top: 0.5rem;
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.conversation-item:hover {
  background-color: #f9f9f9;
}

.conversation-item.active {
  background-color: #e3f2fd;
}

.conversation-avatar {
  position: relative;
  margin-right: 0.75rem;
}

.avatar-image {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-image.small {
  width: 36px;
  height: 36px;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #95a5a6;
  border: 2px solid white;
}

.status-indicator.online {
  background-color: #2ecc71;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.conversation-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.8rem;
  color: #777;
  white-space: nowrap;
}

.message-preview {
  font-size: 0.9rem;
  color: #555;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-conversation-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 300px;
}

.no-conversation-icon {
  font-size: 3rem;
  color: #95a5a6;
  margin-bottom: 1rem;
}

.conversation-details {
  display: flex;
  flex-direction: column;
}

.conversation-status {
  font-size: 0.8rem;
  color: #777;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f9f9f9;
}

.messages-list {
  display: flex;
  flex-direction: column;
}

.load-more-messages {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.load-more-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.load-more-btn:hover {
  background-color: #e0e0e0;
}

.message-item {
  display: flex;
  margin-bottom: 0.5rem;
  max-width: 70%;
}

.message-item.outgoing {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-item.incoming {
  align-self: flex-start;
}

.message-content {
  background-color: white;
  border-radius: 12px;
  padding: 0.75rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
}

.message-item.outgoing .message-content {
  background-color: #e3f2fd;
  margin-left: 0.5rem;
}

.message-item.incoming .message-content {
  background-color: white;
  margin-right: 0.5rem;
}

.message-text {
  word-break: break-word;
}

.message-time {
  font-size: 0.7rem;
  color: #777;
  text-align: right;
  margin-top: 0.25rem;
}

.message-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.action-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0.25rem;
  font-size: 0.9rem;
}

.action-btn:hover {
  color: #333;
}

.deleted-message {
  font-style: italic;
  color: #777;
}

.pending-indicator {
  margin-left: 0.25rem;
  color: #777;
}

.message-input-container {
  padding: 1rem;
  border-top: 1px solid #ddd;
}

.editing-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #f0f0f0;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.cancel-edit-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
}

.message-input {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 24px;
  padding: 0.5rem 0.75rem;
}

.message-input textarea {
  flex: 1;
  border: none;
  background: none;
  resize: none;
  padding: 0.5rem;
  font-family: inherit;
  font-size: 1rem;
  outline: none;
}

.send-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.send-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.send-btn:not(:disabled):hover {
  background-color: #2980b9;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

.user-search {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.empty-results {
  text-align: center;
  padding: 1rem;
  color: #777;
}

.user-results {
  max-height: 300px;
  overflow-y: auto;
}

.user-result-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.user-result-item:hover {
  background-color: #f9f9f9;
}

.user-info {
  margin-left: 0.75rem;
}

.user-name {
  font-weight: bold;
}

.user-premium {
  font-size: 0.8rem;
  color: #f39c12;
}
</style>
