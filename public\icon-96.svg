<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient96" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow96" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="48" cy="48" r="44" fill="url(#bgGradient96)" filter="url(#shadow96)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(48, 48)">
    <!-- Central N -->
    <text x="0" y="9.600000000000001" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="38.400000000000006" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-19.200000000000003" y1="-9.600000000000001" x2="19.200000000000003" y2="9.600000000000001" 
          stroke="#0a0a0a" stroke-width="1.92" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-14.399999999999999" cy="14.399999999999999" r="1.92" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="14.399999999999999" cy="14.399999999999999" r="1.92" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="19.200000000000003" r="1.44" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>