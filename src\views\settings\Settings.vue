<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';

const router = useRouter();
const authStore = useAuthStore();

// Settings state
const settings = ref({
  notifications: {
    email: true,
    push: true,
    likes: true,
    comments: true,
    follows: true,
    messages: true,
    stories: true
  },
  appearance: {
    theme: 'dark',
    language: 'en',
    fontSize: 'medium'
  },
  privacy: {
    profileVisibility: 'public',
    showOnlineStatus: true,
    allowDirectMessages: true,
    showReadReceipts: true
  },
  content: {
    autoplayVideos: true,
    showSensitiveContent: false,
    dataUsage: 'standard'
  }
});

const saving = ref(false);
const error = ref(null);
const success = ref(false);

// Load user settings
const loadSettings = async () => {
  try {
    const response = await fetch('/.netlify/functions/user/settings', {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.data.settings) {
        settings.value = { ...settings.value, ...data.data.settings };
      }
    }
  } catch (err) {
    console.error('Error loading settings:', err);
  }
};

// Save settings
const saveSettings = async () => {
  try {
    saving.value = true;
    error.value = null;

    const response = await fetch('/.netlify/functions/user/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ settings: settings.value })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to save settings');
    }

    success.value = true;
    setTimeout(() => {
      success.value = false;
    }, 3000);

  } catch (err) {
    error.value = err.message;
  } finally {
    saving.value = false;
  }
};

// Handle logout
const handleLogout = () => {
  authStore.logout();
  router.push('/');
};

// Clear cache
const clearCache = () => {
  if ('caches' in window) {
    caches.keys().then(names => {
      names.forEach(name => {
        caches.delete(name);
      });
    });
  }
  localStorage.removeItem('posts_cache');
  localStorage.removeItem('stories_cache');
  alert('Cache cleared successfully!');
};

onMounted(() => {
  loadSettings();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Settings</h1>
        </div>

        <!-- Save Button -->
        <button
          @click="saveSettings"
          :disabled="saving"
          class="px-4 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80 transition-colors disabled:opacity-50 flex items-center gap-2"
        >
          <div v-if="saving" class="animate-spin rounded-full h-4 w-4 border-b-2 border-dark-bg"></div>
          {{ saving ? 'Saving...' : 'Save' }}
        </button>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16 space-y-6">
      <!-- Success Message -->
      <div v-if="success" class="bg-green-900/20 border border-green-500 rounded-xl p-4">
        <p class="text-green-400">Settings saved successfully!</p>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="bg-red-900/20 border border-red-500 rounded-xl p-4">
        <p class="text-red-400">{{ error }}</p>
      </div>

      <!-- Quick Actions -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <router-link to="/edit-profile" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 flex items-center gap-3 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <div>
              <div class="font-medium">Edit Profile</div>
              <div class="text-sm text-gray-400">Update your profile information</div>
            </div>
          </router-link>

          <router-link to="/privacy" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 flex items-center gap-3 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            <div>
              <div class="font-medium">Privacy Settings</div>
              <div class="text-sm text-gray-400">Control your privacy preferences</div>
            </div>
          </router-link>

          <router-link to="/subscription" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 flex items-center gap-3 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
            </svg>
            <div>
              <div class="font-medium">NTA+ Subscription</div>
              <div class="text-sm text-gray-400">Manage your premium subscription</div>
            </div>
          </router-link>

          <button @click="clearCache" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 flex items-center gap-3 transition-colors text-left">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <div>
              <div class="font-medium">Clear Cache</div>
              <div class="text-sm text-gray-400">Clear app cache and stored data</div>
            </div>
          </button>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Notification Preferences</h2>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Email Notifications</div>
              <div class="text-sm text-gray-400">Receive notifications via email</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.email" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Push Notifications</div>
              <div class="text-sm text-gray-400">Receive push notifications</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.push" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Likes</div>
              <div class="text-sm text-gray-400">When someone likes your posts</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.likes" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Comments</div>
              <div class="text-sm text-gray-400">When someone comments on your posts</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.comments" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">New Followers</div>
              <div class="text-sm text-gray-400">When someone follows you</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.follows" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Messages</div>
              <div class="text-sm text-gray-400">When you receive new messages</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.notifications.messages" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>
        </div>
      </div>

      <!-- Appearance Settings -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Appearance</h2>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Theme</label>
            <select v-model="settings.appearance.theme" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
              <option value="dark">Dark</option>
              <option value="light">Light</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Language</label>
            <select v-model="settings.appearance.language" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
              <option value="en">English</option>
              <option value="es">Español</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
              <option value="it">Italiano</option>
              <option value="pt">Português</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Font Size</label>
            <select v-model="settings.appearance.fontSize" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Content Settings -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Content Preferences</h2>

        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Autoplay Videos</div>
              <div class="text-sm text-gray-400">Automatically play videos in feed</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.content.autoplayVideos" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <div class="font-medium">Show Sensitive Content</div>
              <div class="text-sm text-gray-400">Display content marked as sensitive</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input v-model="settings.content.showSensitiveContent" type="checkbox" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-neon-blue"></div>
            </label>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Data Usage</label>
            <select v-model="settings.content.dataUsage" class="w-full bg-dark-bg border border-gray-700 rounded-lg px-4 py-3 text-white focus:border-neon-blue focus:outline-none">
              <option value="low">Low (Save data)</option>
              <option value="standard">Standard</option>
              <option value="high">High (Best quality)</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Account Actions -->
      <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
        <h2 class="text-xl font-semibold mb-4">Account</h2>

        <div class="space-y-4">
          <button @click="handleLogout" class="w-full p-4 border border-red-600 text-red-400 rounded-lg hover:bg-red-900/20 transition-colors flex items-center justify-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
            </svg>
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
