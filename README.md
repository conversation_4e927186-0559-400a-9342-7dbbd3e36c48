# NeTuArk - The World Is Opening

NeTuArk is a modern social platform built with Vue.js, Netlify Functions, MongoDB Atlas, and Backblaze B2 storage.

## Features

- User authentication and profile management
- Social feed with posts, likes, and comments
- Stories that expire after 24h (48h for premium users)
- Real-time messaging
- Custom emoji creation and management
- AI-driven content tagging and assistant
- Premium subscription (NTA+) with enhanced features
- Responsive design for all devices

## Tech Stack

- **Frontend**: Vue 3 + Composition API, Pinia for state management
- **Backend**: Netlify Functions (serverless)
- **Database**: MongoDB Atlas
- **Storage**: Backblaze B2
- **Styling**: Tailwind CSS
- **Authentication**: JWT-based auth

## Deployment

### Prerequisites

1. MongoDB Atlas account and cluster
2. Backblaze B2 account and bucket
3. Netlify account

### Environment Variables

Set the following environment variables in your Netlify site settings:

#### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `production` |
| `MONGODB_URI` | MongoDB connection string | `mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=netuark` |
| `DB_NAME` | MongoDB database name | `netuark` |
| `JWT_SECRET` | Secret key for JWT token generation | `your-strong-secret-key-for-production-use-only` |

#### Media Storage (Backblaze B2)

| Variable | Description | Example |
|----------|-------------|---------|
| `B2_BUCKET_NAME` | Backblaze B2 bucket name | `netuark-media` |
| `B2_ENDPOINT` | Backblaze B2 endpoint URL | `https://s3.us-west-004.backblazeb2.com` |
| `B2_KEY_ID` | Backblaze B2 application key ID | `001234567890abcdef0123456` |
| `B2_APP_KEY` | Backblaze B2 application key | `K001a2b3c4d5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0t1` |

#### Optional Configuration

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `RATE_LIMIT_MAX` | Maximum requests per window | `100` | `100` |
| `RATE_LIMIT_WINDOW_MS` | Rate limit window in milliseconds | `60000` | `60000` |
| `PREMIUM_PRICE_MONTHLY` | Monthly subscription price | `3` | `3` |
| `PREMIUM_PRICE_YEARLY` | Yearly subscription price | `30` | `30` |
| `LOG_LEVEL` | Logging level | `info` | `info`, `debug`, `error` |

### Deployment Steps

1. Fork this repository
2. Connect your fork to Netlify
3. Set the required environment variables
4. Deploy the site

## Local Development

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/netuark.git
   cd netuark
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   # Node environment
   NODE_ENV=production

   # MongoDB configuration
   MONGODB_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=netuark
   DB_NAME=netuark

   # JWT configuration
   JWT_SECRET=your-strong-secret-key-for-development-only

   # Backblaze B2 configuration
   B2_BUCKET_NAME=netuark-media
   B2_ENDPOINT=https://s3.us-west-004.backblazeb2.com
   B2_KEY_ID=your-backblaze-key-id
   B2_APP_KEY=your-backblaze-application-key

   # API rate limiting
   RATE_LIMIT_MAX=100
   RATE_LIMIT_WINDOW_MS=60000

   # Premium subscription settings
   PREMIUM_PRICE_MONTHLY=3
   PREMIUM_PRICE_YEARLY=30

   # Logging level
   LOG_LEVEL=debug
   ```

4. Start the development server:
   ```
   npm run dev
   ```

5. For local Netlify Functions development, install the Netlify CLI:
   ```
   npm install -g netlify-cli
   netlify dev
   ```

## MongoDB Setup

1. Create a MongoDB Atlas cluster
2. Create a database named `netuark`
3. Create the following collections:
   - `users`
   - `posts`
   - `comments`
   - `stories`
   - `conversations`
   - `messages`
   - `emojis`
   - `subscriptions`

## Backblaze B2 Setup

1. Create a Backblaze B2 bucket named `netuark-media`
2. Set the bucket to public
3. Create an application key with read and write access to the bucket
4. Note the key ID, application key, and bucket ID

## Project Structure

- `/src`: Vue.js frontend code
  - `/assets`: Static assets
  - `/components`: Vue components
  - `/stores`: Pinia stores
  - `/views`: Vue views/pages
  - `/router`: Vue Router configuration
- `/netlify/functions`: Serverless backend functions
  - `/utils`: Utility functions for the backend
  - Individual function files for different API endpoints

## License

MIT

## Motto

"The World Is Opening"
