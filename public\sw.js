// NeTuArk Service Worker
const CACHE_NAME = 'netuark-v1.0.0';
const STATIC_CACHE = 'netuark-static-v1.0.0';
const DYNAMIC_CACHE = 'netuark-dynamic-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/manifest.json',
  '/logo1.png',
  '/logo2.png',
  '/offline.html'
];

// API endpoints to cache
const API_CACHE_PATTERNS = [
  /\.netlify\/functions\/posts/,
  /\.netlify\/functions\/stories/,
  /\.netlify\/functions\/user/,
  /\.netlify\/functions\/emojis/
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');

  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');

  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip unsupported URL schemes
  if (!url.protocol.startsWith('http')) {
    return;
  }

  // Skip chrome-extension and other browser-specific schemes
  if (url.protocol === 'chrome-extension:' ||
      url.protocol === 'moz-extension:' ||
      url.protocol === 'safari-extension:' ||
      url.protocol === 'ms-browser-extension:') {
    return;
  }

  // Handle different types of requests
  if (request.url.includes('/.netlify/functions/')) {
    // API requests - cache with network first strategy
    event.respondWith(handleApiRequest(request));
  } else if (request.destination === 'image') {
    // Images - cache first strategy
    event.respondWith(handleImageRequest(request));
  } else if (request.mode === 'navigate') {
    // Navigation requests - network first with offline fallback
    event.respondWith(handleNavigationRequest(request));
  } else {
    // Other requests - cache first strategy
    event.respondWith(handleOtherRequests(request));
  }
});

// Handle API requests with network first strategy
async function handleApiRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.put(request, networkResponse.clone());
      } catch (cacheError) {
        console.warn('Failed to cache API response:', cacheError);
        // Continue without caching
      }
      return networkResponse;
    }

    throw new Error('Network response not ok');
  } catch (error) {
    // Fall back to cache
    try {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    } catch (cacheError) {
      console.warn('Failed to retrieve from cache:', cacheError);
    }

    // Return offline response for API calls
    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'You are currently offline. Please check your connection.'
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle image requests with cache first strategy
async function handleImageRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
  } catch (cacheError) {
    console.warn('Failed to check cache for image:', cacheError);
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.put(request, networkResponse.clone());
      } catch (cacheError) {
        console.warn('Failed to cache image:', cacheError);
        // Continue without caching
      }
      return networkResponse;
    }
    throw new Error('Network response not ok');
  } catch (error) {
    // Return placeholder image for failed image loads
    return new Response('', { status: 404 });
  }
}

// Handle navigation requests
async function handleNavigationRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      return networkResponse;
    }
    throw new Error('Network response not ok');
  } catch (error) {
    // Fall back to cached page or offline page
    try {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    } catch (cacheError) {
      console.warn('Failed to check cache for navigation:', cacheError);
    }

    // Return offline page
    try {
      return await caches.match('/offline.html');
    } catch (offlineError) {
      console.warn('Failed to load offline page:', offlineError);
      // Return basic offline response
      return new Response(
        '<!DOCTYPE html><html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
        { headers: { 'Content-Type': 'text/html' } }
      );
    }
  }
}

// Handle other requests with cache first strategy
async function handleOtherRequests(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
  } catch (cacheError) {
    console.warn('Failed to check cache for other request:', cacheError);
  }

  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      try {
        const cache = await caches.open(DYNAMIC_CACHE);
        await cache.put(request, networkResponse.clone());
      } catch (cacheError) {
        console.warn('Failed to cache other request:', cacheError);
        // Continue without caching
      }
      return networkResponse;
    }
    return networkResponse;
  } catch (error) {
    return new Response('Offline', { status: 503 });
  }
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);

  if (event.tag === 'background-sync-posts') {
    event.waitUntil(syncPosts());
  } else if (event.tag === 'background-sync-likes') {
    event.waitUntil(syncLikes());
  } else if (event.tag === 'background-sync-comments') {
    event.waitUntil(syncComments());
  }
});

// Sync offline posts
async function syncPosts() {
  try {
    const offlinePosts = await getOfflineData('offline-posts');

    for (const post of offlinePosts) {
      try {
        const response = await fetch('/.netlify/functions/posts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${post.token}`
          },
          body: JSON.stringify(post.data)
        });

        if (response.ok) {
          await removeOfflineData('offline-posts', post.id);
          console.log('Service Worker: Synced offline post', post.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync post', post.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing posts', error);
  }
}

// Sync offline likes
async function syncLikes() {
  try {
    const offlineLikes = await getOfflineData('offline-likes');

    for (const like of offlineLikes) {
      try {
        const response = await fetch(`/.netlify/functions/posts/${like.postId}/like`, {
          method: like.action === 'like' ? 'POST' : 'DELETE',
          headers: {
            'Authorization': `Bearer ${like.token}`
          }
        });

        if (response.ok) {
          await removeOfflineData('offline-likes', like.id);
          console.log('Service Worker: Synced offline like', like.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync like', like.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing likes', error);
  }
}

// Sync offline comments
async function syncComments() {
  try {
    const offlineComments = await getOfflineData('offline-comments');

    for (const comment of offlineComments) {
      try {
        const response = await fetch(`/.netlify/functions/posts/${comment.postId}/comments`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${comment.token}`
          },
          body: JSON.stringify({ content: comment.content })
        });

        if (response.ok) {
          await removeOfflineData('offline-comments', comment.id);
          console.log('Service Worker: Synced offline comment', comment.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync comment', comment.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing comments', error);
  }
}

// Helper functions for offline data management
async function getOfflineData(storeName) {
  // This would typically use IndexedDB, but for simplicity using a basic approach
  return [];
}

async function removeOfflineData(storeName, id) {
  // Remove synced data from offline storage
  console.log(`Removing ${id} from ${storeName}`);
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');

  const options = {
    body: 'You have new activity on NeTuArk!',
    icon: '/logo1.png',
    badge: '/logo1.png',
    vibrate: [200, 100, 200],
    data: {
      url: '/feed'
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/logo1.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ]
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data.url = data.url || options.data.url;
  }

  event.waitUntil(
    self.registration.showNotification('NeTuArk', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');

  event.notification.close();

  if (event.action === 'view') {
    event.waitUntil(
      clients.openWindow(event.notification.data.url || '/feed')
    );
  }
});

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('Service Worker: Notification closed');
});

console.log('Service Worker: Loaded');
