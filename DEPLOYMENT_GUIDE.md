# 🚀 NeTuArk Recommendation System Deployment Guide

## Prerequisites

### Required Dependencies
- Node.js 18+ 
- MongoDB Atlas account
- Netlify account
- Backblaze B2 account (optional for media storage)

### Environment Variables
Create a `.env` file with the following variables:

```env
# Database
MONGODB_URI=mongodb+srv://soham:<EMAIL>/?retryWrites=true&w=majority&appName=netuark
DB_NAME=netuark

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key-here

# Storage (Backblaze B2)
B2_KEY_ID=your-b2-key-id
B2_APP_KEY=your-b2-application-key
B2_BUCKET_ID=your-b2-bucket-id
B2_BUCKET_NAME=netuark-media

# Environment
NODE_ENV=production
URL=https://your-netlify-site.netlify.app
```

## 📦 Installation Steps

### 1. Install Dependencies
```bash
npm install
```

### 2. Database Setup
The recommendation system requires the following MongoDB collections:

#### Core Collections
- `users` - User profiles and preferences
- `posts` - Content with AI tags and engagement metrics
- `user_interactions` - Real-time interaction tracking
- `community_notes` - Fact-checking and verification notes
- `note_reports` - Community note reporting system

#### Analytics Collections
- `user_analytics` - Aggregated user behavior data
- `content_analytics` - Post performance metrics
- `trending_data` - Platform trending information

### 3. Database Indexes
Create the following indexes for optimal performance:

```javascript
// User interactions - critical for ML algorithms
db.user_interactions.createIndex({ userId: 1, createdAt: -1 });
db.user_interactions.createIndex({ postId: 1, type: 1 });
db.user_interactions.createIndex({ createdAt: -1 });

// Posts - for content discovery
db.posts.createIndex({ author: 1, createdAt: -1 });
db.posts.createIndex({ aiTags: 1, createdAt: -1 });
db.posts.createIndex({ createdAt: -1, visibility: 1 });
db.posts.createIndex({ "likes": 1 });

// Community notes
db.community_notes.createIndex({ postId: 1, status: 1 });
db.community_notes.createIndex({ author: 1, createdAt: -1 });
db.community_notes.createIndex({ "votes.score": -1 });

// Users - for search and recommendations
db.users.createIndex({ username: 1 });
db.users.createIndex({ following: 1 });
db.users.createIndex({ lastActivityAt: -1 });
```

## 🌐 Netlify Deployment

### 1. Build Configuration
Create `netlify.toml`:

```toml
[build]
  command = "npm run build"
  functions = "netlify/functions"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  node_bundler = "esbuild"
```

### 2. Environment Variables in Netlify
Set the following environment variables in Netlify dashboard:

1. Go to Site Settings → Environment Variables
2. Add all variables from your `.env` file
3. Ensure `NODE_ENV=production`

### 3. Function Dependencies
The recommendation system uses additional ML libraries. Ensure they're in your main `package.json`:

```json
{
  "dependencies": {
    "ml-matrix": "^6.10.0",
    "natural": "^6.12.0",
    "compromise": "^14.10.0",
    "sentiment": "^5.0.2",
    "stopword": "^2.0.8",
    "lodash": "^4.17.21",
    "redis": "^4.6.0",
    "node-cron": "^3.0.3"
  }
}
```

## 🔧 Configuration

### 1. Recommendation Engine Settings
Adjust algorithm weights in `netlify/functions/utils/recommendation-engine.js`:

```javascript
this.config = {
  minInteractions: 5,           // Minimum interactions for recommendations
  maxRecommendations: 50,       // Maximum posts per recommendation batch
  diversityThreshold: 0.3,      // Prevent echo chambers
  freshnessWeight: 0.2,         // Importance of recent content
  popularityWeight: 0.15,       // Trending content boost
  personalWeight: 0.65,         // User preference importance
  updateInterval: 300000        // Profile cache duration (5 minutes)
};
```

### 2. Interaction Tracking Settings
Configure batch processing in `netlify/functions/utils/interaction-tracker.js`:

```javascript
this.batchSize = 100;          // Interactions per batch
this.batchTimeout = 5000;      // Batch timeout (5 seconds)
```

### 3. Community Notes Configuration
Adjust credibility scoring in `netlify/functions/community-notes.js`:

```javascript
function calculateUserCredibility(user) {
  let score = 0.5; // Base score
  
  // Account age factor (max +0.2)
  const accountAge = (new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24);
  score += Math.min(accountAge / 365, 0.2);
  
  // Verification and premium bonuses
  if (user.isVerified) score += 0.1;
  if (user.isPremium) score += 0.05;
  
  return Math.min(score, 1.0);
}
```

## 📊 Monitoring & Analytics

### 1. Performance Monitoring
Monitor these key metrics:

- **Response Time**: Feed generation should be <100ms
- **Error Rate**: Keep below 1% for critical endpoints
- **Cache Hit Rate**: User profile cache should be >80%
- **Database Performance**: Query execution times

### 2. ML Algorithm Performance
Track recommendation quality:

- **Click-Through Rate (CTR)**: Target >5%
- **Dwell Time**: Average time spent on recommended content
- **Engagement Rate**: Likes, comments, shares on recommendations
- **Diversity Score**: Prevent filter bubbles

### 3. User Experience Metrics
Monitor user satisfaction:

- **Session Duration**: Time spent in app
- **Return Rate**: Daily/weekly active users
- **Feed Completion**: How far users scroll
- **Interaction Frequency**: User engagement patterns

## 🔒 Security & Privacy

### 1. Data Protection
- All user interactions are anonymized for ML training
- Personal data is encrypted at rest and in transit
- Regular data cleanup removes old interaction data
- Users can export or delete their data

### 2. Rate Limiting
Implement rate limiting for API endpoints:

```javascript
// Example rate limiting configuration
const rateLimits = {
  '/recommendation/feed': { requests: 60, window: '1m' },
  '/recommendation/track': { requests: 1000, window: '1m' },
  '/community-notes/create': { requests: 10, window: '1m' },
  '/advanced-search': { requests: 100, window: '1m' }
};
```

### 3. Input Validation
All user inputs are validated and sanitized:
- Content length limits
- URL validation for sources
- SQL injection prevention
- XSS protection

## 🧪 Testing

### 1. Unit Tests
Test individual algorithm components:

```bash
npm test
```

### 2. Integration Tests
Test API endpoints:

```bash
npm run test:integration
```

### 3. Performance Tests
Load test the recommendation system:

```bash
npm run test:performance
```

### 4. A/B Testing
The system includes built-in A/B testing for algorithm optimization:

```javascript
// Example A/B test configuration
const abTests = {
  'recommendation_algorithm': {
    variants: ['hybrid', 'collaborative', 'content_based'],
    traffic_split: [0.7, 0.15, 0.15]
  }
};
```

## 🚀 Go Live Checklist

### Pre-Launch
- [ ] All environment variables configured
- [ ] Database indexes created
- [ ] SSL certificate installed
- [ ] CDN configured for static assets
- [ ] Monitoring dashboards set up
- [ ] Backup strategy implemented

### Launch
- [ ] Deploy to production
- [ ] Verify all API endpoints
- [ ] Test recommendation generation
- [ ] Monitor error rates
- [ ] Check database performance
- [ ] Validate user interactions tracking

### Post-Launch
- [ ] Monitor user engagement metrics
- [ ] Track recommendation quality
- [ ] Analyze performance bottlenecks
- [ ] Gather user feedback
- [ ] Plan algorithm improvements

## 🔄 Maintenance

### Daily
- Monitor error rates and performance
- Check database health
- Review user feedback

### Weekly
- Analyze recommendation performance
- Update trending data
- Review community notes quality

### Monthly
- Retrain ML models with new data
- Optimize database queries
- Update algorithm weights based on performance
- Security audit and updates

## 📞 Support

For technical support or questions about the recommendation system:

1. Check the logs in Netlify Functions dashboard
2. Monitor MongoDB Atlas performance metrics
3. Review the recommendation system documentation
4. Check GitHub issues for known problems

---

**Ready to launch your intelligent social platform! 🚀**

*"The World Is Opening"* - NeTuArk
