<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import { useUserStore } from '../../stores/user';
import { usePostsStore } from '../../stores/posts';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const userStore = useUserStore();
const postsStore = usePostsStore();

const username = route.params.username;
const userProfile = ref(null);
const userPosts = ref([]);
const loading = ref(true);
const error = ref(null);
const activeTab = ref('posts');
const isFollowing = ref(false);
const followersCount = ref(0);
const followingCount = ref(0);
const postsCount = ref(0);

// Check if this is the current user's profile
const isOwnProfile = computed(() => {
  return authStore.user?.username === username;
});

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Fetch user profile data
const fetchUserProfile = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await fetch(`/.netlify/functions/user/profile/${username}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch user profile');
    }

    userProfile.value = data.data.user;
    followersCount.value = data.data.followersCount || 0;
    followingCount.value = data.data.followingCount || 0;
    postsCount.value = data.data.postsCount || 0;
    isFollowing.value = data.data.isFollowing || false;

    // Fetch user's posts
    await fetchUserPosts();
  } catch (err) {
    console.error('Error fetching user profile:', err);
    error.value = err.message || 'Failed to load user profile. Please try again.';

    // Reset values on error
    userProfile.value = null;
    followersCount.value = 0;
    followingCount.value = 0;
    postsCount.value = 0;
    isFollowing.value = false;
    userPosts.value = [];
  } finally {
    loading.value = false;
  }
};

// Fetch user's posts
const fetchUserPosts = async () => {
  try {
    const response = await fetch(`/.netlify/functions/user/posts/${username}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (response.ok) {
      userPosts.value = data.data.posts || [];
    } else {
      throw new Error('Failed to fetch posts');
    }
  } catch (err) {
    console.error('Error fetching user posts:', err);
    // Set empty array on error - no fallback data
    userPosts.value = [];
  }
};

// Follow/Unfollow user
const toggleFollow = async () => {
  if (!authStore.isAuthenticated || isOwnProfile.value) return;

  try {
    const endpoint = isFollowing.value ? 'unfollow' : 'follow';
    const response = await fetch(`/.netlify/functions/user/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ username })
    });

    const data = await response.json();

    if (response.ok) {
      isFollowing.value = !isFollowing.value;
      followersCount.value += isFollowing.value ? 1 : -1;
    } else {
      throw new Error(data.error || 'Failed to update follow status');
    }
  } catch (err) {
    console.error('Error toggling follow:', err);
  }
};

// Block/Unblock user
const toggleBlock = async () => {
  if (!authStore.isAuthenticated || isOwnProfile.value) return;

  try {
    const isBlocked = userStore.isUserBlacklisted(userProfile.value._id);
    const endpoint = isBlocked ? 'unblock' : 'block';

    const response = await fetch(`/.netlify/functions/user/${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authStore.token}`
      },
      body: JSON.stringify({ userId: userProfile.value._id })
    });

    const data = await response.json();

    if (response.ok) {
      if (isBlocked) {
        await userStore.unblacklistUser(userProfile.value._id);
      } else {
        await userStore.blacklistUser(userProfile.value._id);
      }
    } else {
      throw new Error(data.error || 'Failed to update block status');
    }
  } catch (err) {
    console.error('Error toggling block:', err);
  }
};

// Report user
const reportUser = () => {
  // TODO: Implement report modal
  console.log('Report user functionality to be implemented');
};

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  });
};

onMounted(() => {
  fetchUserProfile();
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">{{ username }}</h1>
        </div>

        <!-- Profile Actions -->
        <div v-if="!isOwnProfile && userProfile && authStore.isAuthenticated" class="flex items-center gap-2">
          <button
            @click="toggleFollow"
            :class="[
              'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
              isFollowing
                ? 'bg-gray-600 text-white hover:bg-gray-700'
                : 'bg-neon-blue text-dark-bg hover:bg-neon-blue/80'
            ]"
          >
            {{ isFollowing ? 'Following' : 'Follow' }}
          </button>

          <!-- More options menu -->
          <div class="relative">
            <button class="p-2 text-gray-400 hover:text-white">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-neon-blue"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500 rounded-xl p-6 text-center">
        <p class="text-red-400">{{ error }}</p>
        <button @click="fetchUserProfile" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
          Try Again
        </button>
      </div>

      <!-- Profile Content -->
      <div v-else-if="userProfile" class="space-y-6">
        <!-- Profile Header -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
            <!-- Profile Picture -->
            <div class="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden flex-shrink-0">
              <img
                :src="userProfile.profilePicture || getDefaultAvatar()"
                :alt="userProfile.username"
                class="w-full h-full object-cover"
              />
            </div>

            <!-- Profile Info -->
            <div class="flex-1 min-w-0">
              <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-bold flex items-center gap-2">
                    {{ userProfile.displayName || userProfile.username }}
                    <span v-if="userProfile.isPremium" class="text-sm text-neon-blue">NTA+</span>
                  </h1>
                  <p class="text-gray-400">@{{ userProfile.username }}</p>
                  <p v-if="userProfile.bio" class="mt-2 text-gray-300">{{ userProfile.bio }}</p>
                </div>
              </div>

              <!-- Stats -->
              <div class="flex gap-6 mt-4">
                <div class="text-center">
                  <div class="text-xl font-bold">{{ postsCount }}</div>
                  <div class="text-sm text-gray-400">Posts</div>
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold">{{ followersCount }}</div>
                  <div class="text-sm text-gray-400">Followers</div>
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold">{{ followingCount }}</div>
                  <div class="text-sm text-gray-400">Following</div>
                </div>
              </div>

              <!-- Join Date -->
              <div class="mt-4 text-sm text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Joined {{ formatDate(userProfile.createdAt) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Tabs -->
        <div class="bg-darker-bg rounded-xl border border-gray-800">
          <div class="border-b border-gray-800">
            <nav class="flex">
              <button
                @click="activeTab = 'posts'"
                :class="[
                  'px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                  activeTab === 'posts'
                    ? 'border-neon-blue text-neon-blue'
                    : 'border-transparent text-gray-400 hover:text-white'
                ]"
              >
                Posts
              </button>
              <button
                @click="activeTab = 'media'"
                :class="[
                  'px-6 py-4 text-sm font-medium border-b-2 transition-colors',
                  activeTab === 'media'
                    ? 'border-neon-blue text-neon-blue'
                    : 'border-transparent text-gray-400 hover:text-white'
                ]"
              >
                Media
              </button>

            </nav>
          </div>

          <!-- Tab Content -->
          <div class="p-6">
            <!-- Posts Tab -->
            <div v-if="activeTab === 'posts'">
              <div v-if="userPosts.length === 0" class="text-center py-12 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
                </svg>
                <p>No posts yet</p>
              </div>

              <div v-else class="space-y-4">
                <div
                  v-for="post in userPosts"
                  :key="post._id"
                  class="border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-colors cursor-pointer"
                  @click="router.push(`/post/${post._id}`)"
                >
                  <p class="text-gray-300 mb-2">{{ post.content }}</p>
                  <div class="flex items-center justify-between text-sm text-gray-400">
                    <span>{{ formatDate(post.createdAt) }}</span>
                    <div class="flex items-center gap-4">
                      <span>{{ post.likes?.length || 0 }} likes</span>
                      <span>{{ post.comments?.length || 0 }} comments</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Media Tab -->
            <div v-else-if="activeTab === 'media'" class="text-center py-12 text-gray-400">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-4 opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p>Media posts will be shown here</p>
            </div>


          </div>
        </div>
      </div>
    </div>
  </div>
</template>
