const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne } = require('./utils/db');
const { verifyToken } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const db = await connectToDatabase();
    const authHeader = event.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization token required' })
      };
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    if (!decoded) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token' })
      };
    }

    const userId = decoded.userId;
    const { httpMethod, path } = event;

    // GET /notifications - Fetch user notifications
    if (httpMethod === 'GET' && !path.includes('/')) {
      const { page = 1, limit = 20 } = event.queryStringParameters || {};
      const skip = (parseInt(page) - 1) * parseInt(limit);

      const notifications = await db.collection('notifications')
        .find({ userId: new ObjectId(userId) })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .populate('fromUser', 'username profilePicture')
        .toArray();

      const unreadCount = await db.collection('notifications')
        .countDocuments({
          userId: new ObjectId(userId),
          read: false
        });

      const totalCount = await db.collection('notifications')
        .countDocuments({ userId: new ObjectId(userId) });

      const hasMore = skip + notifications.length < totalCount;

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          data: {
            notifications,
            unreadCount,
            hasMore,
            page: parseInt(page),
            totalCount
          }
        })
      };
    }

    // PUT /notifications/:id/read - Mark notification as read
    if (httpMethod === 'PUT' && path.includes('/read')) {
      const notificationId = path.split('/')[1];

      const result = await db.collection('notifications').updateOne(
        {
          _id: new ObjectId(notificationId),
          userId: new ObjectId(userId)
        },
        {
          $set: {
            read: true,
            readAt: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'Notification not found' })
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: 'Notification marked as read'
        })
      };
    }

    // PUT /notifications/read-all - Mark all notifications as read
    if (httpMethod === 'PUT' && path.includes('/read-all')) {
      await db.collection('notifications').updateMany(
        { userId: new ObjectId(userId) },
        {
          $set: {
            read: true,
            readAt: new Date()
          }
        }
      );

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: 'All notifications marked as read'
        })
      };
    }

    // DELETE /notifications/:id - Delete notification
    if (httpMethod === 'DELETE' && path.includes('/')) {
      const notificationId = path.split('/')[1];

      const result = await db.collection('notifications').deleteOne({
        _id: new ObjectId(notificationId),
        userId: new ObjectId(userId)
      });

      if (result.deletedCount === 0) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'Notification not found' })
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: 'Notification deleted'
        })
      };
    }

    // DELETE /notifications/clear - Clear all notifications
    if (httpMethod === 'DELETE' && path.includes('/clear')) {
      await db.collection('notifications').deleteMany({
        userId: new ObjectId(userId)
      });

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: 'All notifications cleared'
        })
      };
    }

    // POST /notifications - Create notification (internal use)
    if (httpMethod === 'POST') {
      const {
        targetUserId,
        type,
        message,
        fromUserId,
        postId,
        commentId
      } = JSON.parse(event.body);

      const notification = {
        userId: new ObjectId(targetUserId),
        fromUserId: fromUserId ? new ObjectId(fromUserId) : null,
        type,
        message,
        postId: postId ? new ObjectId(postId) : null,
        commentId: commentId ? new ObjectId(commentId) : null,
        read: false,
        createdAt: new Date()
      };

      const result = await db.collection('notifications').insertOne(notification);

      return {
        statusCode: 201,
        headers,
        body: JSON.stringify({
          success: true,
          data: {
            notification: {
              ...notification,
              _id: result.insertedId
            }
          }
        })
      };
    }

    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };

  } catch (error) {
    console.error('Notifications function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        details: error.message
      })
    };
  }
};

// Helper function to create notifications
async function createNotification(db, { targetUserId, type, message, fromUserId, postId, commentId }) {
  try {
    const notification = {
      userId: new ObjectId(targetUserId),
      fromUserId: fromUserId ? new ObjectId(fromUserId) : null,
      type,
      message,
      postId: postId ? new ObjectId(postId) : null,
      commentId: commentId ? new ObjectId(commentId) : null,
      read: false,
      createdAt: new Date()
    };

    await db.collection('notifications').insertOne(notification);
    console.log('Notification created:', notification);
  } catch (error) {
    console.error('Error creating notification:', error);
  }
}

module.exports = { createNotification };
