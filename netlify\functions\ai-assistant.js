const { ObjectId } = require('mongodb');
const { findOne, updateOne, aggregate } = require('./utils/db');
const { requireAuth, requirePremium } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Dummy AI response function (placeholder for actual AI integration)
async function generateAIResponse(prompt, context) {
  // In a real implementation, this would call an AI API
  // For now, return a dummy response based on the prompt
  
  const responses = [
    "I'm <PERSON><PERSON><PERSON>, NeTuArk's AI assistant. How can I help you today?",
    "That's an interesting question! Let me think about that...",
    "Based on what I understand, I think the answer is...",
    "I'd recommend considering these options...",
    "Let me analyze that for you...",
    "I've looked at the context and here's what I think...",
    "That's a great point! Have you also considered...",
    "From my analysis, I can suggest the following...",
    "I'm still learning, but here's my best response...",
    "Thanks for asking! Here's what I know about that..."
  ];
  
  // Simple logic to make responses somewhat relevant to the prompt
  let response = "";
  
  if (prompt.toLowerCase().includes("hello") || prompt.toLowerCase().includes("hi")) {
    response = "Hello! I'm Kraizer, NeTuArk's AI assistant. How can I help you today?";
  } else if (prompt.toLowerCase().includes("help")) {
    response = "I'd be happy to help! What specifically do you need assistance with?";
  } else if (prompt.toLowerCase().includes("feature") || prompt.toLowerCase().includes("features")) {
    response = "NeTuArk offers many features including AI-driven feed, private messaging, emoji studio, and premium subscription options. Which one would you like to know more about?";
  } else if (prompt.toLowerCase().includes("premium") || prompt.toLowerCase().includes("nta+")) {
    response = "NTA+ subscription costs $3 per month and includes ad-free experience, 48h stories, unlimited custom emojis, GIF/MP4 profile pictures, unlimited AI assistant access, priority in search results, and exclusive seasonal content.";
  } else if (prompt.toLowerCase().includes("emoji")) {
    response = "The Emoji Studio lets you create custom emojis with layers, filters, and animations. Free users can create up to 10 emojis, while NTA+ subscribers have unlimited creation.";
  } else if (prompt.toLowerCase().includes("story") || prompt.toLowerCase().includes("stories")) {
    response = "Stories on NeTuArk last 24 hours for free users and 48 hours for NTA+ subscribers. They're a great way to share temporary moments with your followers.";
  } else {
    // Pick a random response for other queries
    const randomIndex = Math.floor(Math.random() * responses.length);
    response = responses[randomIndex];
  }
  
  // Add context awareness if context is provided
  if (context && context.length > 0) {
    response += " Based on the conversation context, I think this is relevant to what you're discussing.";
  }
  
  return response;
}

// Ask AI assistant
exports.askAI = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { prompt, contextPostId, contextCommentIds } = JSON.parse(event.body);
  
  // Validate input
  if (!prompt) {
    return error('Prompt is required');
  }
  
  // Get user to check premium status and AI request limits
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Check if user has reached the free tier limit
  if (!user.isPremium) {
    // Check if reset time has passed
    const now = new Date();
    if (now > new Date(user.aiRequestsResetTime)) {
      // Reset the counter and update reset time
      await updateOne(
        'users',
        { _id: new ObjectId(userId) },
        {
          $set: {
            aiRequestsRemaining: 3, // Reset to 3 requests
            aiRequestsResetTime: new Date(now.getTime() + 3600000) // 1 hour from now
          }
        }
      );
      
      // Update user object
      user.aiRequestsRemaining = 3;
    }
    
    // Check if user has any requests remaining
    if (user.aiRequestsRemaining <= 0) {
      return error('You have reached the limit of free AI requests. Please wait or upgrade to NTA+.', 403);
    }
    
    // Decrement the counter
    await updateOne(
      'users',
      { _id: new ObjectId(userId) },
      { $inc: { aiRequestsRemaining: -1 } }
    );
  }
  
  // Gather context if provided
  let context = [];
  
  if (contextPostId) {
    // Get post content
    const post = await findOne('posts', { _id: new ObjectId(contextPostId) });
    if (post) {
      context.push(post.content);
      
      // Get comments if specified
      if (contextCommentIds && contextCommentIds.length > 0) {
        const commentIds = contextCommentIds.map(id => new ObjectId(id));
        const comments = await aggregate('comments', [
          { $match: { _id: { $in: commentIds }, postId: new ObjectId(contextPostId) } },
          { $project: { content: 1 } }
        ]);
        
        context = [...context, ...comments.map(comment => comment.content)];
      }
    }
  }
  
  // Generate AI response
  const response = await generateAIResponse(prompt, context);
  
  // Return AI response
  return success({
    response,
    requestsRemaining: user.isPremium ? 'unlimited' : user.aiRequestsRemaining - 1,
    resetTime: user.isPremium ? null : user.aiRequestsResetTime
  });
}));

// Tag content with AI
exports.tagContent = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { postId } = JSON.parse(event.body);
  
  // Validate input
  if (!postId) {
    return error('Post ID is required');
  }
  
  // Check if post exists
  const post = await findOne('posts', { _id: new ObjectId(postId) });
  if (!post) {
    return error('Post not found', 404);
  }
  
  // Check if user is the author
  if (post.author.toString() !== userId) {
    return error('You can only tag your own posts', 403);
  }
  
  // Generate dummy AI tags based on post content
  const possibleTags = [
    'technology', 'art', 'music', 'food', 'travel', 'fashion', 'sports',
    'gaming', 'education', 'science', 'health', 'fitness', 'business',
    'entertainment', 'politics', 'nature', 'photography', 'design'
  ];
  
  // Select 3-5 random tags
  const numTags = Math.floor(Math.random() * 3) + 3; // 3 to 5 tags
  const tags = [];
  
  for (let i = 0; i < numTags; i++) {
    const randomIndex = Math.floor(Math.random() * possibleTags.length);
    const tag = possibleTags.splice(randomIndex, 1)[0];
    tags.push(tag);
  }
  
  // Update post with AI tags
  await updateOne(
    'posts',
    { _id: new ObjectId(postId) },
    { $set: { aiTags: tags } }
  );
  
  // Return tags
  return success({ tags });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/ai-assistant', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/ask' && event.httpMethod === 'POST':
      return exports.askAI(event, context);
    case path === '/tag' && event.httpMethod === 'POST':
      return exports.tagContent(event, context);
    default:
      return error('Not found', 404);
  }
};
