<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeTuArk PNG Icon Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
        }
        .container {
            background: #1a1a1a;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.1);
        }
        h1 {
            color: #00d4ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            background: #2a2a2a;
            border-radius: 8px;
            border: 2px solid transparent;
        }
        .icon-item.generated {
            border-color: #00d4ff;
        }
        .icon-preview {
            width: 64px;
            height: 64px;
            margin: 0 auto 10px;
            background: #00d4ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #0a0a0a;
        }
        .icon-size {
            font-size: 14px;
            color: #ccc;
            margin-bottom: 10px;
        }
        .download-btn {
            background: #00d4ff;
            color: #0a0a0a;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .download-btn:hover {
            background: #00b8e6;
            transform: translateY(-2px);
        }
        .download-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .generate-all {
            background: #00d4ff;
            color: #0a0a0a;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
            margin: 20px auto;
            display: block;
            transition: all 0.3s ease;
        }
        .generate-all:hover {
            background: #00b8e6;
            transform: translateY(-2px);
        }
        .status {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #2a2a2a;
        }
        .success {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid #00d4ff;
            color: #00d4ff;
        }
        .error {
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff0000;
            color: #ff6666;
        }
        canvas {
            display: none;
        }
        .instructions {
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #00d4ff;
        }
        .instructions h3 {
            color: #00d4ff;
            margin-top: 0;
        }
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 NeTuArk PNG Icon Generator</h1>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <ol>
                <li>Click "Generate All PNG Icons" to create all required icon sizes</li>
                <li>Download each generated icon using the download buttons</li>
                <li>Save all icons to your <code>public/</code> directory</li>
                <li>Deploy to Netlify to fix the PWA manifest icon errors</li>
            </ol>
        </div>

        <button class="generate-all" onclick="generateAllIcons()">
            🚀 Generate All PNG Icons
        </button>

        <div id="status" class="status" style="display: none;"></div>

        <div class="icon-grid" id="iconGrid">
            <!-- Icons will be generated here -->
        </div>

        <canvas id="canvas"></canvas>
    </div>

    <script>
        const iconSizes = [48, 72, 96, 144, 192, 512];
        const generatedIcons = new Map();

        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }

        function createIconSVG(size) {
            return `
                <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
                        </linearGradient>
                        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
                            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
                        </filter>
                    </defs>
                    
                    <!-- Background circle -->
                    <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 4}" fill="url(#bgGradient)" filter="url(#shadow)"/>
                    
                    <!-- NeTuArk logo elements -->
                    <g transform="translate(${size/2}, ${size/2})">
                        <!-- Central N -->
                        <text x="0" y="${size * 0.1}" text-anchor="middle" 
                              font-family="Arial, sans-serif" 
                              font-weight="bold" 
                              font-size="${size * 0.4}" 
                              fill="#0a0a0a">N</text>
                        
                        <!-- Connection lines -->
                        <line x1="${-size * 0.2}" y1="${-size * 0.1}" x2="${size * 0.2}" y2="${size * 0.1}" 
                              stroke="#0a0a0a" stroke-width="${size * 0.02}" opacity="0.7"/>
                        
                        <!-- Small dots -->
                        <circle cx="${-size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="#0a0a0a" opacity="0.8"/>
                        <circle cx="${size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="#0a0a0a" opacity="0.8"/>
                        <circle cx="0" cy="${size * 0.2}" r="${size * 0.015}" fill="#0a0a0a" opacity="0.6"/>
                    </g>
                </svg>
            `;
        }

        function generateIcon(size) {
            return new Promise((resolve, reject) => {
                try {
                    const canvas = document.getElementById('canvas');
                    const ctx = canvas.getContext('2d');
                    
                    canvas.width = size;
                    canvas.height = size;
                    
                    const svgString = createIconSVG(size);
                    const img = new Image();
                    
                    img.onload = function() {
                        ctx.clearRect(0, 0, size, size);
                        ctx.drawImage(img, 0, 0, size, size);
                        
                        canvas.toBlob(function(blob) {
                            if (blob) {
                                generatedIcons.set(size, blob);
                                resolve(blob);
                            } else {
                                reject(new Error('Failed to generate blob'));
                            }
                        }, 'image/png');
                    };
                    
                    img.onerror = function() {
                        reject(new Error('Failed to load SVG'));
                    };
                    
                    const svgBlob = new Blob([svgString], {type: 'image/svg+xml'});
                    const url = URL.createObjectURL(svgBlob);
                    img.src = url;
                    
                } catch (error) {
                    reject(error);
                }
            });
        }

        function downloadIcon(size) {
            const blob = generatedIcons.get(size);
            if (!blob) {
                showStatus(`Icon ${size}x${size} not generated yet`, 'error');
                return;
            }
            
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `icon-${size}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showStatus(`Downloaded icon-${size}.png`);
        }

        function createIconItem(size) {
            return `
                <div class="icon-item" id="icon-${size}">
                    <div class="icon-preview">${size}</div>
                    <div class="icon-size">${size}x${size}</div>
                    <button class="download-btn" onclick="downloadIcon(${size})" disabled>
                        Download
                    </button>
                </div>
            `;
        }

        async function generateAllIcons() {
            const grid = document.getElementById('iconGrid');
            grid.innerHTML = iconSizes.map(size => createIconItem(size)).join('');
            
            showStatus('Generating PNG icons...', 'success');
            
            try {
                for (const size of iconSizes) {
                    await generateIcon(size);
                    
                    // Update UI
                    const iconItem = document.getElementById(`icon-${size}`);
                    iconItem.classList.add('generated');
                    const btn = iconItem.querySelector('.download-btn');
                    btn.disabled = false;
                    btn.textContent = 'Download';
                }
                
                showStatus('✅ All PNG icons generated successfully! Click download buttons to save them.', 'success');
                
            } catch (error) {
                console.error('Error generating icons:', error);
                showStatus(`❌ Error generating icons: ${error.message}`, 'error');
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('Ready to generate PNG icons for NeTuArk PWA', 'success');
        });
    </script>
</body>
</html>
