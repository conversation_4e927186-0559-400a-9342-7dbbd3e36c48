<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import Breadcrumb from '../../components/ui/Breadcrumb.vue';

const router = useRouter();
const authStore = useAuthStore();

// Reactive data
const loading = ref(true);
const error = ref(null);
const userStats = ref({
  postsCount: 0,
  followersCount: 0,
  followingCount: 0
});

// Computed
const currentUser = computed(() => authStore.user);

// Helper function to get default avatar
const getDefaultAvatar = () => {
  return '/default-avatar.svg';
};

// Fetch user stats
const fetchUserStats = async () => {
  if (!currentUser.value) return;

  try {
    loading.value = true;
    error.value = null;

    const response = await fetch(`/.netlify/functions/user/profile/${currentUser.value.username}`, {
      headers: {
        'Authorization': `Bearer ${authStore.token}`
      }
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || 'Failed to fetch user stats');
    }

    userStats.value = {
      postsCount: data.data.postsCount || 0,
      followersCount: data.data.followersCount || 0,
      followingCount: data.data.followingCount || 0
    };

  } catch (err) {
    console.error('Error fetching user stats:', err);
    error.value = err.message || 'Failed to load profile stats';
  } finally {
    loading.value = false;
  }
};

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  });
};

onMounted(() => {
  if (currentUser.value) {
    fetchUserStats();
  } else {
    loading.value = false;
  }
});
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">My Profile</h1>
        </div>
        <router-link to="/edit-profile" class="text-neon-blue hover:text-neon-blue/80">Edit</router-link>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Breadcrumb -->
      <Breadcrumb :custom-breadcrumbs="[
        { name: 'Home', path: '/' },
        { name: 'My Profile', path: '/profile', isLast: true }
      ]" />

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-neon-blue"></div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="bg-red-900/20 border border-red-500 rounded-xl p-6 text-center">
        <p class="text-red-400">{{ error }}</p>
        <button @click="fetchUserStats" class="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
          Try Again
        </button>
      </div>

      <!-- Not Authenticated -->
      <div v-else-if="!currentUser" class="bg-yellow-900/20 border border-yellow-500 rounded-xl p-6 text-center">
        <p class="text-yellow-400">Please log in to view your profile.</p>
        <router-link to="/auth" class="mt-4 inline-block px-4 py-2 bg-neon-blue text-dark-bg rounded-lg hover:bg-neon-blue/80">
          Log In
        </router-link>
      </div>

      <!-- Profile Content -->
      <div v-else class="space-y-6">
        <!-- Profile Header -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <div class="flex flex-col md:flex-row items-start md:items-center gap-6">
            <!-- Profile Picture -->
            <div class="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden flex-shrink-0">
              <img
                :src="currentUser.profilePicture || getDefaultAvatar()"
                :alt="currentUser.username"
                class="w-full h-full object-cover"
              />
            </div>

            <!-- Profile Info -->
            <div class="flex-1 min-w-0">
              <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h1 class="text-2xl font-bold flex items-center gap-2">
                    {{ currentUser.displayName || currentUser.username }}
                    <span v-if="currentUser.isPremium" class="text-sm text-neon-blue">NTA+</span>
                    <span v-if="currentUser.isVerified" class="text-sm text-green-400">✓</span>
                  </h1>
                  <p class="text-gray-400">@{{ currentUser.username }}</p>
                  <p v-if="currentUser.bio" class="mt-2 text-gray-300">{{ currentUser.bio }}</p>
                </div>
              </div>

              <!-- Stats -->
              <div class="flex gap-6 mt-4">
                <div class="text-center">
                  <div class="text-xl font-bold">{{ userStats.postsCount }}</div>
                  <div class="text-sm text-gray-400">Posts</div>
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold">{{ userStats.followersCount }}</div>
                  <div class="text-sm text-gray-400">Followers</div>
                </div>
                <div class="text-center">
                  <div class="text-xl font-bold">{{ userStats.followingCount }}</div>
                  <div class="text-sm text-gray-400">Following</div>
                </div>
              </div>

              <!-- Join Date -->
              <div class="mt-4 text-sm text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Joined {{ formatDate(currentUser.createdAt) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 p-6">
          <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <router-link to="/edit-profile" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 transition-colors">
              <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <div>
                  <h3 class="font-medium">Edit Profile</h3>
                  <p class="text-sm text-gray-400">Update your information</p>
                </div>
              </div>
            </router-link>

            <router-link to="/settings" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 transition-colors">
              <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-neon-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <div>
                  <h3 class="font-medium">Settings</h3>
                  <p class="text-sm text-gray-400">Privacy & preferences</p>
                </div>
              </div>
            </router-link>

            <router-link v-if="!currentUser.isPremium" to="/subscription" class="p-4 border border-gray-700 rounded-lg hover:border-gray-600 transition-colors">
              <div class="flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
                <div>
                  <h3 class="font-medium">Upgrade to NTA+</h3>
                  <p class="text-sm text-gray-400">Get premium features</p>
                </div>
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
