const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { uploadFile } = require('./utils/storage');

// Create a new story
exports.createStory = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { media } = JSON.parse(event.body);

  // Validate input
  if (!media || !media.data || !media.type) {
    return error('Media data and type are required for story');
  }

  // Validate media type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/webm'];
  if (!allowedTypes.includes(media.type)) {
    return error(`Unsupported media type: ${media.type}. Allowed types: JPEG, PNG, GIF, WebP, MP4, QuickTime, WebM`);
  }

  // Validate base64 data format
  if (!media.data.startsWith('data:')) {
    return error('Invalid media data format: must be base64 data URL');
  }

  // Extract base64 data
  const base64Data = media.data.split(',')[1];
  if (!base64Data) {
    return error('Invalid media data: missing base64 content');
  }

  // Validate file size (20MB limit for stories)
  const sizeInMB = (base64Data.length * 0.75) / 1024 / 1024;
  if (sizeInMB > 20) {
    return error(`Media file exceeds 20MB size limit (current: ${sizeInMB.toFixed(2)}MB)`);
  }

  // Get user to check premium status
  const user = await findOne('users', { _id: new ObjectId(userId) });

  // Set expiry time based on premium status (24h for free, 48h for premium)
  const expiryHours = user.isPremium ? 48 : 24;
  const expiryTime = new Date(Date.now() + (expiryHours * 60 * 60 * 1000));

  // Decode base64 media (use the extracted base64 data)
  const fileBuffer = Buffer.from(base64Data, 'base64');

  // Generate unique filename
  const fileExtension = media.type.split('/')[1];
  const fileName = `stories/${userId}_${Date.now()}.${fileExtension}`;

  // Upload to B2
  const uploadResult = await uploadFile(fileBuffer, fileName, media.type);

  // Create story object
  const story = {
    author: new ObjectId(userId),
    mediaUrl: uploadResult.fileUrl,
    mediaType: media.type.startsWith('image') ? 'image' : 'video',
    viewers: [],
    createdAt: new Date(),
    expiresAt: expiryTime
  };

  // Insert story into database
  const result = await insertOne('stories', story);

  // Get the inserted story with author details
  const newStory = await aggregate('stories', [
    { $match: { _id: result.insertedId } },
    { $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'authorDetails'
      }
    },
    { $unwind: '$authorDetails' },
    { $project: {
        _id: 1,
        mediaUrl: 1,
        mediaType: 1,
        viewers: 1,
        createdAt: 1,
        expiresAt: 1,
        author: {
          _id: '$authorDetails._id',
          username: '$authorDetails.username',
          profilePicture: '$authorDetails.profilePicture',
          isPremium: '$authorDetails.isPremium'
        }
      }
    }
  ]);

  // Return the new story
  return success({ story: newStory[0] });
}));

// Get active stories
exports.getStories = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get user to check blacklisted users
  const user = await findOne('users', { _id: new ObjectId(userId) });

  // Convert blacklisted user IDs to ObjectId
  const blacklistedUserIds = (user.blacklistedUsers || []).map(id => new ObjectId(id));

  // Get active stories excluding blacklisted users
  const stories = await aggregate('stories', [
    { $match: {
        $and: [
          { author: { $nin: blacklistedUserIds } },
          { expiresAt: { $gt: new Date() } }
        ]
      }
    },
    { $lookup: {
        from: 'users',
        localField: 'author',
        foreignField: '_id',
        as: 'authorDetails'
      }
    },
    { $unwind: '$authorDetails' },
    { $project: {
        _id: 1,
        mediaUrl: 1,
        mediaType: 1,
        viewers: 1,
        createdAt: 1,
        expiresAt: 1,
        viewed: { $in: [new ObjectId(userId), '$viewers'] },
        author: {
          _id: '$authorDetails._id',
          username: '$authorDetails.username',
          profilePicture: '$authorDetails.profilePicture',
          isPremium: '$authorDetails.isPremium'
        }
      }
    },
    { $sort: { createdAt: -1 } },
    // Group stories by author
    { $group: {
        _id: '$author._id',
        author: { $first: '$author' },
        stories: {
          $push: {
            _id: '$_id',
            mediaUrl: '$mediaUrl',
            mediaType: '$mediaType',
            viewers: '$viewers',
            viewed: '$viewed',
            createdAt: '$createdAt',
            expiresAt: '$expiresAt'
          }
        }
      }
    }
  ]);

  // Return stories
  return success({ stories });
}));

// Mark story as viewed
exports.viewStory = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { storyId } = JSON.parse(event.body);

  // Validate input
  if (!storyId) {
    return error('Story ID is required');
  }

  // Check if story exists
  const story = await findOne('stories', { _id: new ObjectId(storyId) });
  if (!story) {
    return error('Story not found', 404);
  }

  // Add user to viewers array if not already viewed
  await updateOne(
    'stories',
    { _id: new ObjectId(storyId) },
    { $addToSet: { viewers: new ObjectId(userId) } }
  );

  // Return success
  return success({ message: 'Story marked as viewed' });
}));

// Get my stories
exports.getMyStories = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get active and expired stories for the user
  const stories = await aggregate('stories', [
    { $match: { author: new ObjectId(userId) } },
    { $project: {
        _id: 1,
        mediaUrl: 1,
        mediaType: 1,
        viewCount: { $size: '$viewers' },
        createdAt: 1,
        expiresAt: 1,
        isActive: { $gt: ['$expiresAt', new Date()] }
      }
    },
    { $sort: { createdAt: -1 } }
  ]);

  // Return stories
  return success({ stories });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/stories', '');

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/create' && event.httpMethod === 'POST':
      return exports.createStory(event, context);
    case path === '/feed' && event.httpMethod === 'GET':
      return exports.getStories(event, context);
    case path === '/view' && event.httpMethod === 'POST':
      return exports.viewStory(event, context);
    case path === '/my-stories' && event.httpMethod === 'GET':
      return exports.getMyStories(event, context);
    default:
      return error('Not found', 404);
  }
};
