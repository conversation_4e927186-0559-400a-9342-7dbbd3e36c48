const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne, aggregate } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Create or get conversation
exports.getOrCreateConversation = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { participantId } = JSON.parse(event.body);

  // Validate input
  if (!participantId) {
    return error('Participant ID is required');
  }

  // Check if participant exists
  const participant = await findOne('users', { _id: new ObjectId(participantId) });
  if (!participant) {
    return error('Participant not found', 404);
  }

  // Check if conversation already exists
  const existingConversation = await findOne('conversations', {
    participants: {
      $all: [new ObjectId(userId), new ObjectId(participantId)],
      $size: 2
    }
  });

  if (existingConversation) {
    // Return existing conversation
    return success({ conversation: existingConversation });
  }

  // Create new conversation
  const conversation = {
    participants: [new ObjectId(userId), new ObjectId(participantId)],
    lastMessage: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert conversation into database
  const result = await insertOne('conversations', conversation);

  // Get the inserted conversation
  const newConversation = await findOne('conversations', { _id: result.insertedId });

  // Return the new conversation
  return success({ conversation: newConversation });
}));

// Get conversations
exports.getConversations = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversations with participant details
  const conversations = await aggregate('conversations', [
    { $match: { participants: new ObjectId(userId) } },
    { $lookup: {
        from: 'users',
        localField: 'participants',
        foreignField: '_id',
        as: 'participantDetails'
      }
    },
    { $project: {
        _id: 1,
        participants: 1,
        lastMessage: 1,
        createdAt: 1,
        updatedAt: 1,
        participantDetails: {
          $filter: {
            input: '$participantDetails',
            as: 'participant',
            cond: { $ne: ['$$participant._id', new ObjectId(userId)] }
          }
        }
      }
    },
    { $unwind: '$participantDetails' },
    { $project: {
        _id: 1,
        lastMessage: 1,
        createdAt: 1,
        updatedAt: 1,
        participant: {
          _id: '$participantDetails._id',
          username: '$participantDetails.username',
          profilePicture: '$participantDetails.profilePicture',
          isPremium: '$participantDetails.isPremium'
        }
      }
    },
    { $sort: { updatedAt: -1 } }
  ]);

  // Return conversations
  return success({ conversations });
}));

// Send message
exports.sendMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { conversationId, content } = JSON.parse(event.body);

  // Validate input
  if (!conversationId || !content) {
    return error('Conversation ID and content are required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Create message
  const message = {
    conversationId: new ObjectId(conversationId),
    sender: new ObjectId(userId),
    content,
    editHistory: [],
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // Insert message into database
  const result = await insertOne('messages', message);

  // Update conversation with last message
  await updateOne(
    'conversations',
    { _id: new ObjectId(conversationId) },
    {
      $set: {
        lastMessage: {
          _id: result.insertedId,
          sender: new ObjectId(userId),
          content,
          createdAt: new Date()
        },
        updatedAt: new Date()
      }
    }
  );

  // Get the inserted message
  const newMessage = await findOne('messages', { _id: result.insertedId });

  // Return the new message
  return success({ message: newMessage });
}));

// Get messages
exports.getMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversation ID from query parameters
  const conversationId = event.queryStringParameters.conversationId;

  // Validate input
  if (!conversationId) {
    return error('Conversation ID is required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Get pagination parameters
  const page = parseInt(event.queryStringParameters?.page) || 1;
  const limit = parseInt(event.queryStringParameters?.limit) || 20;
  const skip = (page - 1) * limit;

  // Get messages
  const messages = await find(
    'messages',
    { conversationId: new ObjectId(conversationId) },
    {
      sort: { createdAt: -1 },
      skip,
      limit
    }
  );

  // Return messages
  return success({ messages, page, limit });
}));

// Edit message
exports.editMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messageId, content } = JSON.parse(event.body);

  // Validate input
  if (!messageId || !content) {
    return error('Message ID and content are required');
  }

  // Check if message exists
  const message = await findOne('messages', { _id: new ObjectId(messageId) });
  if (!message) {
    return error('Message not found', 404);
  }

  // Check if user is the sender
  if (message.sender.toString() !== userId) {
    return error('You can only edit your own messages', 403);
  }

  // Add current content to edit history
  const editHistory = [...message.editHistory, {
    content: message.content,
    editedAt: new Date()
  }];

  // Update message
  await updateOne(
    'messages',
    { _id: new ObjectId(messageId) },
    {
      $set: {
        content,
        editHistory,
        updatedAt: new Date()
      }
    }
  );

  // Return success
  return success({ message: 'Message updated successfully' });
}));

// Delete message
exports.deleteMessage = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messageId } = JSON.parse(event.body);

  // Validate input
  if (!messageId) {
    return error('Message ID is required');
  }

  // Check if message exists
  const message = await findOne('messages', { _id: new ObjectId(messageId) });
  if (!message) {
    return error('Message not found', 404);
  }

  // Check if user is the sender
  if (message.sender.toString() !== userId) {
    return error('You can only delete your own messages', 403);
  }

  // Soft delete message
  await updateOne(
    'messages',
    { _id: new ObjectId(messageId) },
    {
      $set: {
        content: 'This message has been deleted',
        isDeleted: true,
        updatedAt: new Date()
      }
    }
  );

  // Return success
  return success({ message: 'Message deleted successfully' });
}));

// Get new messages since a specific time
exports.getNewMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Get conversation ID and timestamp from query parameters
  const conversationId = event.queryStringParameters.conversationId;
  const since = event.queryStringParameters.since;

  // Validate input
  if (!conversationId || !since) {
    return error('Conversation ID and since timestamp are required');
  }

  // Check if conversation exists
  const conversation = await findOne('conversations', { _id: new ObjectId(conversationId) });
  if (!conversation) {
    return error('Conversation not found', 404);
  }

  // Check if user is a participant
  if (!conversation.participants.some(p => p.toString() === userId)) {
    return error('You are not a participant in this conversation', 403);
  }

  // Get messages since the specified timestamp
  const messages = await find(
    'messages',
    {
      conversationId: new ObjectId(conversationId),
      createdAt: { $gt: new Date(since) }
    },
    {
      sort: { createdAt: 1 }
    }
  );

  // Return messages
  return success({ messages });
}));

// Get user status
exports.getUserStatus = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { userIds } = JSON.parse(event.body);

  // Validate input
  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return error('User IDs are required');
  }

  // Convert user IDs to ObjectId
  const objectIds = userIds.map(id => new ObjectId(id));

  // Get user statuses
  const users = await find('users', { _id: { $in: objectIds } });

  // Create status map
  const statuses = {};

  for (const user of users) {
    // Check if user has a recent activity timestamp (within last 5 minutes)
    const isOnline = user.lastActivityAt &&
      (new Date() - new Date(user.lastActivityAt)) < 5 * 60 * 1000;

    statuses[user._id.toString()] = isOnline ? 'online' : 'offline';
  }

  // Return statuses
  return success({ statuses });
}));

// Update user status
exports.updateUserStatus = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { status } = JSON.parse(event.body);

  // Validate input
  if (!status) {
    return error('Status is required');
  }

  // Update user status
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    { $set: { lastActivityAt: new Date() } }
  );

  // Return success
  return success({ message: 'Status updated successfully' });
}));

// Offline sync for messages
exports.syncMessages = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;

  // Parse request body
  const { messages: pendingMessages } = JSON.parse(event.body);

  // Validate input
  if (!pendingMessages || !Array.isArray(pendingMessages) || pendingMessages.length === 0) {
    return error('Pending messages are required');
  }

  // Process each pending message
  const results = [];

  for (const pendingMessage of pendingMessages) {
    try {
      // Validate message
      if (!pendingMessage.conversationId || !pendingMessage.content) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Invalid message format'
        });
        continue;
      }

      // Check if conversation exists
      const conversation = await findOne('conversations', { _id: new ObjectId(pendingMessage.conversationId) });
      if (!conversation) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Conversation not found'
        });
        continue;
      }

      // Check if user is a participant
      if (!conversation.participants.some(p => p.toString() === userId)) {
        results.push({
          clientId: pendingMessage.clientId,
          success: false,
          error: 'Not a participant in this conversation'
        });
        continue;
      }

      // Create message
      const message = {
        conversationId: new ObjectId(pendingMessage.conversationId),
        sender: new ObjectId(userId),
        content: pendingMessage.content,
        editHistory: [],
        isDeleted: false,
        createdAt: new Date(pendingMessage.timestamp || Date.now()),
        updatedAt: new Date(pendingMessage.timestamp || Date.now())
      };

      // Insert message into database
      const result = await insertOne('messages', message);

      // Update conversation with last message
      await updateOne(
        'conversations',
        { _id: new ObjectId(pendingMessage.conversationId) },
        {
          $set: {
            lastMessage: {
              _id: result.insertedId,
              sender: new ObjectId(userId),
              content: pendingMessage.content,
              createdAt: message.createdAt
            },
            updatedAt: new Date()
          }
        }
      );

      // Add result
      results.push({
        clientId: pendingMessage.clientId,
        success: true,
        messageId: result.insertedId.toString()
      });
    } catch (err) {
      console.error('Error processing pending message:', err);

      results.push({
        clientId: pendingMessage.clientId,
        success: false,
        error: 'Server error'
      });
    }
  }

  // Return results
  return success({ results });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/messages', '');

  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/conversation' && event.httpMethod === 'POST':
      return exports.getOrCreateConversation(event, context);
    case path === '/conversations' && event.httpMethod === 'GET':
      return exports.getConversations(event, context);
    case path === '/send' && event.httpMethod === 'POST':
      return exports.sendMessage(event, context);
    case path === '/messages' && event.httpMethod === 'GET':
      return exports.getMessages(event, context);
    case path === '/edit' && event.httpMethod === 'PUT':
      return exports.editMessage(event, context);
    case path === '/delete' && event.httpMethod === 'DELETE':
      return exports.deleteMessage(event, context);
    case path === '/new-messages' && event.httpMethod === 'GET':
      return exports.getNewMessages(event, context);
    case path === '/user-status' && event.httpMethod === 'POST':
      return exports.getUserStatus(event, context);
    case path === '/update-status' && event.httpMethod === 'POST':
      return exports.updateUserStatus(event, context);
    case path === '/sync' && event.httpMethod === 'POST':
      return exports.syncMessages(event, context);
    default:
      return error('Not found', 404);
  }
};
