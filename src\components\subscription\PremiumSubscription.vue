<template>
  <div class="premium-subscription">
    <div class="subscription-header">
      <div class="header-content">
        <div class="logo-section">
          <h1 class="premium-title">
            <i class="fas fa-crown premium-icon"></i>
            NTA+ Premium
          </h1>
          <p class="premium-tagline">Unlock the full potential of NeTuArk</p>
        </div>
        
        <div v-if="isPremium" class="current-status">
          <div class="status-badge premium">
            <i class="fas fa-check-circle"></i>
            Premium Active
          </div>
          <div class="expiry-info">
            <span v-if="!isCancelled">Renews on {{ formatDate(expiryDate) }}</span>
            <span v-else class="cancelled">Expires on {{ formatDate(expiryDate) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="subscription-content">
      <!-- Features Section -->
      <div class="features-section">
        <h2>Premium Features</h2>
        <div class="features-grid">
          <div 
            v-for="feature in premiumFeatures" 
            :key="feature.id" 
            class="feature-card"
            :class="{ 'available': isPremium }"
          >
            <div class="feature-icon">
              <i :class="getFeatureIcon(feature.id)"></i>
            </div>
            <div class="feature-content">
              <h3>{{ feature.name }}</h3>
              <p>{{ feature.description }}</p>
            </div>
            <div v-if="isPremium" class="feature-status">
              <i class="fas fa-check-circle"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Section -->
      <div v-if="!isPremium" class="pricing-section">
        <h2>Simple Pricing</h2>
        <div class="pricing-card">
          <div class="price-header">
            <div class="price">
              <span class="currency">$</span>
              <span class="amount">{{ subscriptionPrice }}</span>
              <span class="period">/month</span>
            </div>
            <div class="price-description">
              Cancel anytime. No hidden fees.
            </div>
          </div>
          
          <div class="price-features">
            <div v-for="feature in premiumFeatures" :key="feature.id" class="price-feature">
              <i class="fas fa-check"></i>
              <span>{{ feature.name }}</span>
            </div>
          </div>
          
          <button 
            @click="subscribe" 
            class="subscribe-btn"
            :disabled="isSubscribing"
          >
            <span v-if="isSubscribing">
              <i class="fas fa-spinner fa-spin"></i> Processing...
            </span>
            <span v-else>
              <i class="fas fa-crown"></i> Upgrade to NTA+
            </span>
          </button>
        </div>
      </div>

      <!-- Current Subscription Management -->
      <div v-if="isPremium" class="subscription-management">
        <h2>Manage Subscription</h2>
        
        <div class="management-card">
          <div class="subscription-info">
            <div class="info-item">
              <label>Status:</label>
              <span class="status" :class="{ 'cancelled': isCancelled }">
                {{ isCancelled ? 'Cancelled' : 'Active' }}
              </span>
            </div>
            
            <div class="info-item">
              <label>{{ isCancelled ? 'Expires:' : 'Next billing:' }}</label>
              <span>{{ formatDate(expiryDate) }}</span>
            </div>
            
            <div class="info-item">
              <label>Monthly cost:</label>
              <span>${{ subscriptionPrice }}</span>
            </div>
          </div>
          
          <div class="management-actions">
            <button 
              v-if="!isCancelled" 
              @click="cancelSubscription" 
              class="cancel-btn"
              :disabled="isCancelling"
            >
              <span v-if="isCancelling">
                <i class="fas fa-spinner fa-spin"></i> Cancelling...
              </span>
              <span v-else>
                <i class="fas fa-times"></i> Cancel Subscription
              </span>
            </button>
            
            <button 
              v-else 
              @click="reactivateSubscription" 
              class="reactivate-btn"
              :disabled="isReactivating"
            >
              <span v-if="isReactivating">
                <i class="fas fa-spinner fa-spin"></i> Reactivating...
              </span>
              <span v-else>
                <i class="fas fa-redo"></i> Reactivate Subscription
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="faq-section">
        <h2>Frequently Asked Questions</h2>
        <div class="faq-list">
          <div 
            v-for="(faq, index) in faqs" 
            :key="index" 
            class="faq-item"
            :class="{ 'open': openFaq === index }"
          >
            <button @click="toggleFaq(index)" class="faq-question">
              <span>{{ faq.question }}</span>
              <i class="fas fa-chevron-down faq-icon"></i>
            </button>
            <div class="faq-answer">
              <p>{{ faq.answer }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error/Success Messages -->
    <div v-if="error" class="message error-message">
      <i class="fas fa-exclamation-circle"></i>
      {{ error }}
    </div>
    
    <div v-if="successMessage" class="message success-message">
      <i class="fas fa-check-circle"></i>
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useSubscriptionStore } from '@/stores/subscription';

// Store
const subscriptionStore = useSubscriptionStore();

// State
const isSubscribing = ref(false);
const isCancelling = ref(false);
const isReactivating = ref(false);
const error = ref(null);
const successMessage = ref(null);
const openFaq = ref(null);

// FAQ data
const faqs = ref([
  {
    question: "What happens when I upgrade to NTA+?",
    answer: "You'll immediately get access to all premium features including ad-free browsing, extended story duration, unlimited emoji creation, and priority in search results."
  },
  {
    question: "Can I cancel my subscription anytime?",
    answer: "Yes! You can cancel your NTA+ subscription at any time. You'll continue to have access to premium features until your current billing period ends."
  },
  {
    question: "What payment methods do you accept?",
    answer: "We accept all major credit cards, PayPal, and other secure payment methods through our payment processor."
  },
  {
    question: "Is there a free trial?",
    answer: "Currently, we don't offer a free trial, but our monthly subscription is very affordable at just $3/month with no long-term commitment."
  },
  {
    question: "What happens to my custom emojis if I cancel?",
    answer: "Your existing custom emojis will remain available, but you won't be able to create new ones beyond the free tier limit of 10 emojis."
  }
]);

// Computed
const isPremium = computed(() => subscriptionStore.isPremium);
const expiryDate = computed(() => subscriptionStore.expiryDate);
const isCancelled = computed(() => subscriptionStore.isCancelled);
const premiumFeatures = computed(() => subscriptionStore.allPremiumFeatures);
const subscriptionPrice = computed(() => subscriptionStore.subscriptionPrice);

// Methods
function getFeatureIcon(featureId) {
  const icons = {
    'ad-free': 'fas fa-ban',
    'extended-stories': 'fas fa-clock',
    'premium-avatars': 'fas fa-user-circle',
    'unlimited-emojis': 'fas fa-smile',
    'unlimited-ai': 'fas fa-robot',
    'search-priority': 'fas fa-search-plus'
  };
  
  return icons[featureId] || 'fas fa-star';
}

function formatDate(dateString) {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

async function subscribe() {
  if (isSubscribing.value) return;
  
  isSubscribing.value = true;
  error.value = null;
  successMessage.value = null;
  
  try {
    await subscriptionStore.subscribe();
    successMessage.value = 'Welcome to NTA+ Premium! Your subscription is now active.';
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      successMessage.value = null;
    }, 5000);
  } catch (err) {
    error.value = err.message || 'Failed to process subscription. Please try again.';
  } finally {
    isSubscribing.value = false;
  }
}

async function cancelSubscription() {
  if (isCancelling.value) return;
  
  if (!confirm('Are you sure you want to cancel your NTA+ subscription? You\'ll lose access to premium features at the end of your current billing period.')) {
    return;
  }
  
  isCancelling.value = true;
  error.value = null;
  successMessage.value = null;
  
  try {
    await subscriptionStore.cancelSubscription();
    successMessage.value = 'Your subscription has been cancelled. You\'ll continue to have access to premium features until your current billing period ends.';
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      successMessage.value = null;
    }, 5000);
  } catch (err) {
    error.value = err.message || 'Failed to cancel subscription. Please try again.';
  } finally {
    isCancelling.value = false;
  }
}

async function reactivateSubscription() {
  if (isReactivating.value) return;
  
  isReactivating.value = true;
  error.value = null;
  successMessage.value = null;
  
  try {
    await subscriptionStore.reactivateSubscription();
    successMessage.value = 'Your NTA+ subscription has been reactivated!';
    
    // Hide success message after 5 seconds
    setTimeout(() => {
      successMessage.value = null;
    }, 5000);
  } catch (err) {
    error.value = err.message || 'Failed to reactivate subscription. Please try again.';
  } finally {
    isReactivating.value = false;
  }
}

function toggleFaq(index) {
  openFaq.value = openFaq.value === index ? null : index;
}

// Lifecycle hooks
onMounted(() => {
  subscriptionStore.fetchSubscriptionStatus();
});
</script>

<style scoped>
.premium-subscription {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

.subscription-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.premium-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-icon {
  color: #f39c12;
}

.premium-tagline {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.current-status {
  text-align: right;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 24px;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.status-badge.premium {
  background-color: rgba(46, 204, 113, 0.2);
  border: 2px solid #2ecc71;
}

.expiry-info {
  font-size: 0.9rem;
  opacity: 0.9;
}

.cancelled {
  color: #e74c3c !important;
}

.subscription-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.features-section h2, .pricing-section h2, .subscription-management h2, .faq-section h2 {
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.feature-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.feature-card.available {
  border-color: #2ecc71;
  background-color: #f8fff9;
}

.feature-icon {
  width: 48px;
  height: 48px;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.feature-card.available .feature-icon {
  background-color: #2ecc71;
}

.feature-content {
  flex: 1;
}

.feature-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  color: #333;
}

.feature-content p {
  margin: 0;
  color: #777;
  line-height: 1.4;
}

.feature-status {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: #2ecc71;
  font-size: 1.2rem;
}

.pricing-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pricing-card {
  background-color: white;
  border: 2px solid #3498db;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.price-header {
  margin-bottom: 1.5rem;
}

.price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.currency {
  font-size: 1.5rem;
  color: #777;
}

.amount {
  font-size: 3rem;
  font-weight: bold;
  color: #3498db;
}

.period {
  font-size: 1.2rem;
  color: #777;
}

.price-description {
  color: #777;
  font-size: 0.9rem;
}

.price-features {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2rem;
  text-align: left;
}

.price-feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.price-feature i {
  color: #2ecc71;
  width: 16px;
}

.subscribe-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.subscribe-btn:hover {
  background-color: #2980b9;
}

.subscribe-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.management-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 1.5rem;
}

.subscription-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item label {
  font-weight: bold;
  color: #333;
}

.status.cancelled {
  color: #e74c3c;
}

.management-actions {
  display: flex;
  justify-content: center;
}

.cancel-btn, .reactivate-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cancel-btn {
  background-color: #e74c3c;
  color: white;
}

.cancel-btn:hover {
  background-color: #c0392b;
}

.reactivate-btn {
  background-color: #2ecc71;
  color: white;
}

.reactivate-btn:hover {
  background-color: #27ae60;
}

.cancel-btn:disabled, .reactivate-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.faq-item {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: bold;
}

.faq-question:hover {
  background-color: #f8f9fa;
}

.faq-icon {
  transition: transform 0.2s;
}

.faq-item.open .faq-icon {
  transform: rotate(180deg);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-item.open .faq-answer {
  max-height: 200px;
}

.faq-answer p {
  padding: 0 1rem 1rem 1rem;
  margin: 0;
  color: #777;
  line-height: 1.5;
}

.message {
  position: fixed;
  top: 1rem;
  right: 1rem;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 1000;
  max-width: 400px;
}

.error-message {
  background-color: #fee;
  color: #c0392b;
  border: 1px solid #e74c3c;
}

.success-message {
  background-color: #efe;
  color: #27ae60;
  border: 1px solid #2ecc71;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .current-status {
    text-align: center;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .subscription-info {
    gap: 0.75rem;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
}
</style>
