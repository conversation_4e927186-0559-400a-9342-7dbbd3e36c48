<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const props = defineProps({
  customBreadcrumbs: {
    type: Array,
    default: () => []
  }
});

// Generate breadcrumbs based on current route
const breadcrumbs = computed(() => {
  if (props.customBreadcrumbs.length > 0) {
    return props.customBreadcrumbs;
  }

  const pathSegments = route.path.split('/').filter(segment => segment);
  const breadcrumbItems = [{ name: 'Home', path: '/' }];

  let currentPath = '';
  
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Generate readable names for common routes
    let name = segment;
    switch (segment) {
      case 'feed':
        name = 'Feed';
        break;
      case 'profile':
        name = 'Profile';
        break;
      case 'edit-profile':
        name = 'Edit Profile';
        break;
      case 'messages':
        name = 'Messages';
        break;
      case 'discover':
        name = 'Discover';
        break;
      case 'settings':
        name = 'Settings';
        break;
      case 'create':
        name = 'Create';
        break;
      case 'emojis':
        name = 'Emojis';
        break;
      case 'stories':
        name = 'Stories';
        break;
      case 'subscription':
        name = 'Subscription';
        break;
      case 'admin':
        name = 'Admin';
        break;
      default:
        // For dynamic segments like usernames or IDs, capitalize first letter
        name = segment.charAt(0).toUpperCase() + segment.slice(1);
    }

    breadcrumbItems.push({
      name,
      path: currentPath,
      isLast: index === pathSegments.length - 1
    });
  });

  return breadcrumbItems;
});
</script>

<template>
  <nav class="breadcrumb-nav" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-2 text-sm">
      <li v-for="(item, index) in breadcrumbs" :key="item.path" class="flex items-center">
        <!-- Separator -->
        <svg
          v-if="index > 0"
          class="w-4 h-4 text-gray-500 mx-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>

        <!-- Breadcrumb item -->
        <router-link
          v-if="!item.isLast"
          :to="item.path"
          class="text-gray-400 hover:text-white transition-colors"
        >
          {{ item.name }}
        </router-link>
        
        <span
          v-else
          class="text-white font-medium"
        >
          {{ item.name }}
        </span>
      </li>
    </ol>
  </nav>
</template>

<style scoped>
.breadcrumb-nav {
  padding: 0.5rem 0;
}
</style>
