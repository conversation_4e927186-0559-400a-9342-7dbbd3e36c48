const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');
const { RecommendationEngine } = require('./utils/recommendation-engine');
const { InteractionTracker } = require('./utils/interaction-tracker');

// Initialize recommendation engine and interaction tracker
const recommendationEngine = new RecommendationEngine();
const interactionTracker = new InteractionTracker();

/**
 * Generate personalized feed recommendations
 */
exports.generateFeed = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    
    // Parse query parameters
    const page = Math.max(parseInt(event.queryStringParameters?.page) || 1, 1);
    const limit = Math.min(Math.max(parseInt(event.queryStringParameters?.limit) || 20, 1), 50);
    const sortBy = event.queryStringParameters?.sortBy || 'hybrid';
    const includeExploration = event.queryStringParameters?.exploration !== 'false';
    
    // Get posts user has already seen (for exclusion)
    const seenPosts = await find('user_interactions', 
      { 
        userId: new ObjectId(userId),
        type: 'view'
      },
      { 
        sort: { createdAt: -1 }, 
        limit: 1000,
        projection: { postId: 1 }
      }
    );
    
    const excludePostIds = seenPosts.map(interaction => interaction.postId.toString());

    // Generate recommendations using ML engine
    const recommendations = await recommendationEngine.generateRecommendations(userId, {
      limit,
      page,
      excludePostIds,
      includeExploration,
      sortBy
    });

    // Track feed generation for analytics
    await interactionTracker.trackInteraction(userId, null, 'feed_generation', {
      algorithm: recommendations.metadata.algorithm,
      totalCandidates: recommendations.metadata.totalCandidates,
      sortBy,
      page
    });

    return success({
      posts: recommendations.posts,
      scores: recommendations.scores,
      metadata: {
        ...recommendations.metadata,
        page,
        limit,
        hasMore: recommendations.posts.length === limit,
        algorithm: 'ml_hybrid'
      }
    });

  } catch (err) {
    console.error('Error generating personalized feed:', err);
    return error('Failed to generate personalized feed', 500);
  }
}));

/**
 * Track user interaction with posts
 */
exports.trackInteraction = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const { postId, type, metadata = {} } = JSON.parse(event.body);

    // Validate input
    if (!postId || !type) {
      return error('Post ID and interaction type are required', 400);
    }

    const validTypes = ['view', 'like', 'unlike', 'comment', 'share', 'save', 'click'];
    if (!validTypes.includes(type)) {
      return error(`Invalid interaction type. Must be one of: ${validTypes.join(', ')}`, 400);
    }

    // Track the interaction
    const result = await interactionTracker.trackInteraction(userId, postId, type, {
      ...metadata,
      userAgent: event.headers['user-agent'],
      timestamp: new Date()
    });

    if (!result.success) {
      return error('Failed to track interaction', 500);
    }

    return success({ 
      message: 'Interaction tracked successfully',
      type,
      postId 
    });

  } catch (err) {
    console.error('Error tracking interaction:', err);
    return error('Failed to track interaction', 500);
  }
}));

/**
 * Get user's interaction analytics
 */
exports.getUserAnalytics = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const timeframe = event.queryStringParameters?.timeframe || '7d';

    // Get interaction summary
    const summary = await interactionTracker.getUserInteractionSummary(userId, timeframe);
    
    if (!summary) {
      return error('Failed to get user analytics', 500);
    }

    // Get user profile strength
    const userProfile = await recommendationEngine.getUserProfile(userId);

    return success({
      summary,
      profileStrength: userProfile.strength,
      preferences: {
        topTags: Object.entries(userProfile.preferredTags)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 10)
          .map(([tag, weight]) => ({ tag, weight })),
        mediaPreferences: userProfile.mediaPreferences,
        activityLevel: userProfile.activityLevel
      },
      timeframe
    });

  } catch (err) {
    console.error('Error getting user analytics:', err);
    return error('Failed to get user analytics', 500);
  }
}));

/**
 * Get trending posts
 */
exports.getTrending = handleAsync(async (event) => {
  try {
    const timeframe = event.queryStringParameters?.timeframe || '24h';
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 20, 50);

    // Get trending posts from interaction tracker
    const trendingData = await interactionTracker.getTrendingPosts(timeframe, limit);

    // Get full post details
    const postIds = trendingData.map(item => item._id);
    const posts = await find('posts', 
      { _id: { $in: postIds } },
      {
        lookup: {
          from: 'users',
          localField: 'author',
          foreignField: '_id',
          as: 'authorDetails'
        }
      }
    );

    // Combine trending data with post details
    const trendingPosts = trendingData.map(trending => {
      const post = posts.find(p => p._id.toString() === trending._id.toString());
      return {
        ...post,
        trendingScore: trending.trendingScore,
        interactions: {
          total: trending.totalInteractions,
          likes: trending.likes,
          comments: trending.comments,
          shares: trending.shares,
          views: trending.views,
          uniqueUsers: trending.uniqueUserCount
        }
      };
    }).filter(post => post._id); // Filter out posts that weren't found

    return success({
      posts: trendingPosts,
      timeframe,
      metadata: {
        algorithm: 'trending',
        totalPosts: trendingPosts.length
      }
    });

  } catch (err) {
    console.error('Error getting trending posts:', err);
    return error('Failed to get trending posts', 500);
  }
});

/**
 * Get similar posts based on content
 */
exports.getSimilarPosts = handleAsync(async (event) => {
  try {
    const postId = event.queryStringParameters?.postId;
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 10, 20);

    if (!postId) {
      return error('Post ID is required', 400);
    }

    // Get the reference post
    const referencePost = await findOne('posts', { _id: new ObjectId(postId) });
    if (!referencePost) {
      return error('Post not found', 404);
    }

    // Find similar posts based on AI tags
    const similarPosts = await find('posts', {
      _id: { $ne: new ObjectId(postId) },
      aiTags: { $in: referencePost.aiTags || [] },
      isDeleted: { $ne: true },
      visibility: 'public'
    }, {
      limit,
      sort: { createdAt: -1 }
    });

    // Add similarity scores
    const postsWithScores = similarPosts.map(post => {
      const commonTags = (post.aiTags || []).filter(tag => 
        (referencePost.aiTags || []).includes(tag)
      );
      const similarity = commonTags.length / Math.max(
        (post.aiTags || []).length, 
        (referencePost.aiTags || []).length
      );

      return {
        ...post,
        similarityScore: similarity,
        commonTags
      };
    }).sort((a, b) => b.similarityScore - a.similarityScore);

    return success({
      posts: postsWithScores,
      referencePost: {
        _id: referencePost._id,
        aiTags: referencePost.aiTags
      },
      metadata: {
        algorithm: 'content_similarity',
        totalPosts: postsWithScores.length
      }
    });

  } catch (err) {
    console.error('Error getting similar posts:', err);
    return error('Failed to get similar posts', 500);
  }
});

/**
 * Get personalized hashtag recommendations
 */
exports.getHashtagRecommendations = handleAsync(requireAuth(async (event) => {
  try {
    const userId = event.user._id;
    const limit = Math.min(parseInt(event.queryStringParameters?.limit) || 10, 20);

    // Get user profile
    const userProfile = await recommendationEngine.getUserProfile(userId);

    // Get trending hashtags
    const trendingTags = await interactionTracker.getTrendingPosts('7d', 100);
    
    // Extract and score hashtags
    const tagScores = {};
    
    // Score based on user preferences
    Object.entries(userProfile.preferredTags).forEach(([tag, weight]) => {
      tagScores[tag] = (tagScores[tag] || 0) + weight * 2; // User preference weight
    });

    // Add trending factor
    trendingTags.forEach(trending => {
      const post = posts.find(p => p._id.toString() === trending._id.toString());
      if (post && post.aiTags) {
        post.aiTags.forEach(tag => {
          tagScores[tag] = (tagScores[tag] || 0) + (trending.trendingScore / 1000);
        });
      }
    });

    // Sort and format recommendations
    const recommendations = Object.entries(tagScores)
      .sort(([,a], [,b]) => b - a)
      .slice(0, limit)
      .map(([tag, score]) => ({
        tag,
        score,
        reason: userProfile.preferredTags[tag] ? 'personal_preference' : 'trending'
      }));

    return success({
      hashtags: recommendations,
      metadata: {
        algorithm: 'hybrid_hashtag',
        userProfileStrength: userProfile.strength
      }
    });

  } catch (err) {
    console.error('Error getting hashtag recommendations:', err);
    return error('Failed to get hashtag recommendations', 500);
  }
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/recommendation', '');
  
  switch (true) {
    case path === '/feed' && event.httpMethod === 'GET':
      return exports.generateFeed(event, context);
    case path === '/track' && event.httpMethod === 'POST':
      return exports.trackInteraction(event, context);
    case path === '/analytics' && event.httpMethod === 'GET':
      return exports.getUserAnalytics(event, context);
    case path === '/trending' && event.httpMethod === 'GET':
      return exports.getTrending(event, context);
    case path === '/similar' && event.httpMethod === 'GET':
      return exports.getSimilarPosts(event, context);
    case path === '/hashtags' && event.httpMethod === 'GET':
      return exports.getHashtagRecommendations(event, context);
    default:
      return error('Not found', 404);
  }
};
