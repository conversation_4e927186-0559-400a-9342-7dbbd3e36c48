import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { useSubscriptionStore } from './subscription';

export const useAdsStore = defineStore('ads', () => {
  // State
  const feedAds = ref([]);
  const storyAds = ref([]);
  const carouselAds = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const adImpressions = ref({});
  const adClicks = ref({});
  
  // Getters
  const allFeedAds = computed(() => feedAds.value);
  const allStoryAds = computed(() => storyAds.value);
  const allCarouselAds = computed(() => carouselAds.value);
  
  // Check if ads should be shown (not for premium users)
  const shouldShowAds = computed(() => {
    const subscriptionStore = useSubscriptionStore();
    return !subscriptionStore.isPremium;
  });
  
  // Actions
  
  // Fetch feed ads
  async function fetchFeedAds() {
    // Don't fetch ads for premium users
    if (!shouldShowAds.value) {
      feedAds.value = [];
      return [];
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/ads/feed');
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch feed ads');
      }
      
      feedAds.value = data.data.ads;
      return data.data.ads;
    } catch (err) {
      error.value = err.message;
      console.error('Error fetching feed ads:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch story ads
  async function fetchStoryAds() {
    // Don't fetch ads for premium users
    if (!shouldShowAds.value) {
      storyAds.value = [];
      return [];
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/ads/story');
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch story ads');
      }
      
      storyAds.value = data.data.ads;
      return data.data.ads;
    } catch (err) {
      error.value = err.message;
      console.error('Error fetching story ads:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch carousel ads
  async function fetchCarouselAds() {
    // Don't fetch ads for premium users
    if (!shouldShowAds.value) {
      carouselAds.value = [];
      return [];
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/ads/carousel');
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch carousel ads');
      }
      
      carouselAds.value = data.data.ads;
      return data.data.ads;
    } catch (err) {
      error.value = err.message;
      console.error('Error fetching carousel ads:', err);
      return [];
    } finally {
      loading.value = false;
    }
  }
  
  // Track ad impression
  async function trackImpression(adId, adType) {
    // Don't track for premium users
    if (!shouldShowAds.value) return;
    
    // Check if we've already tracked this impression
    const key = `${adId}-${adType}`;
    if (adImpressions.value[key]) return;
    
    // Mark as tracked
    adImpressions.value[key] = true;
    
    try {
      await fetch('/.netlify/functions/ads/impression', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adId, adType })
      });
    } catch (err) {
      console.error('Error tracking ad impression:', err);
    }
  }
  
  // Track ad click
  async function trackClick(adId, adType) {
    // Don't track for premium users
    if (!shouldShowAds.value) return;
    
    // Check if we've already tracked this click
    const key = `${adId}-${adType}`;
    if (adClicks.value[key]) return;
    
    // Mark as tracked
    adClicks.value[key] = true;
    
    try {
      await fetch('/.netlify/functions/ads/click', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ adId, adType })
      });
    } catch (err) {
      console.error('Error tracking ad click:', err);
    }
  }
  
  // Get ad for feed
  function getAdForFeed(index) {
    if (!shouldShowAds.value || feedAds.value.length === 0) return null;
    
    // Show an ad every 5 posts
    if (index % 5 !== 0 || index === 0) return null;
    
    // Get ad based on index
    const adIndex = Math.floor(index / 5) % feedAds.value.length;
    const ad = feedAds.value[adIndex];
    
    // Track impression
    if (ad) {
      trackImpression(ad._id, 'feed');
    }
    
    return ad;
  }
  
  // Get ad for story
  function getAdForStory(index) {
    if (!shouldShowAds.value || storyAds.value.length === 0) return null;
    
    // Show an ad every 4 stories
    if (index % 4 !== 0 || index === 0) return null;
    
    // Get ad based on index
    const adIndex = Math.floor(index / 4) % storyAds.value.length;
    const ad = storyAds.value[adIndex];
    
    // Track impression
    if (ad) {
      trackImpression(ad._id, 'story');
    }
    
    return ad;
  }
  
  // Get ad for carousel
  function getAdForCarousel(index) {
    if (!shouldShowAds.value || carouselAds.value.length === 0) return null;
    
    // Show an ad every 6 carousel items
    if (index % 6 !== 0 || index === 0) return null;
    
    // Get ad based on index
    const adIndex = Math.floor(index / 6) % carouselAds.value.length;
    const ad = carouselAds.value[adIndex];
    
    // Track impression
    if (ad) {
      trackImpression(ad._id, 'carousel');
    }
    
    return ad;
  }
  
  return {
    // State
    feedAds,
    storyAds,
    carouselAds,
    loading,
    error,
    
    // Getters
    allFeedAds,
    allStoryAds,
    allCarouselAds,
    shouldShowAds,
    
    // Actions
    fetchFeedAds,
    fetchStoryAds,
    fetchCarouselAds,
    trackImpression,
    trackClick,
    getAdForFeed,
    getAdForStory,
    getAdForCarousel
  };
});
