<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { usePostsStore } from '../../stores/posts';
import { useAuthStore } from '../../stores/auth';
import { useAIAssistantStore } from '../../stores/ai-assistant';

const route = useRoute();
const router = useRouter();
const postsStore = usePostsStore();
const authStore = useAuthStore();
const aiAssistantStore = useAIAssistantStore();

// State
const isLoading = ref(true);
const comment = ref('');
const isSubmittingComment = ref(false);
const aiResponse = ref('');
const isLoadingAI = ref(false);

// Get post ID from route
const postId = route.params.id;

// Load post data
onMounted(async () => {
  isLoading.value = true;

  try {
    await postsStore.fetchPostById(postId);
  } catch (error) {
    console.error('Error loading post:', error);
  } finally {
    isLoading.value = false;
  }
});

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Like/unlike post
const toggleLike = async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  try {
    const post = postsStore.currentPostDetails;
    if (post.isLiked) {
      await postsStore.unlikePost(post._id);
    } else {
      await postsStore.likePost(post._id);
    }
  } catch (error) {
    console.error('Error toggling like:', error);
  }
};

// Submit comment (placeholder for now)
const submitComment = async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  if (!comment.value.trim() || isSubmittingComment.value) return;

  isSubmittingComment.value = true;

  try {
    // This would call a comment API in a real implementation
    console.log('Submitting comment:', comment.value);

    // Clear comment input
    comment.value = '';
  } catch (error) {
    console.error('Error submitting comment:', error);
  } finally {
    isSubmittingComment.value = false;
  }
};

// Ask AI about this post
const askAI = async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login');
    return;
  }

  isLoadingAI.value = true;

  try {
    const post = postsStore.currentPostDetails;
    const response = await aiAssistantStore.askAI(
      `Tell me about this post: "${post.content}"`,
      post._id
    );

    aiResponse.value = response;
  } catch (error) {
    console.error('Error asking AI:', error);
    aiResponse.value = 'Sorry, I encountered an error analyzing this post.';
  } finally {
    isLoadingAI.value = false;
  }
};
</script>

<template>
  <div class="min-h-screen bg-dark-bg">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-darker-bg/80 backdrop-blur-md border-b border-gray-800">
      <div class="max-w-4xl mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center">
          <button @click="router.back()" class="mr-3 text-gray-300 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 class="text-lg font-medium">Post</h1>
        </div>
      </div>
    </header>

    <div class="max-w-4xl mx-auto px-4 pt-20 pb-16">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-10">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-neon-blue"></div>
      </div>

      <!-- Post not found -->
      <div v-else-if="!postsStore.currentPostDetails" class="flex flex-col items-center justify-center py-10">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p class="text-gray-400 text-lg">Post not found</p>
        <router-link to="/feed" class="mt-4 text-neon-blue hover:underline">Return to feed</router-link>
      </div>

      <!-- Post content -->
      <div v-else>
        <div class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden mb-6">
          <!-- Post header -->
          <div class="p-4 flex items-center">
            <router-link :to="`/profile/${postsStore.currentPostDetails.author.username}`" class="flex items-center">
              <div class="w-10 h-10 rounded-full overflow-hidden mr-3">
                <img
                  :src="postsStore.currentPostDetails.author.profilePicture || '/default-avatar.svg'"
                  :alt="postsStore.currentPostDetails.author.username"
                  class="w-full h-full object-cover"
                />
              </div>
              <div>
                <div class="font-medium flex items-center">
                  {{ postsStore.currentPostDetails.author.username }}
                  <span
                    v-if="postsStore.currentPostDetails.author.isPremium"
                    class="ml-1 text-xs text-neon-blue"
                  >
                    NTA+
                  </span>
                </div>
                <div class="text-xs text-gray-400">
                  {{ formatDate(postsStore.currentPostDetails.createdAt) }}
                </div>
              </div>
            </router-link>
          </div>

          <!-- Media -->
          <div
            v-if="postsStore.currentPostDetails.media && postsStore.currentPostDetails.media.length > 0"
            class="relative"
          >
            <img
              v-if="postsStore.currentPostDetails.media[0].type === 'image'"
              :src="postsStore.currentPostDetails.media[0].url"
              :alt="postsStore.currentPostDetails.content"
              class="w-full max-h-[500px] object-cover"
            />
            <video
              v-else
              :src="postsStore.currentPostDetails.media[0].url"
              controls
              class="w-full max-h-[500px] object-cover"
            ></video>
          </div>

          <!-- Text content -->
          <div class="p-4">
            <p class="text-white">{{ postsStore.currentPostDetails.content }}</p>

            <!-- AI Tags -->
            <div
              v-if="postsStore.currentPostDetails.aiTags && postsStore.currentPostDetails.aiTags.length > 0"
              class="mt-3 flex flex-wrap gap-2"
            >
              <span
                v-for="tag in postsStore.currentPostDetails.aiTags"
                :key="tag"
                class="px-2 py-1 rounded-full text-xs bg-neon-blue/10 text-neon-blue border border-neon-blue/30"
              >
                #{{ tag }}
              </span>
            </div>
          </div>

          <!-- Post actions -->
          <div class="px-4 py-3 border-t border-gray-800 flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button
                @click="toggleLike"
                class="flex items-center text-gray-400 hover:text-neon-pink"
                :class="{ 'text-neon-pink': postsStore.currentPostDetails.isLiked }"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  :fill="postsStore.currentPostDetails.isLiked ? 'currentColor' : 'none'"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
                <span class="ml-1">{{ postsStore.currentPostDetails.likes?.length || 0 }}</span>
              </button>

              <button class="flex items-center text-gray-400 hover:text-neon-blue">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                <span class="ml-1">{{ postsStore.currentPostDetails.comments?.length || 0 }}</span>
              </button>
            </div>

            <button class="text-gray-400 hover:text-neon-blue">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- AI Analysis -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden mb-6 p-4">
          <div class="flex items-center mb-3">
            <div class="w-8 h-8 rounded-full bg-neon-purple/20 flex items-center justify-center mr-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 text-neon-purple"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            </div>
            <h3 class="font-medium">Kraizer AI Analysis</h3>
          </div>

          <div v-if="!aiResponse && !isLoadingAI" class="text-gray-400 text-sm">
            <p>Ask Kraizer to analyze this post for insights and context.</p>
            <button
              @click="askAI"
              class="mt-2 px-3 py-1.5 bg-neon-purple/10 hover:bg-neon-purple/20 text-neon-purple rounded-md text-sm transition-colors"
            >
              Analyze with AI
            </button>
          </div>

          <div v-else-if="isLoadingAI" class="flex items-center text-gray-400 text-sm">
            <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-neon-purple mr-2"></div>
            Analyzing post...
          </div>

          <div v-else class="text-gray-200 text-sm bg-neon-purple/5 p-3 rounded-md border border-neon-purple/20">
            {{ aiResponse }}
          </div>
        </div>

        <!-- Comment form -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden mb-6 p-4">
          <form @submit.prevent="submitComment" class="flex">
            <input
              v-model="comment"
              type="text"
              placeholder="Add a comment..."
              class="flex-grow px-4 py-2 rounded-md bg-dark-bg border border-gray-700 text-white focus:border-neon-blue focus:ring-1 focus:ring-neon-blue"
            />
            <button
              type="submit"
              :disabled="!comment.trim() || isSubmittingComment"
              class="ml-2 px-4 py-2 bg-neon-blue text-white rounded-md hover:bg-neon-blue/90 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isSubmittingComment">...</span>
              <span v-else>Post</span>
            </button>
          </form>
        </div>

        <!-- Comments (placeholder) -->
        <div class="bg-darker-bg rounded-xl border border-gray-800 overflow-hidden p-4">
          <p class="text-gray-400 text-center py-4">No comments yet</p>
        </div>
      </div>
    </div>
  </div>
</template>
