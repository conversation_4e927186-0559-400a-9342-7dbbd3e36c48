<template>
  <nav class="main-navigation">
    <div class="nav-container">
      <!-- Logo Section -->
      <div class="nav-logo">
        <router-link to="/" class="logo-link">
          <img src="/logo1.png" alt="NeTuArk" class="logo-image" />
          <span class="logo-text">NeTuArk</span>
        </router-link>
        <div class="motto">The World Is Opening</div>
      </div>

      <!-- Search Bar -->
      <div class="nav-search">
        <div class="search-container">
          <i class="fas fa-search search-icon"></i>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search NeTuArk..."
            class="search-input"
            @focus="showSearchResults = true"
            @blur="hideSearchResults"
            @input="handleSearch"
          />

          <!-- Search Results Dropdown -->
          <div v-if="showSearchResults && (searchResults.length > 0 || searchQuery.trim().length > 0)" class="search-results">
            <div v-if="searchingUsers" class="search-loading">
              <i class="fas fa-spinner fa-spin"></i> Searching...
            </div>

            <div v-else-if="searchResults.length === 0 && searchQuery.trim().length > 0" class="no-results">
              No users found for "{{ searchQuery }}"
            </div>

            <div v-else class="results-list">
              <router-link
                v-for="user in searchResults.slice(0, 5)"
                :key="user._id"
                :to="`/profile/${user.username}`"
                class="result-item"
                @click="clearSearch"
              >
                <img :src="user.profilePicture || defaultAvatar()" :alt="user.username" class="result-avatar" />
                <div class="result-info">
                  <div class="result-username">
                    {{ user.username }}
                    <i v-if="user.isPremium" class="fas fa-crown premium-badge"></i>
                  </div>
                  <div v-if="user.displayName" class="result-display-name">{{ user.displayName }}</div>
                </div>
              </router-link>

              <router-link
                v-if="searchResults.length > 5"
                to="/discover"
                class="view-all-link"
                @click="clearSearch"
              >
                View all {{ searchResults.length }} results
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation Links -->
      <div class="nav-links">
        <router-link to="/" class="nav-link" :class="{ active: $route.path === '/' }">
          <i class="fas fa-home"></i>
          <span class="link-text">Home</span>
        </router-link>

        <router-link to="/discover" class="nav-link" :class="{ active: $route.path.startsWith('/discover') }">
          <i class="fas fa-compass"></i>
          <span class="link-text">Discover</span>
        </router-link>

        <router-link to="/messages" class="nav-link" :class="{ active: $route.path.startsWith('/messages') }">
          <i class="fas fa-envelope"></i>
          <span class="link-text">Messages</span>
          <span v-if="unreadMessages > 0" class="notification-badge">{{ unreadMessages }}</span>
        </router-link>

        <!-- Notification Center -->
        <NotificationCenter />

        <router-link to="/emojis" class="nav-link" :class="{ active: $route.path.startsWith('/emojis') }">
          <i class="fas fa-smile"></i>
          <span class="link-text">Emojis</span>
        </router-link>

        <router-link to="/subscription" class="nav-link premium-link" :class="{ active: $route.path.startsWith('/subscription') }">
          <i class="fas fa-crown"></i>
          <span class="link-text">{{ isPremium ? 'NTA+' : 'Upgrade' }}</span>
        </router-link>
      </div>

      <!-- Floating Create Button -->
      <router-link to="/create" class="create-fab" :class="{ active: $route.path === '/create' }">
        <i class="fas fa-plus"></i>
      </router-link>

      <!-- User Menu -->
      <div class="nav-user">
        <div class="user-menu" @click="showUserMenu = !showUserMenu">
          <img :src="currentUser?.profilePicture || defaultAvatar()" :alt="currentUser?.username" class="user-avatar" />
          <div class="user-info">
            <div class="username">
              {{ currentUser?.username }}
              <i v-if="isPremium" class="fas fa-crown premium-badge"></i>
            </div>
            <div class="user-status">{{ isPremium ? 'Premium' : 'Free' }}</div>
          </div>
          <i class="fas fa-chevron-down dropdown-icon" :class="{ 'rotated': showUserMenu }"></i>
        </div>

        <!-- User Dropdown Menu -->
        <div v-if="showUserMenu" class="user-dropdown" @click.stop>
          <router-link :to="`/profile/${currentUser?.username}`" class="dropdown-item" @click="showUserMenu = false">
            <i class="fas fa-user"></i>
            <span>My Profile</span>
          </router-link>

          <router-link to="/settings" class="dropdown-item" @click="showUserMenu = false">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
          </router-link>

          <router-link to="/privacy" class="dropdown-item" @click="showUserMenu = false">
            <i class="fas fa-shield-alt"></i>
            <span>Privacy</span>
          </router-link>

          <router-link v-if="!isPremium" to="/subscription" class="dropdown-item premium-item" @click="showUserMenu = false">
            <i class="fas fa-crown"></i>
            <span>Upgrade to NTA+</span>
          </router-link>

          <router-link v-if="currentUser?.isAdmin" to="/admin" class="dropdown-item admin-item" @click="showUserMenu = false">
            <i class="fas fa-shield-alt"></i>
            <span>Admin Dashboard</span>
          </router-link>

          <div class="dropdown-divider"></div>

          <button @click="logout" class="dropdown-item logout-item">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div class="mobile-nav">
      <router-link to="/" class="mobile-nav-item" :class="{ active: $route.path === '/' }">
        <i class="fas fa-home"></i>
        <span>Home</span>
      </router-link>

      <router-link to="/discover" class="mobile-nav-item" :class="{ active: $route.path.startsWith('/discover') }">
        <i class="fas fa-compass"></i>
        <span>Discover</span>
      </router-link>

      <router-link to="/messages" class="mobile-nav-item" :class="{ active: $route.path.startsWith('/messages') }">
        <i class="fas fa-envelope"></i>
        <span>Messages</span>
        <span v-if="unreadMessages > 0" class="mobile-notification-badge">{{ unreadMessages }}</span>
      </router-link>

      <router-link to="/emojis" class="mobile-nav-item" :class="{ active: $route.path.startsWith('/emojis') }">
        <i class="fas fa-smile"></i>
        <span>Emojis</span>
      </router-link>

      <router-link :to="`/profile/${currentUser?.username}`" class="mobile-nav-item" :class="{ active: $route.path.includes('/profile/') }">
        <img :src="currentUser?.profilePicture || defaultAvatar()" :alt="currentUser?.username" class="mobile-user-avatar" />
        <span>Profile</span>
      </router-link>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import { useSubscriptionStore } from '@/stores/subscription';
import { useDiscoveryStore } from '@/stores/discovery';
import NotificationCenter from '../notifications/NotificationCenter.vue';

// Router
const router = useRouter();

// Stores
const authStore = useAuthStore();
const subscriptionStore = useSubscriptionStore();
const discoveryStore = useDiscoveryStore();

// State
const searchQuery = ref('');
const searchResults = ref([]);
const showSearchResults = ref(false);
const searchingUsers = ref(false);
const searchTimeout = ref(null);
const showUserMenu = ref(false);
const unreadMessages = ref(0); // This would be populated from a messages store

// Computed
const currentUser = computed(() => authStore.user);
const isPremium = computed(() => subscriptionStore.isPremium);

// Methods
function defaultAvatar() {
  return '/default-avatar.svg';
}

function handleSearch() {
  // Clear existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  // Set new timeout for debounced search
  searchTimeout.value = setTimeout(async () => {
    if (searchQuery.value.trim().length >= 2) {
      searchingUsers.value = true;

      try {
        const results = await discoveryStore.searchUsers(searchQuery.value);
        searchResults.value = results;
      } catch (err) {
        console.error('Search failed:', err);
        searchResults.value = [];
      } finally {
        searchingUsers.value = false;
      }
    } else {
      searchResults.value = [];
    }
  }, 300);
}

function clearSearch() {
  searchQuery.value = '';
  searchResults.value = [];
  showSearchResults.value = false;
}

function hideSearchResults() {
  // Delay hiding to allow for clicks on results
  setTimeout(() => {
    showSearchResults.value = false;
  }, 200);
}

async function logout() {
  try {
    await authStore.logout();
    router.push('/auth');
  } catch (err) {
    console.error('Logout failed:', err);
  }
}

// Close dropdowns when clicking outside
function handleClickOutside(event) {
  if (!event.target.closest('.user-menu')) {
    showUserMenu.value = false;
  }

  if (!event.target.closest('.search-container')) {
    showSearchResults.value = false;
  }
}

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.main-navigation {
  background-color: white;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #333;
}

.logo-image {
  height: 32px;
  width: auto;
}

.logo-text {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3498db;
}

.motto {
  font-size: 0.7rem;
  color: #777;
  font-style: italic;
  margin-top: 0.25rem;
}

.nav-search {
  flex: 1;
  max-width: 400px;
  margin: 0 2rem;
  position: relative;
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #777;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid #ddd;
  border-radius: 24px;
  font-size: 1rem;
  outline: none;
  background-color: #f8f9fa;
}

.search-input:focus {
  border-color: #3498db;
  background-color: white;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.search-loading, .no-results {
  padding: 1rem;
  text-align: center;
  color: #777;
}

.results-list {
  display: flex;
  flex-direction: column;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:hover {
  background-color: #f8f9fa;
}

.result-item:last-child {
  border-bottom: none;
}

.result-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.result-info {
  flex: 1;
}

.result-username {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.result-display-name {
  font-size: 0.9rem;
  color: #777;
}

.premium-badge {
  color: #f39c12;
  font-size: 0.8rem;
}

.view-all-link {
  padding: 0.75rem 1rem;
  text-align: center;
  color: #3498db;
  text-decoration: none;
  font-weight: bold;
  border-top: 1px solid #f0f0f0;
}

.view-all-link:hover {
  background-color: #f8f9fa;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: #777;
  border-radius: 8px;
  position: relative;
  transition: all 0.2s;
}

.nav-link:hover {
  background-color: #f0f0f0;
  color: #333;
}

.nav-link.active {
  color: #3498db;
  background-color: #e3f2fd;
}

.nav-link.premium-link {
  color: #f39c12;
}

.nav-link.premium-link:hover {
  background-color: #fef9e7;
}

.link-text {
  font-weight: 500;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #e74c3c;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.4rem;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.nav-user {
  position: relative;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-menu:hover {
  background-color: #f0f0f0;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.user-status {
  font-size: 0.8rem;
  color: #777;
}

.dropdown-icon {
  color: #777;
  transition: transform 0.2s;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.5rem;
  min-width: 200px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #333;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item.premium-item {
  color: #f39c12;
}

.dropdown-item.admin-item {
  color: #e74c3c;
  font-weight: 600;
}

.dropdown-item.admin-item:hover {
  background-color: #fee;
}

.dropdown-item.logout-item {
  color: #e74c3c;
}

.dropdown-divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 0.5rem 0;
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #ddd;
  padding: 0.5rem;
  z-index: 100;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  text-decoration: none;
  color: #777;
  font-size: 0.8rem;
  position: relative;
  flex: 1;
}

.mobile-nav-item.active {
  color: #3498db;
}

.mobile-user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.mobile-notification-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: #e74c3c;
  color: white;
  font-size: 0.6rem;
  padding: 0.1rem 0.3rem;
  border-radius: 8px;
  min-width: 12px;
  text-align: center;
}

@media (max-width: 768px) {
  .nav-container {
    padding: 0.5rem 1rem;
  }

  .nav-search {
    margin: 0 1rem;
    max-width: 200px;
  }

  .nav-links {
    display: none;
  }

  .user-info {
    display: none;
  }

  .mobile-nav {
    display: flex;
  }

  .motto {
    display: none;
  }

  .link-text {
    display: none;
  }
}

@media (max-width: 480px) {
  .nav-search {
    display: none;
  }

  .nav-container {
    justify-content: space-between;
  }
}

/* Floating Create Button */
.create-fab {
  position: fixed;
  bottom: 1.5rem;
  right: 1.5rem;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  z-index: 50;
  transition: all 0.3s ease;
  text-decoration: none;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  box-shadow: 0 8px 25px rgba(0, 212, 255, 0.4);
}

.create-fab:hover {
  background: linear-gradient(135deg, #00b8e6 0%, #0088bb 100%);
  transform: translateY(-4px) scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 212, 255, 0.5);
  color: white;
}

.create-fab.active {
  background: linear-gradient(135deg, #0088bb 0%, #006699 100%);
  transform: rotate(45deg);
}

.create-fab i {
  transition: transform 0.3s ease;
}

.create-fab:hover i {
  transform: rotate(90deg);
}

@media (max-width: 768px) {
  .create-fab {
    bottom: 5rem;
    right: 1rem;
    width: 3rem;
    height: 3rem;
    font-size: 1.125rem;
  }
}
</style>
