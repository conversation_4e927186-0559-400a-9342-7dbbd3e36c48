// Simple test to check if the auth endpoint is accessible
const fetch = require('node-fetch');

async function testSimple() {
  console.log('Testing simple auth endpoint access...\n');

  try {
    console.log('Testing auth endpoint...');
    const response = await fetch('http://localhost:8888/.netlify/functions/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'test123'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response body:', text);

    if (response.ok) {
      console.log('✅ Auth endpoint is accessible!');
    } else {
      console.log('❌ Auth endpoint returned error');
    }

  } catch (error) {
    console.error('❌ Failed to access auth endpoint:', error.message);
  }
}

testSimple();
