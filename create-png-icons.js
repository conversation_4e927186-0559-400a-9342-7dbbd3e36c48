#!/usr/bin/env node
/**
 * PNG Icon Generator for NeTuArk PWA
 * Creates PNG icons using Canvas API
 */

const fs = require('fs');
const path = require('path');

// Icon sizes needed for PWA
const ICON_SIZES = [48, 72, 96, 144, 192, 512];

// NeTuArk brand colors
const BG_COLOR = '#00d4ff';
const TEXT_COLOR = '#0a0a0a';
const ACCENT_COLOR = '#ffffff';

function createNeTuArkSVG(size) {
  return `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${BG_COLOR};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
        </filter>
      </defs>
      
      <!-- Background circle -->
      <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 4}" fill="url(#bgGradient)" filter="url(#shadow)"/>
      
      <!-- NeTuArk logo elements -->
      <g transform="translate(${size/2}, ${size/2})">
        <!-- Central N -->
        <text x="0" y="${size * 0.1}" text-anchor="middle" 
              font-family="Arial, sans-serif" 
              font-weight="bold" 
              font-size="${size * 0.4}" 
              fill="${TEXT_COLOR}">N</text>
        
        <!-- Connection lines -->
        <line x1="${-size * 0.2}" y1="${-size * 0.1}" x2="${size * 0.2}" y2="${size * 0.1}" 
              stroke="${TEXT_COLOR}" stroke-width="${size * 0.02}" opacity="0.7"/>
        
        <!-- Small dots -->
        <circle cx="${-size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="${TEXT_COLOR}" opacity="0.8"/>
        <circle cx="${size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="${TEXT_COLOR}" opacity="0.8"/>
        <circle cx="0" cy="${size * 0.2}" r="${size * 0.015}" fill="${TEXT_COLOR}" opacity="0.6"/>
      </g>
    </svg>
  `;
}

function createPNGFromSVG(svgContent, size) {
  // For Node.js environment without canvas, we'll create a simple base64 PNG
  // This is a fallback approach - in a real environment you'd use canvas or sharp
  
  // Create a simple PNG data URL (this is a minimal implementation)
  const canvas = createSimpleCanvas(size);
  return canvas;
}

function createSimpleCanvas(size) {
  // Since we don't have canvas in Node.js by default, let's create the icons manually
  // We'll copy the existing SVG icons and create a conversion script
  
  const svgContent = createNeTuArkSVG(size);
  
  // For now, let's save the SVG and provide instructions for conversion
  return svgContent;
}

function saveIcon(content, size, format = 'svg') {
  const outputDir = 'public';
  const filename = `icon-${size}.${format}`;
  const filepath = path.join(outputDir, filename);
  
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save the file
  fs.writeFileSync(filepath, content, 'utf8');
  console.log(`✅ Created ${filename} (${size}x${size})`);
  
  return filepath;
}

function createAllIcons() {
  console.log('🎨 Creating NeTuArk icons...');
  console.log('📁 Output directory: public/');
  
  const createdFiles = [];
  
  for (const size of ICON_SIZES) {
    try {
      // Create SVG content
      const svgContent = createNeTuArkSVG(size);
      
      // Save as SVG first
      const svgPath = saveIcon(svgContent, size, 'svg');
      createdFiles.push(svgPath);
      
    } catch (error) {
      console.error(`❌ Error creating icon ${size}x${size}:`, error.message);
    }
  }
  
  console.log(`\n🎉 Successfully created ${createdFiles.length} SVG icons!`);
  console.log('\n📋 Next steps:');
  console.log('1. SVG icons created in public/ directory');
  console.log('2. To convert to PNG, use one of these methods:');
  console.log('   • Online converter: https://convertio.co/svg-png/');
  console.log('   • ImageMagick: convert icon-*.svg icon-*.png');
  console.log('   • Use the HTML generator: open generate-png-icons.html');
  console.log('3. Update manifest.json to use PNG icons (already done)');
  console.log('4. Deploy to Netlify');
  
  return createdFiles;
}

function createFavicon() {
  try {
    const faviconSVG = createNeTuArkSVG(32);
    const faviconPath = path.join('public', 'favicon.svg');
    
    fs.writeFileSync(faviconPath, faviconSVG, 'utf8');
    console.log('✅ Created favicon.svg');
    
    return faviconPath;
  } catch (error) {
    console.error('❌ Error creating favicon:', error.message);
    return null;
  }
}

function updateHTMLWithIcons() {
  // Update index.html to include proper icon references
  const indexPath = path.join('index.html');
  
  if (fs.existsSync(indexPath)) {
    try {
      let htmlContent = fs.readFileSync(indexPath, 'utf8');
      
      // Add favicon and icon links
      const iconLinks = `
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="/icon-32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icon-16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/icon-192.png">
    <link rel="mask-icon" href="/favicon.svg" color="${BG_COLOR}">
    <meta name="theme-color" content="${BG_COLOR}">`;
      
      // Insert before closing head tag
      if (htmlContent.includes('</head>')) {
        htmlContent = htmlContent.replace('</head>', `${iconLinks}\n  </head>`);
        fs.writeFileSync(indexPath, htmlContent, 'utf8');
        console.log('✅ Updated index.html with icon references');
      }
    } catch (error) {
      console.error('❌ Error updating index.html:', error.message);
    }
  }
}

function main() {
  console.log('🚀 NeTuArk Icon Generator');
  console.log('=' * 40);
  
  // Create all icons
  const createdFiles = createAllIcons();
  
  // Create favicon
  const favicon = createFavicon();
  if (favicon) {
    createdFiles.push(favicon);
  }
  
  // Update HTML
  updateHTMLWithIcons();
  
  console.log(`\n📊 Summary:`);
  console.log(`   • Created ${createdFiles.length} SVG files`);
  console.log(`   • Icon sizes: ${ICON_SIZES.join(', ')}`);
  console.log(`   • Format: SVG (convert to PNG for PWA)`);
  console.log(`   • Brand colors: ${BG_COLOR} (background), ${TEXT_COLOR} (text)`);
  
  console.log(`\n🔧 Files created:`);
  createdFiles.forEach(file => {
    console.log(`   • ${file}`);
  });
  
  console.log(`\n⚠️  Important: Convert SVG icons to PNG for PWA compatibility`);
  console.log(`   Use the HTML generator: open generate-png-icons.html in browser`);
}

if (require.main === module) {
  main();
}

module.exports = {
  createNeTuArkSVG,
  createAllIcons,
  ICON_SIZES
};
