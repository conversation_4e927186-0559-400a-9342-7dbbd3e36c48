const { ObjectId } = require('mongodb');
const { findOne, updateOne } = require('./utils/db');
const { requireAuth } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Subscribe to NTA+
exports.subscribe = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Parse request body
  const { paymentMethod, paymentToken } = JSON.parse(event.body);
  
  // Validate input
  if (!paymentMethod || !paymentToken) {
    return error('Payment method and token are required');
  }
  
  // In a real implementation, this would process the payment through a payment processor
  // For now, we'll simulate a successful payment
  
  // Calculate subscription expiry (1 month from now)
  const expiryDate = new Date();
  expiryDate.setMonth(expiryDate.getMonth() + 1);
  
  // Update user with premium status
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    {
      $set: {
        isPremium: true,
        premiumExpiry: expiryDate,
        updatedAt: new Date()
      }
    }
  );
  
  // Create subscription record
  const subscription = {
    userId: new ObjectId(userId),
    plan: 'NTA+',
    price: 3.00, // $3 per month
    paymentMethod,
    status: 'active',
    currentPeriodStart: new Date(),
    currentPeriodEnd: expiryDate,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  // In a real implementation, this would be stored in a 'subscriptions' collection
  
  // Return subscription details
  return success({
    subscription: {
      plan: subscription.plan,
      status: subscription.status,
      currentPeriodStart: subscription.currentPeriodStart,
      currentPeriodEnd: subscription.currentPeriodEnd
    }
  });
}));

// Cancel subscription
exports.cancelSubscription = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user to check premium status
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Check if user is premium
  if (!user.isPremium) {
    return error('You do not have an active subscription', 400);
  }
  
  // In a real implementation, this would cancel the subscription through a payment processor
  // For now, we'll simulate a successful cancellation
  
  // Update user to remove premium status at the end of the current period
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    {
      $set: {
        // Keep premium until the end of the current period
        // isPremium will be set to false when premiumExpiry is reached
        subscriptionCancelled: true,
        updatedAt: new Date()
      }
    }
  );
  
  // Return success
  return success({
    message: 'Subscription cancelled successfully',
    activeUntil: user.premiumExpiry
  });
}));

// Get subscription status
exports.getSubscriptionStatus = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user to check premium status
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Return subscription status
  return success({
    isPremium: user.isPremium,
    premiumExpiry: user.premiumExpiry,
    subscriptionCancelled: user.subscriptionCancelled || false
  });
}));

// Reactivate cancelled subscription
exports.reactivateSubscription = handleAsync(requireAuth(async (event) => {
  // Get user ID from token
  const userId = event.user._id;
  
  // Get user to check premium status
  const user = await findOne('users', { _id: new ObjectId(userId) });
  
  // Check if user is premium and has cancelled
  if (!user.isPremium || !user.subscriptionCancelled) {
    return error('You do not have a cancelled subscription to reactivate', 400);
  }
  
  // In a real implementation, this would reactivate the subscription through a payment processor
  // For now, we'll simulate a successful reactivation
  
  // Update user to remove cancellation flag
  await updateOne(
    'users',
    { _id: new ObjectId(userId) },
    {
      $set: {
        subscriptionCancelled: false,
        updatedAt: new Date()
      }
    }
  );
  
  // Return success
  return success({
    message: 'Subscription reactivated successfully',
    activeUntil: user.premiumExpiry
  });
}));

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/subscription', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/subscribe' && event.httpMethod === 'POST':
      return exports.subscribe(event, context);
    case path === '/cancel' && event.httpMethod === 'POST':
      return exports.cancelSubscription(event, context);
    case path === '/status' && event.httpMethod === 'GET':
      return exports.getSubscriptionStatus(event, context);
    case path === '/reactivate' && event.httpMethod === 'POST':
      return exports.reactivateSubscription(event, context);
    default:
      return error('Not found', 404);
  }
};
