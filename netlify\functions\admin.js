const { ObjectId } = require('mongodb');
const { findOne, find, insertOne, updateOne, deleteOne } = require('./utils/db');
const { verifyToken } = require('./utils/auth-utils');
const { success, error, handleAsync } = require('./utils/response');

// Check if user is admin
async function checkAdminAccess(userId) {
  const user = await findOne('users', { _id: new ObjectId(userId) });
  return user && user.isAdmin;
}

exports.handler = async (event, context) => {
  // Enable CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    const db = await connectToDatabase();
    const authHeader = event.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Authorization token required' })
      };
    }

    const token = authHeader.substring(7);
    const decoded = verifyToken(token);

    if (!decoded) {
      return {
        statusCode: 401,
        headers,
        body: JSON.stringify({ error: 'Invalid token' })
      };
    }

    const userId = decoded.userId;

    // Check admin access
    const isAdmin = await checkAdminAccess(db, userId);
    if (!isAdmin) {
      return {
        statusCode: 403,
        headers,
        body: JSON.stringify({ error: 'Admin access required' })
      };
    }

    const { httpMethod, path } = event;

    // GET /admin/dashboard - Get dashboard statistics
    if (httpMethod === 'GET' && path.includes('/dashboard')) {
      // Get basic stats
      const [
        totalUsers,
        totalPosts,
        totalReports,
        activeUsers,
        premiumUsers
      ] = await Promise.all([
        db.collection('users').countDocuments(),
        db.collection('posts').countDocuments(),
        db.collection('reports').countDocuments({ status: 'pending' }),
        db.collection('users').countDocuments({
          lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
        }),
        db.collection('users').countDocuments({ isPremium: true })
      ]);

      // Calculate total revenue (simplified)
      const subscriptions = await db.collection('subscriptions').find({
        status: 'active'
      }).toArray();
      const totalRevenue = subscriptions.length * 3; // $3 per subscription

      // Get recent reports
      const recentReports = await db.collection('reports').aggregate([
        { $match: { status: 'pending' } },
        {
          $lookup: {
            from: 'users',
            localField: 'reportedBy',
            foreignField: '_id',
            as: 'reportedBy'
          }
        },
        {
          $unwind: '$reportedBy'
        },
        {
          $project: {
            'reportedBy.password': 0,
            'reportedBy.email': 0
          }
        },
        { $sort: { createdAt: -1 } },
        { $limit: 10 }
      ]).toArray();

      // Get recent users
      const recentUsers = await db.collection('users').aggregate([
        {
          $project: {
            username: 1,
            profilePicture: 1,
            isPremium: 1,
            createdAt: 1,
            postsCount: { $ifNull: ['$postsCount', 0] },
            followersCount: { $ifNull: ['$followersCount', 0] }
          }
        },
        { $sort: { createdAt: -1 } },
        { $limit: 10 }
      ]).toArray();

      // System health (simplified)
      const systemHealth = {
        database: 'healthy',
        storage: 'healthy',
        api: 'healthy',
        cdn: 'healthy'
      };

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          data: {
            stats: {
              totalUsers,
              totalPosts,
              totalReports,
              activeUsers,
              premiumUsers,
              totalRevenue
            },
            recentReports,
            recentUsers,
            systemHealth
          }
        })
      };
    }

    // PUT /admin/reports/:id - Handle report action
    if (httpMethod === 'PUT' && path.includes('/reports/')) {
      const reportId = path.split('/')[2];
      const { action } = JSON.parse(event.body);

      if (!['approve', 'dismiss', 'escalate'].includes(action)) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
      }

      const report = await db.collection('reports').findOne({
        _id: new ObjectId(reportId)
      });

      if (!report) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'Report not found' })
        };
      }

      // Update report status
      const updateData = {
        status: action === 'approve' ? 'approved' : action === 'dismiss' ? 'dismissed' : 'escalated',
        action,
        reviewedBy: new ObjectId(userId),
        reviewedAt: new Date()
      };

      await db.collection('reports').updateOne(
        { _id: new ObjectId(reportId) },
        { $set: updateData }
      );

      // Take action based on report type and action
      if (action === 'approve') {
        await handleApprovedReport(db, report);
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: `Report ${action}d successfully`
        })
      };
    }

    // GET /admin/users - Get users with admin controls
    if (httpMethod === 'GET' && path.includes('/users')) {
      const { page = 1, limit = 20, search, filter } = event.queryStringParameters || {};
      const skip = (parseInt(page) - 1) * parseInt(limit);

      let query = {};

      if (search) {
        query.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      if (filter === 'premium') {
        query.isPremium = true;
      } else if (filter === 'banned') {
        query.isBanned = true;
      }

      const users = await db.collection('users').aggregate([
        { $match: query },
        {
          $project: {
            password: 0
          }
        },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: parseInt(limit) }
      ]).toArray();

      const totalCount = await db.collection('users').countDocuments(query);
      const hasMore = skip + users.length < totalCount;

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          data: {
            users,
            hasMore,
            page: parseInt(page),
            totalCount
          }
        })
      };
    }

    // PUT /admin/users/:id - Update user (ban, unban, etc.)
    if (httpMethod === 'PUT' && path.includes('/users/')) {
      const targetUserId = path.split('/')[2];
      const { action, reason } = JSON.parse(event.body);

      if (!['ban', 'unban', 'verify', 'unverify'].includes(action)) {
        return {
          statusCode: 400,
          headers,
          body: JSON.stringify({ error: 'Invalid action' })
        };
      }

      const updateData = {};

      if (action === 'ban') {
        updateData.isBanned = true;
        updateData.banReason = reason || 'Violation of community guidelines';
        updateData.bannedAt = new Date();
        updateData.bannedBy = new ObjectId(userId);
      } else if (action === 'unban') {
        updateData.isBanned = false;
        updateData.banReason = null;
        updateData.bannedAt = null;
        updateData.bannedBy = null;
      } else if (action === 'verify') {
        updateData.isVerified = true;
      } else if (action === 'unverify') {
        updateData.isVerified = false;
      }

      const result = await db.collection('users').updateOne(
        { _id: new ObjectId(targetUserId) },
        { $set: updateData }
      );

      if (result.matchedCount === 0) {
        return {
          statusCode: 404,
          headers,
          body: JSON.stringify({ error: 'User not found' })
        };
      }

      return {
        statusCode: 200,
        headers,
        body: JSON.stringify({
          success: true,
          message: `User ${action}ned successfully`
        })
      };
    }

    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' })
    };

  } catch (error) {
    console.error('Admin function error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Internal server error',
        details: error.message
      })
    };
  }
};

// Handle approved reports by taking action on the reported content
async function handleApprovedReport(db, report) {
  try {
    const { targetType, targetId, reason } = report;

    switch (targetType) {
      case 'post':
        // Remove or flag the post
        await db.collection('posts').updateOne(
          { _id: new ObjectId(targetId) },
          {
            $set: {
              isRemoved: true,
              removalReason: reason,
              removedAt: new Date()
            }
          }
        );
        break;

      case 'user':
        // Warn or temporarily restrict the user
        await db.collection('users').updateOne(
          { _id: new ObjectId(targetId) },
          {
            $inc: { warningCount: 1 },
            $set: { lastWarningAt: new Date() }
          }
        );
        break;

      case 'comment':
        // Remove the comment
        await db.collection('comments').updateOne(
          { _id: new ObjectId(targetId) },
          {
            $set: {
              isRemoved: true,
              removalReason: reason,
              removedAt: new Date()
            }
          }
        );
        break;

      default:
        console.log('Unknown target type for report action:', targetType);
    }
  } catch (error) {
    console.error('Error handling approved report:', error);
  }
}
