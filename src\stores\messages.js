import { defineStore } from 'pinia';
import { ref, computed, onUnmounted } from 'vue';
import { useAuthStore } from './auth';

export const useMessagesStore = defineStore('messages', () => {
  // State
  const conversations = ref([]);
  const currentConversation = ref(null);
  const messages = ref([]);
  const loading = ref(false);
  const error = ref(null);
  const page = ref(1);
  const hasMore = ref(true);

  // Real-time polling
  const pollingInterval = ref(null);
  const pollingFrequency = 5000; // 5 seconds
  const lastMessageTimestamp = ref(null);

  // User status
  const userStatuses = ref({});
  const statusPollingInterval = ref(null);

  // Offline sync
  const pendingMessages = ref([]);
  const isOnline = ref(navigator.onLine);
  const syncInProgress = ref(false);

  // Getters
  const allConversations = computed(() => conversations.value);
  const currentMessages = computed(() => messages.value);
  const conversationDetails = computed(() => currentConversation.value);

  // Get user status by ID
  const getUserStatus = computed(() => (userId) => {
    return userStatuses.value[userId] || 'offline';
  });

  // Check if a user is online
  const isUserOnline = computed(() => (userId) => {
    return userStatuses.value[userId] === 'online';
  });

  // Actions
  async function fetchConversations() {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/conversations', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch conversations');
      }

      // Update conversations
      conversations.value = data.data.conversations;

      return data.data.conversations;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function fetchMessages(conversationId, refresh = false) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    loading.value = true;
    error.value = null;

    try {
      // Reset page if refreshing
      if (refresh) {
        page.value = 1;
        messages.value = [];
        hasMore.value = true;
      }

      // Don't fetch if no more messages
      if (!hasMore.value && !refresh) {
        loading.value = false;
        return [];
      }

      const response = await fetch(`/.netlify/functions/messages/messages?conversationId=${conversationId}&page=${page.value}&limit=20`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch messages');
      }

      // Update messages
      if (refresh) {
        messages.value = data.data.messages;
      } else {
        messages.value = [...data.data.messages, ...messages.value];
      }

      // Check if there are more messages
      hasMore.value = data.data.messages.length === 20;

      // Increment page for next fetch
      if (hasMore.value) {
        page.value++;
      }

      return data.data.messages;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function getOrCreateConversation(participantId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ participantId })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create conversation');
      }

      // Set current conversation
      currentConversation.value = data.data.conversation;

      // Reset messages
      messages.value = [];
      page.value = 1;
      hasMore.value = true;

      // Fetch messages for the conversation
      await fetchMessages(data.data.conversation._id, true);

      // Refresh conversations list
      await fetchConversations();

      return data.data.conversation;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  // Initialize offline sync
  function initOfflineSync() {
    // Load pending messages from local storage
    loadPendingMessages();

    // Set up online/offline event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up on component unmount
    onUnmounted(() => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    });
  }

  // Handle going online
  async function handleOnline() {
    isOnline.value = true;
    console.log('Connection restored. Syncing pending messages...');

    // Sync pending messages
    await syncPendingMessages();
  }

  // Handle going offline
  function handleOffline() {
    isOnline.value = false;
    console.log('Connection lost. Messages will be queued for later delivery.');
  }

  // Save pending messages to local storage
  function savePendingMessages() {
    try {
      localStorage.setItem('pendingMessages', JSON.stringify(pendingMessages.value));
    } catch (err) {
      console.error('Failed to save pending messages to local storage:', err);
    }
  }

  // Load pending messages from local storage
  function loadPendingMessages() {
    try {
      const stored = localStorage.getItem('pendingMessages');
      if (stored) {
        pendingMessages.value = JSON.parse(stored);
      }
    } catch (err) {
      console.error('Failed to load pending messages from local storage:', err);
    }
  }

  // Sync pending messages with the server
  async function syncPendingMessages() {
    if (syncInProgress.value || pendingMessages.value.length === 0) return;

    const authStore = useAuthStore();
    if (!authStore.token) return;

    syncInProgress.value = true;

    try {
      // Get pending messages that need to be synced
      const messagesToSync = pendingMessages.value.filter(
        msg => msg.status === 'pending' || msg.status === 'failed'
      );

      if (messagesToSync.length === 0) {
        syncInProgress.value = false;
        return;
      }

      // Send messages to server
      const response = await fetch('/.netlify/functions/messages/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messages: messagesToSync })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync messages');
      }

      // Process results
      for (const result of data.data.results) {
        const pendingIndex = pendingMessages.value.findIndex(
          msg => msg.clientId === result.clientId
        );

        if (pendingIndex !== -1) {
          if (result.success) {
            // Remove from pending messages
            pendingMessages.value.splice(pendingIndex, 1);

            // Update optimistic message in UI
            const messageIndex = messages.value.findIndex(
              msg => msg._id === result.clientId
            );

            if (messageIndex !== -1) {
              messages.value[messageIndex]._id = result.messageId;
              messages.value[messageIndex].isPending = false;
            }
          } else {
            // Mark as failed
            pendingMessages.value[pendingIndex].status = 'failed';
            pendingMessages.value[pendingIndex].error = result.error;
          }
        }
      }

      // Save updated pending messages
      savePendingMessages();
    } catch (err) {
      console.error('Error syncing pending messages:', err);
    } finally {
      syncInProgress.value = false;
    }
  }

  async function sendMessage(content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    if (!currentConversation.value) throw new Error('No active conversation');
    if (!content || content.trim() === '') throw new Error('Message content is required');

    const conversationId = currentConversation.value._id;

    // Check if we're online
    if (!isOnline.value) {
      // Store message for later sync
      const pendingMessage = {
        clientId: `pending-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        content,
        timestamp: new Date().toISOString(),
        status: 'pending'
      };

      // Add to pending messages
      pendingMessages.value.push(pendingMessage);

      // Save to local storage
      savePendingMessages();

      // Add optimistic message to UI
      const optimisticMessage = {
        _id: pendingMessage.clientId,
        conversationId,
        sender: authStore.user,
        content,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isPending: true
      };

      // Add message to the messages array
      messages.value = [...messages.value, optimisticMessage];

      // Update conversation in the conversations array
      const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
      if (conversationIndex !== -1) {
        conversations.value[conversationIndex].lastMessage = {
          _id: optimisticMessage._id,
          sender: optimisticMessage.sender,
          content: optimisticMessage.content,
          createdAt: optimisticMessage.createdAt
        };

        // Move conversation to the top of the list
        const conversation = conversations.value.splice(conversationIndex, 1)[0];
        conversations.value.unshift(conversation);
      }

      return optimisticMessage;
    }

    // We're online, send message normally
    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          conversationId,
          content
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send message');
      }

      // Add new message to the messages array
      messages.value = [...messages.value, data.data.message];

      // Update conversation in the conversations array
      const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
      if (conversationIndex !== -1) {
        conversations.value[conversationIndex].lastMessage = {
          _id: data.data.message._id,
          sender: data.data.message.sender,
          content: data.data.message.content,
          createdAt: data.data.message.createdAt
        };

        // Move conversation to the top of the list
        const conversation = conversations.value.splice(conversationIndex, 1)[0];
        conversations.value.unshift(conversation);
      }

      return data.data.message;
    } catch (err) {
      error.value = err.message;

      // If we failed to send, add to pending messages
      const pendingMessage = {
        clientId: `pending-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        conversationId,
        content,
        timestamp: new Date().toISOString(),
        status: 'failed'
      };

      // Add to pending messages
      pendingMessages.value.push(pendingMessage);

      // Save to local storage
      savePendingMessages();

      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function editMessage(messageId, content) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/edit', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messageId, content })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to edit message');
      }

      // Update message in the messages array
      const messageIndex = messages.value.findIndex(m => m._id === messageId);
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = content;
        messages.value[messageIndex].updatedAt = new Date();
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  async function deleteMessage(messageId) {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');

    loading.value = true;
    error.value = null;

    try {
      const response = await fetch('/.netlify/functions/messages/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ messageId })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete message');
      }

      // Update message in the messages array
      const messageIndex = messages.value.findIndex(m => m._id === messageId);
      if (messageIndex !== -1) {
        messages.value[messageIndex].content = 'This message has been deleted';
        messages.value[messageIndex].isDeleted = true;
        messages.value[messageIndex].updatedAt = new Date();
      }

      return data.data;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }

  function setCurrentConversation(conversation) {
    // Stop any existing polling
    stopRealtimePolling();

    currentConversation.value = conversation;
    messages.value = [];
    page.value = 1;
    hasMore.value = true;

    if (conversation) {
      fetchMessages(conversation._id, true).then(() => {
        // Start real-time polling for new messages
        startRealtimePolling(conversation._id);

        // Start polling for user status
        if (conversation.participant && conversation.participant._id) {
          startStatusPolling([conversation.participant._id]);
        }
      });
    }
  }

  // Start real-time polling for new messages
  function startRealtimePolling(conversationId) {
    // Stop any existing polling
    stopRealtimePolling();

    // Set the last message timestamp
    if (messages.value.length > 0) {
      const latestMessage = [...messages.value].sort((a, b) =>
        new Date(b.createdAt) - new Date(a.createdAt)
      )[0];

      lastMessageTimestamp.value = new Date(latestMessage.createdAt).toISOString();
    } else {
      lastMessageTimestamp.value = new Date().toISOString();
    }

    // Start polling
    pollingInterval.value = setInterval(async () => {
      try {
        await pollNewMessages(conversationId);
      } catch (err) {
        console.error('Error polling for new messages:', err);
      }
    }, pollingFrequency);

    // Clean up on component unmount
    onUnmounted(() => {
      stopRealtimePolling();
    });
  }

  // Stop real-time polling
  function stopRealtimePolling() {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
  }

  // Poll for new messages
  async function pollNewMessages(conversationId) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch(`/.netlify/functions/messages/new-messages?conversationId=${conversationId}&since=${lastMessageTimestamp.value}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to poll for new messages');
      }

      // If there are new messages, add them to the messages array
      if (data.data.messages && data.data.messages.length > 0) {
        // Add new messages
        messages.value = [...messages.value, ...data.data.messages];

        // Update last message timestamp
        const latestMessage = [...data.data.messages].sort((a, b) =>
          new Date(b.createdAt) - new Date(a.createdAt)
        )[0];

        lastMessageTimestamp.value = new Date(latestMessage.createdAt).toISOString();

        // Update conversation in the conversations array
        const conversationIndex = conversations.value.findIndex(c => c._id === conversationId);
        if (conversationIndex !== -1 && latestMessage) {
          conversations.value[conversationIndex].lastMessage = {
            _id: latestMessage._id,
            sender: latestMessage.sender,
            content: latestMessage.content,
            createdAt: latestMessage.createdAt
          };

          // Move conversation to the top of the list
          const conversation = conversations.value.splice(conversationIndex, 1)[0];
          conversations.value.unshift(conversation);
        }
      }
    } catch (err) {
      console.error('Error polling for new messages:', err);
    }
  }

  // Start polling for user status
  function startStatusPolling(userIds) {
    // Stop any existing polling
    stopStatusPolling();

    // Start polling
    statusPollingInterval.value = setInterval(async () => {
      try {
        await pollUserStatus(userIds);
      } catch (err) {
        console.error('Error polling for user status:', err);
      }
    }, 30000); // Poll every 30 seconds

    // Poll immediately
    pollUserStatus(userIds);

    // Clean up on component unmount
    onUnmounted(() => {
      stopStatusPolling();
    });
  }

  // Stop status polling
  function stopStatusPolling() {
    if (statusPollingInterval.value) {
      clearInterval(statusPollingInterval.value);
      statusPollingInterval.value = null;
    }
  }

  // Poll for user status
  async function pollUserStatus(userIds) {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      const response = await fetch('/.netlify/functions/messages/user-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ userIds })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to poll for user status');
      }

      // Update user statuses
      userStatuses.value = { ...userStatuses.value, ...data.data.statuses };
    } catch (err) {
      console.error('Error polling for user status:', err);
    }
  }

  // Update user status (called when user opens the app)
  async function updateUserStatus(status = 'online') {
    const authStore = useAuthStore();
    if (!authStore.token) return;

    try {
      await fetch('/.netlify/functions/messages/update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({ status })
      });
    } catch (err) {
      console.error('Error updating user status:', err);
    }
  }

  // Initialize offline sync when store is created
  initOfflineSync();

  return {
    // State
    conversations,
    currentConversation,
    messages,
    loading,
    error,
    page,
    hasMore,
    userStatuses,
    pendingMessages,
    isOnline,
    syncInProgress,

    // Getters
    allConversations,
    currentMessages,
    conversationDetails,
    getUserStatus,
    isUserOnline,

    // Actions
    fetchConversations,
    fetchMessages,
    getOrCreateConversation,
    sendMessage,
    editMessage,
    deleteMessage,
    setCurrentConversation,
    startRealtimePolling,
    stopRealtimePolling,
    updateUserStatus,
    syncPendingMessages,
    handleOnline,
    handleOffline
  };
});
