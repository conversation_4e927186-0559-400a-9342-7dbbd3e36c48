import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useAuthStore } from './auth';
import { useSubscriptionStore } from './subscription';

export const useDiscoveryStore = defineStore('discovery', () => {
  // State
  const recommendedUsers = ref([]);
  const trendingUsers = ref([]);
  const loading = ref(false);
  const error = ref(null);
  
  // Getters
  const allRecommendedUsers = computed(() => recommendedUsers.value);
  const allTrendingUsers = computed(() => trendingUsers.value);
  
  // Get premium users first (for search results)
  const sortedTrendingUsers = computed(() => {
    return [...trendingUsers.value].sort((a, b) => {
      // Sort by premium status first
      if (a.isPremium && !b.isPremium) return -1;
      if (!a.isPremium && b.isPremium) return 1;
      
      // Then by engagement score
      return b.engagementScore - a.engagementScore;
    });
  });
  
  // Actions
  
  // Fetch recommended users
  async function fetchRecommendedUsers() {
    const authStore = useAuthStore();
    if (!authStore.token) throw new Error('Not authenticated');
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/discovery/recommended', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${authStore.token}`
        }
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch recommended users');
      }
      
      recommendedUsers.value = data.data.users;
      return data.data.users;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Fetch trending users
  async function fetchTrendingUsers() {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch('/.netlify/functions/discovery/trending');
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch trending users');
      }
      
      trendingUsers.value = data.data.users;
      return data.data.users;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  // Search users
  async function searchUsers(query) {
    if (!query || query.trim() === '') {
      return [];
    }
    
    loading.value = true;
    error.value = null;
    
    try {
      const response = await fetch(`/.netlify/functions/discovery/search?q=${encodeURIComponent(query)}`);
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to search users');
      }
      
      // Sort results with premium users first
      const subscriptionStore = useSubscriptionStore();
      const sortedResults = [...data.data.users].sort((a, b) => {
        // Current user's subscription status affects search results
        if (subscriptionStore.isPremium) {
          // Premium users see more relevant results regardless of other users' status
          return 0;
        } else {
          // Non-premium users see premium users first
          if (a.isPremium && !b.isPremium) return -1;
          if (!a.isPremium && b.isPremium) return 1;
          return 0;
        }
      });
      
      return sortedResults;
    } catch (err) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  }
  
  return {
    // State
    recommendedUsers,
    trendingUsers,
    loading,
    error,
    
    // Getters
    allRecommendedUsers,
    allTrendingUsers,
    sortedTrendingUsers,
    
    // Actions
    fetchRecommendedUsers,
    fetchTrendingUsers,
    searchUsers
  };
});
