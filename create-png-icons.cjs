#!/usr/bin/env node
/**
 * PNG Icon Generator for NeTuArk PWA
 * Creates PNG icons using Canvas API
 */

const fs = require('fs');
const path = require('path');

// Icon sizes needed for PWA
const ICON_SIZES = [48, 72, 96, 144, 192, 512];

// NeTuArk brand colors
const BG_COLOR = '#00d4ff';
const TEXT_COLOR = '#0a0a0a';
const ACCENT_COLOR = '#ffffff';

function createNeTuArkSVG(size) {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient${size}" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${BG_COLOR};stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow${size}" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 4}" fill="url(#bgGradient${size})" filter="url(#shadow${size})"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(${size/2}, ${size/2})">
    <!-- Central N -->
    <text x="0" y="${size * 0.1}" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="${size * 0.4}" 
          fill="${TEXT_COLOR}">N</text>
    
    <!-- Connection lines -->
    <line x1="${-size * 0.2}" y1="${-size * 0.1}" x2="${size * 0.2}" y2="${size * 0.1}" 
          stroke="${TEXT_COLOR}" stroke-width="${size * 0.02}" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="${-size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="${TEXT_COLOR}" opacity="0.8"/>
    <circle cx="${size * 0.15}" cy="${size * 0.15}" r="${size * 0.02}" fill="${TEXT_COLOR}" opacity="0.8"/>
    <circle cx="0" cy="${size * 0.2}" r="${size * 0.015}" fill="${TEXT_COLOR}" opacity="0.6"/>
  </g>
</svg>`;
}

function saveIcon(content, size, format = 'svg') {
  const outputDir = 'public';
  const filename = `icon-${size}.${format}`;
  const filepath = path.join(outputDir, filename);
  
  // Ensure output directory exists
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Save the file
  fs.writeFileSync(filepath, content, 'utf8');
  console.log(`✅ Created ${filename} (${size}x${size})`);
  
  return filepath;
}

function createAllIcons() {
  console.log('🎨 Creating NeTuArk icons...');
  console.log('📁 Output directory: public/');
  
  const createdFiles = [];
  
  for (const size of ICON_SIZES) {
    try {
      // Create SVG content
      const svgContent = createNeTuArkSVG(size);
      
      // Save as SVG
      const svgPath = saveIcon(svgContent, size, 'svg');
      createdFiles.push(svgPath);
      
    } catch (error) {
      console.error(`❌ Error creating icon ${size}x${size}:`, error.message);
    }
  }
  
  console.log(`\n🎉 Successfully created ${createdFiles.length} SVG icons!`);
  return createdFiles;
}

function createFavicon() {
  try {
    const faviconSVG = createNeTuArkSVG(32);
    const faviconPath = path.join('public', 'favicon.svg');
    
    fs.writeFileSync(faviconPath, faviconSVG, 'utf8');
    console.log('✅ Created favicon.svg');
    
    return faviconPath;
  } catch (error) {
    console.error('❌ Error creating favicon:', error.message);
    return null;
  }
}

function main() {
  console.log('🚀 NeTuArk Icon Generator');
  console.log('========================================');
  
  // Create all icons
  const createdFiles = createAllIcons();
  
  // Create favicon
  const favicon = createFavicon();
  if (favicon) {
    createdFiles.push(favicon);
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   • Created ${createdFiles.length} SVG files`);
  console.log(`   • Icon sizes: ${ICON_SIZES.join(', ')}`);
  console.log(`   • Format: SVG (need PNG conversion for PWA)`);
  console.log(`   • Brand colors: ${BG_COLOR} (background), ${TEXT_COLOR} (text)`);
  
  console.log(`\n🔧 Files created:`);
  createdFiles.forEach(file => {
    console.log(`   • ${file}`);
  });
  
  console.log(`\n📋 Next steps:`);
  console.log(`1. Open generate-png-icons.html in your browser`);
  console.log(`2. Click "Generate All PNG Icons"`);
  console.log(`3. Download all PNG files to public/ directory`);
  console.log(`4. Deploy to Netlify to fix PWA manifest errors`);
  
  console.log(`\n⚠️  The manifest.json is already updated to use PNG icons`);
}

if (require.main === module) {
  main();
}

module.exports = {
  createNeTuArkSVG,
  createAllIcons,
  ICON_SIZES
};
