import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useToastStore = defineStore('toast', () => {
  // State
  const toasts = ref([]);
  let nextId = 1;

  // Actions
  const addToast = (toast) => {
    const id = nextId++;
    const newToast = {
      id,
      type: toast.type || 'info', // success, error, warning, info
      title: toast.title || '',
      message: toast.message || '',
      duration: toast.duration || 5000,
      persistent: toast.persistent || false,
      actions: toast.actions || []
    };

    toasts.value.push(newToast);

    // Auto-remove non-persistent toasts
    if (!newToast.persistent && newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, newToast.duration);
    }

    return id;
  };

  const removeToast = (id) => {
    const index = toasts.value.findIndex(t => t.id === id);
    if (index > -1) {
      toasts.value.splice(index, 1);
    }
  };

  const clearAll = () => {
    toasts.value = [];
  };

  // Convenience methods
  const success = (message, options = {}) => {
    return addToast({
      type: 'success',
      message,
      ...options
    });
  };

  const error = (message, options = {}) => {
    return addToast({
      type: 'error',
      message,
      duration: 8000, // Longer duration for errors
      ...options
    });
  };

  const warning = (message, options = {}) => {
    return addToast({
      type: 'warning',
      message,
      ...options
    });
  };

  const info = (message, options = {}) => {
    return addToast({
      type: 'info',
      message,
      ...options
    });
  };

  return {
    // State
    toasts,

    // Actions
    addToast,
    removeToast,
    clearAll,

    // Convenience methods
    success,
    error,
    warning,
    info
  };
});
