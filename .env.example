# NeTuArk Environment Variables Example
# Copy this file to .env and fill in your values

# Node environment (development, production)
NODE_ENV=production

# MongoDB configuration
# Replace with your MongoDB Atlas connection string
MONGODB_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=netuark
DB_NAME=netuark

# JWT configuration
# Generate a strong random string for production
JWT_SECRET=replace-with-a-strong-random-string

# Backblaze B2 configuration
B2_BUCKET_NAME=netuark-media
B2_ENDPOINT=https://s3.us-west-004.backblazeb2.com
B2_KEY_ID=your-backblaze-key-id
B2_APP_KEY=your-backblaze-application-key

# API rate limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=60000

# Premium subscription settings
PREMIUM_PRICE_MONTHLY=3
PREMIUM_PRICE_YEARLY=30

# Logging level (debug, info, warn, error)
LOG_LEVEL=info

# AI Integration (placeholder for now)
AI_API_KEY=your-ai-api-key-here
