<template>
  <div class="create-post">
    <div class="create-post-header">
      <h2>Create Post</h2>
    </div>

    <div class="create-post-form">
      <div class="content-input">
        <textarea
          v-model="content"
          placeholder="What's on your mind?"
          rows="4"
          :maxlength="maxContentLength"
        ></textarea>
        <div class="character-count">
          {{ content.length }}/{{ maxContentLength }}
        </div>
      </div>

      <div class="media-preview" v-if="mediaFiles.length > 0">
        <div v-for="(file, index) in mediaFiles" :key="index" class="media-item">
          <div class="media-container">
            <img v-if="file.type.startsWith('image/')" :src="file.preview" alt="Media preview" class="media-image" />
            <video v-else-if="file.type.startsWith('video/')" :src="file.preview" controls class="media-video"></video>
          </div>
          <button @click="removeMedia(index)" class="remove-media-btn">
            <i class="fas fa-times"></i>
          </button>

          <div v-if="file.type.startsWith('image/')" class="image-tools">
            <button @click="cropImage(index)" class="tool-btn">
              <i class="fas fa-crop"></i> Crop
            </button>
            <button @click="applyFilter(index)" class="tool-btn">
              <i class="fas fa-magic"></i> Filter
            </button>
          </div>
        </div>
      </div>

      <div class="post-options">
        <div class="media-upload">
          <label for="media-upload" class="media-upload-label" :class="{ 'uploading': isUploading }">
            <i class="fas fa-image" v-if="!isUploading"></i>
            <i class="fas fa-spinner fa-spin" v-if="isUploading"></i>
            <span v-if="!isUploading">Photo/Video</span>
            <span v-if="isUploading">Uploading...</span>
          </label>
          <input
            type="file"
            id="media-upload"
            multiple
            accept="image/jpeg,image/png,image/gif,image/webp,video/mp4,video/quicktime,video/webm"
            @change="handleMediaUpload"
            class="media-upload-input"
            :disabled="isUploading || mediaFiles.length >= maxMediaFiles"
          />
        </div>

        <div class="media-info" v-if="mediaFiles.length > 0">
          <span class="media-count">{{ mediaFiles.length }}/{{ maxMediaFiles }} files</span>
          <span class="media-size">{{ formatFileSize(totalMediaSize) }}</span>
        </div>

        <div class="visibility-selector">
          <select v-model="visibility">
            <option value="public">🌍 Public</option>
            <option value="followers">👥 Followers Only</option>
            <option value="private">🔒 Private</option>
          </select>
        </div>
      </div>

      <div v-if="error" class="error-message">
        {{ error }}
      </div>

      <div class="post-actions">
        <button
          @click="createPost"
          class="create-post-btn"
          :disabled="isSubmitting || !isValid"
        >
          <span v-if="isSubmitting">
            <i class="fas fa-spinner fa-spin"></i> Posting...
          </span>
          <span v-else>Post</span>
        </button>
      </div>
    </div>

    <!-- Image Cropper Modal -->
    <div v-if="showCropper" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Crop Image</h3>
          <button @click="cancelCrop" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="cropper-container">
            <!-- Image cropper component would go here -->
            <img :src="cropperImage" alt="Crop preview" class="cropper-image" />
          </div>
        </div>
        <div class="modal-footer">
          <button @click="cancelCrop" class="cancel-btn">Cancel</button>
          <button @click="saveCrop" class="save-btn">Save Crop</button>
        </div>
      </div>
    </div>

    <!-- Filter Modal -->
    <div v-if="showFilters" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Apply Filter</h3>
          <button @click="cancelFilter" class="close-modal-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="filters-container">
            <div v-for="filter in availableFilters" :key="filter.name" class="filter-option">
              <div class="filter-preview" @click="selectFilter(filter)">
                <img :src="filterPreviewImage" :class="filter.class" alt="Filter preview" />
                <div class="filter-name">{{ filter.name }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="cancelFilter" class="cancel-btn">Cancel</button>
          <button @click="saveFilter" class="save-btn">Apply Filter</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { usePostsStore } from '@/stores/posts';
import { useAIAssistantStore } from '@/stores/ai-assistant';

// Stores
const postsStore = usePostsStore();
const aiAssistantStore = useAIAssistantStore();

// State
const content = ref('');
const mediaFiles = ref([]);
const visibility = ref('public');
const isSubmitting = ref(false);
const isUploading = ref(false);
const error = ref(null);
const maxContentLength = 500;
const maxMediaFiles = 10;
const maxFileSize = 10 * 1024 * 1024; // 10MB

// Image editing
const showCropper = ref(false);
const showFilters = ref(false);
const currentEditingIndex = ref(-1);
const cropperImage = ref('');
const filterPreviewImage = ref('');
const selectedFilter = ref(null);

// Available filters
const availableFilters = [
  { name: 'Normal', class: 'filter-normal' },
  { name: 'Grayscale', class: 'filter-grayscale' },
  { name: 'Sepia', class: 'filter-sepia' },
  { name: 'Vintage', class: 'filter-vintage' },
  { name: 'Bright', class: 'filter-bright' },
  { name: 'Contrast', class: 'filter-contrast' }
];

// Computed
const remainingCharacters = computed(() => {
  return maxContentLength - content.value.length;
});

const isValid = computed(() => {
  return content.value.trim().length > 0 || mediaFiles.value.length > 0;
});

const totalMediaSize = computed(() => {
  return mediaFiles.value.reduce((total, media) => total + (media.file?.size || 0), 0);
});

const canUploadMore = computed(() => {
  return mediaFiles.value.length < maxMediaFiles && !isUploading.value;
});

// Methods
async function handleMediaUpload(event) {
  const files = event.target.files;

  if (!files || files.length === 0) return;

  // Check if we can upload more files
  if (mediaFiles.value.length >= maxMediaFiles) {
    error.value = `Maximum ${maxMediaFiles} files allowed`;
    return;
  }

  isUploading.value = true;
  error.value = null;

  try {
    // Process each file
    for (let i = 0; i < files.length && mediaFiles.value.length < maxMediaFiles; i++) {
      const file = files[i];

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/webm'];
      if (!allowedTypes.includes(file.type)) {
        error.value = `Unsupported file type: ${file.type}. Allowed types: JPEG, PNG, GIF, WebP, MP4, QuickTime, WebM`;
        continue;
      }

      // Check file size
      if (file.size > maxFileSize) {
        error.value = `File "${file.name}" exceeds ${formatFileSize(maxFileSize)} size limit`;
        continue;
      }

      // Check total size
      if (totalMediaSize.value + file.size > maxFileSize * maxMediaFiles) {
        error.value = 'Total file size limit exceeded';
        break;
      }

      // Create preview
      const preview = await createFilePreview(file);

      mediaFiles.value.push({
        file,
        preview,
        type: file.type,
        filter: 'normal',
        name: file.name,
        size: file.size
      });
    }
  } catch (err) {
    error.value = err.message || 'Failed to process files';
  } finally {
    isUploading.value = false;
    // Reset input
    event.target.value = '';
  }
}

function createFilePreview(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeMedia(index) {
  mediaFiles.value.splice(index, 1);
}

function cropImage(index) {
  currentEditingIndex.value = index;
  cropperImage.value = mediaFiles.value[index].preview;
  showCropper.value = true;
}

function cancelCrop() {
  showCropper.value = false;
  currentEditingIndex.value = -1;
}

function saveCrop() {
  // In a real implementation, this would apply the crop
  // For now, we'll just close the modal
  showCropper.value = false;
  currentEditingIndex.value = -1;
}

function applyFilter(index) {
  currentEditingIndex.value = index;
  filterPreviewImage.value = mediaFiles.value[index].preview;
  selectedFilter.value = null;
  showFilters.value = true;
}

function selectFilter(filter) {
  selectedFilter.value = filter;
}

function cancelFilter() {
  showFilters.value = false;
  currentEditingIndex.value = -1;
  selectedFilter.value = null;
}

function saveFilter() {
  if (selectedFilter.value && currentEditingIndex.value >= 0) {
    mediaFiles.value[currentEditingIndex.value].filter = selectedFilter.value.name.toLowerCase();
  }

  showFilters.value = false;
  currentEditingIndex.value = -1;
}

async function createPost() {
  if (!isValid.value || isSubmitting.value) return;

  isSubmitting.value = true;
  error.value = null;

  try {
    // Prepare media files
    const mediaData = [];

    for (const media of mediaFiles.value) {
      mediaData.push({
        data: media.preview,
        type: media.type,
        filter: media.filter
      });
    }

    // Create post
    const post = await postsStore.createPost({
      content: content.value,
      media: mediaData,
      visibility: visibility.value
    });

    // Generate AI tags
    if (post && post._id) {
      try {
        await aiAssistantStore.tagContent(post._id, content.value, mediaData);
      } catch (tagError) {
        console.error('Failed to generate AI tags:', tagError);
        // Don't fail the post creation if tagging fails
      }
    }

    // Reset form
    content.value = '';
    mediaFiles.value = [];
    visibility.value = 'public';

    // Show success message or redirect
    // This would typically be handled by the parent component
  } catch (err) {
    error.value = err.message || 'Failed to create post';
  } finally {
    isSubmitting.value = false;
  }
}
</script>

<style scoped>
.create-post {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
  margin-bottom: 1rem;
}

.create-post-header {
  margin-bottom: 1rem;
}

.create-post-header h2 {
  font-size: 1.2rem;
  margin: 0;
}

.content-input {
  position: relative;
  margin-bottom: 1rem;
}

textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 1rem;
}

.character-count {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  font-size: 0.8rem;
  color: #777;
}

.media-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.media-item {
  position: relative;
  width: calc(33.333% - 0.5rem);
}

.media-container {
  aspect-ratio: 1/1;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f0f0f0;
}

.media-image, .media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.remove-media-btn {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.image-tools {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
}

.tool-btn {
  background-color: #f0f0f0;
  border: none;
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  cursor: pointer;
}

.post-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.media-upload {
  position: relative;
}

.media-upload-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
}

.media-upload-label:hover {
  background-color: #f0f0f0;
}

.media-upload-label.uploading {
  background-color: #e3f2fd;
  color: #1976d2;
  cursor: not-allowed;
}

.media-upload-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.media-upload-input:disabled + .media-upload-label {
  opacity: 0.6;
  cursor: not-allowed;
}

.media-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #666;
}

.media-count {
  font-weight: 500;
}

.media-size {
  color: #888;
}

.visibility-selector select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.error-message {
  color: #e74c3c;
  margin-bottom: 1rem;
}

.post-actions {
  display: flex;
  justify-content: flex-end;
}

.create-post-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  cursor: pointer;
}

.create-post-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.create-post-btn:not(:disabled):hover {
  background-color: #2980b9;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  padding: 1rem;
  border-top: 1px solid #eee;
}

.cancel-btn, .save-btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

.save-btn {
  background-color: #3498db;
  color: white;
  border: none;
}

.save-btn:hover {
  background-color: #2980b9;
}

/* Filter styles */
.filters-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.filter-option {
  cursor: pointer;
}

.filter-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.filter-preview img {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.filter-name {
  font-size: 0.8rem;
  text-align: center;
}

/* Filter classes */
.filter-normal {
  filter: none;
}

.filter-grayscale {
  filter: grayscale(100%);
}

.filter-sepia {
  filter: sepia(100%);
}

.filter-vintage {
  filter: sepia(30%) saturate(70%) brightness(90%);
}

.filter-bright {
  filter: brightness(130%);
}

.filter-contrast {
  filter: contrast(150%);
}
</style>
