const { success, error, handleAsync } = require('./utils/response');
const { find, findOne, updateOne } = require('./utils/db');
const { ObjectId } = require('mongodb');

// Get feed ads
exports.getFeedAds = handleAsync(async () => {
  // Get active feed ads
  const ads = await find(
    'ads',
    {
      type: 'feed',
      isActive: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    },
    {
      sort: { priority: -1 }
    }
  );
  
  // Return ads
  return success({ ads });
});

// Get story ads
exports.getStoryAds = handleAsync(async () => {
  // Get active story ads
  const ads = await find(
    'ads',
    {
      type: 'story',
      isActive: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    },
    {
      sort: { priority: -1 }
    }
  );
  
  // Return ads
  return success({ ads });
});

// Get carousel ads
exports.getCarouselAds = handleAsync(async () => {
  // Get active carousel ads
  const ads = await find(
    'ads',
    {
      type: 'carousel',
      isActive: true,
      startDate: { $lte: new Date() },
      endDate: { $gte: new Date() }
    },
    {
      sort: { priority: -1 }
    }
  );
  
  // Return ads
  return success({ ads });
});

// Track ad impression
exports.trackImpression = handleAsync(async (event) => {
  // Parse request body
  const { adId, adType } = JSON.parse(event.body);
  
  // Validate input
  if (!adId || !adType) {
    return error('Ad ID and type are required');
  }
  
  // Check if ad exists
  const ad = await findOne('ads', { _id: new ObjectId(adId), type: adType });
  
  if (!ad) {
    return error('Ad not found', 404);
  }
  
  // Increment impression count
  await updateOne(
    'ads',
    { _id: new ObjectId(adId) },
    { $inc: { impressions: 1 } }
  );
  
  // Return success
  return success({ message: 'Impression tracked successfully' });
});

// Track ad click
exports.trackClick = handleAsync(async (event) => {
  // Parse request body
  const { adId, adType } = JSON.parse(event.body);
  
  // Validate input
  if (!adId || !adType) {
    return error('Ad ID and type are required');
  }
  
  // Check if ad exists
  const ad = await findOne('ads', { _id: new ObjectId(adId), type: adType });
  
  if (!ad) {
    return error('Ad not found', 404);
  }
  
  // Increment click count
  await updateOne(
    'ads',
    { _id: new ObjectId(adId) },
    { $inc: { clicks: 1 } }
  );
  
  // Return success
  return success({ message: 'Click tracked successfully' });
});

// Create dummy ads for testing
exports.createDummyAds = handleAsync(async () => {
  // Check if we already have ads
  const adCount = await find('ads', {}, { count: true });
  
  if (adCount > 0) {
    return success({ message: 'Ads already exist' });
  }
  
  // Create dummy feed ads
  const feedAds = [
    {
      title: 'Premium NeTuArk Subscription',
      description: 'Upgrade to NTA+ for an ad-free experience and exclusive features!',
      imageUrl: 'https://example.com/ads/premium.jpg',
      linkUrl: '/subscription',
      type: 'feed',
      priority: 10,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
    },
    {
      title: 'Discover New Friends',
      description: 'Find people with similar interests on NeTuArk!',
      imageUrl: 'https://example.com/ads/discover.jpg',
      linkUrl: '/discover',
      type: 'feed',
      priority: 8,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    },
    {
      title: 'Create Your First Story',
      description: 'Share your moments with friends in a story!',
      imageUrl: 'https://example.com/ads/story.jpg',
      linkUrl: '/stories/create',
      type: 'feed',
      priority: 6,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    }
  ];
  
  // Create dummy story ads
  const storyAds = [
    {
      title: 'Premium NeTuArk Subscription',
      description: 'Upgrade to NTA+ for an ad-free experience!',
      imageUrl: 'https://example.com/ads/premium-story.jpg',
      linkUrl: '/subscription',
      type: 'story',
      priority: 10,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    },
    {
      title: 'Create Custom Emojis',
      description: 'Express yourself with custom emojis!',
      imageUrl: 'https://example.com/ads/emoji.jpg',
      linkUrl: '/emoji/create',
      type: 'story',
      priority: 8,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    }
  ];
  
  // Create dummy carousel ads
  const carouselAds = [
    {
      title: 'Premium NeTuArk Subscription',
      description: 'Upgrade to NTA+ for an ad-free experience!',
      imageUrl: 'https://example.com/ads/premium-carousel.jpg',
      linkUrl: '/subscription',
      type: 'carousel',
      priority: 10,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    },
    {
      title: 'Discover Trending Users',
      description: 'See who\'s popular on NeTuArk!',
      imageUrl: 'https://example.com/ads/trending.jpg',
      linkUrl: '/discover/trending',
      type: 'carousel',
      priority: 8,
      impressions: 0,
      clicks: 0,
      isActive: true,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    }
  ];
  
  // Insert all ads
  const allAds = [...feedAds, ...storyAds, ...carouselAds];
  
  for (const ad of allAds) {
    await updateOne(
      'ads',
      { title: ad.title, type: ad.type },
      { $set: ad },
      { upsert: true }
    );
  }
  
  // Return success
  return success({ message: 'Dummy ads created successfully' });
});

// Handler for Netlify Functions
exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/ads', '');
  
  // Route to the appropriate function based on the path
  switch (true) {
    case path === '/feed' && event.httpMethod === 'GET':
      return exports.getFeedAds(event, context);
    case path === '/story' && event.httpMethod === 'GET':
      return exports.getStoryAds(event, context);
    case path === '/carousel' && event.httpMethod === 'GET':
      return exports.getCarouselAds(event, context);
    case path === '/impression' && event.httpMethod === 'POST':
      return exports.trackImpression(event, context);
    case path === '/click' && event.httpMethod === 'POST':
      return exports.trackClick(event, context);
    case path === '/create-dummy' && event.httpMethod === 'POST':
      return exports.createDummyAds(event, context);
    default:
      return error('Not found', 404);
  }
};
