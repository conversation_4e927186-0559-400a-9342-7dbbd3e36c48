const { connectToDatabase, findOne } = require('./utils/db');
const { success, error } = require('./utils/response');

exports.handler = async (event, context) => {
  try {
    console.log('Testing database connection...');
    console.log('Environment variables check:');
    console.log('- NODE_ENV:', process.env.NODE_ENV);
    console.log('- MONGODB_URI exists:', !!process.env.MONGODB_URI);
    console.log('- DB_NAME:', process.env.DB_NAME);
    console.log('- JWT_SECRET exists:', !!process.env.JWT_SECRET);

    // Test database connection
    const db = await connectToDatabase();
    console.log('Database connection successful');

    // Test a simple query
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(col => col.name);
    console.log('Collections found:', collectionNames);

    // Get user count
    const userCount = await db.collection('users').countDocuments();
    console.log('User count:', userCount);

    // Get post count
    const postCount = await db.collection('posts').countDocuments();
    console.log('Post count:', postCount);

    // Test finding a specific user
    let testUser = null;
    try {
      testUser = await findOne('users', { username: 'SohamThePal' });
      console.log('SohamThePal user found:', !!testUser);
    } catch (findError) {
      console.log('Error finding SohamThePal:', findError.message);
    }

    return success({
      message: 'Database connection successful!',
      database: db.databaseName,
      collections: collectionNames,
      stats: {
        users: userCount,
        posts: postCount
      },
      testUser: testUser ? {
        username: testUser.username,
        displayName: testUser.displayName,
        hasProfilePicture: !!testUser.profilePicture,
        isPremium: testUser.isPremium,
        isVerified: testUser.isVerified
      } : null,
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasMongoUri: !!process.env.MONGODB_URI,
        dbName: process.env.DB_NAME,
        hasJwtSecret: !!process.env.JWT_SECRET
      },
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error('Database test failed:', err);
    console.error('Error stack:', err.stack);
    return error(`Database connection failed: ${err.message}`, 500);
  }
};
