<svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient48" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0099cc;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow48" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="24" cy="24" r="20" fill="url(#bgGradient48)" filter="url(#shadow48)"/>
  
  <!-- NeTuArk logo elements -->
  <g transform="translate(24, 24)">
    <!-- Central N -->
    <text x="0" y="4.800000000000001" text-anchor="middle" 
          font-family="Arial, sans-serif" 
          font-weight="bold" 
          font-size="19.200000000000003" 
          fill="#0a0a0a">N</text>
    
    <!-- Connection lines -->
    <line x1="-9.600000000000001" y1="-4.800000000000001" x2="9.600000000000001" y2="4.800000000000001" 
          stroke="#0a0a0a" stroke-width="0.96" opacity="0.7"/>
    
    <!-- Small dots -->
    <circle cx="-7.199999999999999" cy="7.199999999999999" r="0.96" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="7.199999999999999" cy="7.199999999999999" r="0.96" fill="#0a0a0a" opacity="0.8"/>
    <circle cx="0" cy="9.600000000000001" r="0.72" fill="#0a0a0a" opacity="0.6"/>
  </g>
</svg>