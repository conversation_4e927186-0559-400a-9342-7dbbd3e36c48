const { B2 } = require('backblaze-b2');

// Cache B2 authorization between function invocations
let cachedB2 = null;
let cachedAuth = null;

async function getAuthorizedB2() {
  console.log('Storage: Getting B2 authorization...');

  // Return cached authorization if available and not expired
  if (cachedB2 && cachedAuth) {
    console.log('Storage: Using cached B2 authorization');
    return cachedB2;
  }

  // Validate environment variables
  if (!process.env.B2_KEY_ID) {
    throw new Error('B2_KEY_ID environment variable is not set');
  }

  if (!process.env.B2_APP_KEY) {
    throw new Error('B2_APP_KEY environment variable is not set');
  }

  console.log('Storage: Initializing B2 client...');

  // Initialize B2 client
  const b2 = new B2({
    applicationKeyId: process.env.B2_KEY_ID,
    applicationKey: process.env.B2_APP_KEY
  });

  try {
    console.log('Storage: Authorizing with B2...');

    // Authorize with B2
    const auth = await b2.authorize();

    if (!auth || !auth.data) {
      throw new Error('Invalid authorization response from B2');
    }

    console.log('Storage: B2 authorization successful');

    // Cache the authorized client and auth data
    cachedB2 = b2;
    cachedAuth = auth;

    return b2;
  } catch (error) {
    console.error('Storage: B2 authorization error:', {
      message: error.message,
      stack: error.stack,
      keyId: process.env.B2_KEY_ID ? 'SET' : 'NOT_SET',
      appKey: process.env.B2_APP_KEY ? 'SET' : 'NOT_SET'
    });

    // Clear cache on error
    cachedB2 = null;
    cachedAuth = null;

    throw new Error(`B2 authorization failed: ${error.message}`);
  }
}

// Get upload URL and authorization token
async function getUploadUrl() {
  console.log('Storage: Getting upload URL...');

  // Validate bucket ID
  if (!process.env.B2_BUCKET_ID) {
    throw new Error('B2_BUCKET_ID environment variable is not set');
  }

  const b2 = await getAuthorizedB2();

  try {
    console.log(`Storage: Requesting upload URL for bucket ${process.env.B2_BUCKET_ID}...`);

    const response = await b2.getUploadUrl({
      bucketId: process.env.B2_BUCKET_ID
    });

    if (!response || !response.data || !response.data.uploadUrl || !response.data.authorizationToken) {
      throw new Error('Invalid upload URL response from B2');
    }

    console.log('Storage: Upload URL obtained successfully');

    return {
      uploadUrl: response.data.uploadUrl,
      authToken: response.data.authorizationToken
    };
  } catch (error) {
    console.error('Storage: Error getting upload URL:', {
      message: error.message,
      stack: error.stack,
      bucketId: process.env.B2_BUCKET_ID ? 'SET' : 'NOT_SET'
    });

    throw new Error(`Failed to get upload URL: ${error.message}`);
  }
}

// Upload file to B2
async function uploadFile(fileBuffer, fileName, contentType) {
  console.log(`Storage: Starting upload for ${fileName}, size: ${fileBuffer.length}, type: ${contentType}`);

  try {
    // Validate inputs
    if (!fileBuffer || fileBuffer.length === 0) {
      throw new Error('File buffer is empty or invalid');
    }

    if (!fileName || typeof fileName !== 'string') {
      throw new Error('File name is required and must be a string');
    }

    if (!contentType || typeof contentType !== 'string') {
      throw new Error('Content type is required and must be a string');
    }

    // Check environment variables
    if (!process.env.B2_BUCKET_NAME) {
      throw new Error('B2_BUCKET_NAME environment variable is not set');
    }

    console.log('Storage: Getting B2 authorization...');
    const b2 = await getAuthorizedB2();

    console.log('Storage: Getting upload URL...');
    const { uploadUrl, authToken } = await getUploadUrl();

    console.log('Storage: Uploading file to B2...');
    const response = await b2.uploadFile({
      uploadUrl: uploadUrl,
      uploadAuthToken: authToken,
      fileName: fileName,
      data: fileBuffer,
      contentType: contentType
    });

    if (!response || !response.data) {
      throw new Error('Invalid response from B2 upload');
    }

    // For private buckets, use our media proxy endpoint
    console.log('Storage: Generating proxy URL for private bucket...');

    // Use our Netlify function as a proxy to serve files from private bucket
    const baseUrl = process.env.URL || 'https://netuark.netlify.app';
    const fileUrl = `${baseUrl}/.netlify/functions/media-proxy?file=${encodeURIComponent(response.data.fileName)}`;

    console.log(`Storage: Upload successful - ${fileUrl}`);

    return {
      fileId: response.data.fileId,
      fileName: response.data.fileName,
      fileUrl: fileUrl,
      bucketId: process.env.B2_BUCKET_ID
    };
  } catch (error) {
    console.error('Storage: Upload error details:', {
      message: error.message,
      stack: error.stack,
      fileName: fileName,
      fileSize: fileBuffer?.length,
      contentType: contentType
    });

    // Re-throw with more context
    throw new Error(`File upload failed: ${error.message}`);
  }
}

// Delete file from B2
async function deleteFile(fileName) {
  const b2 = await getAuthorizedB2();

  try {
    // First, get the file info to get the fileId
    const fileInfo = await b2.listFileNames({
      bucketId: process.env.B2_BUCKET_ID,
      startFileName: fileName,
      maxFileCount: 1
    });

    if (fileInfo.data.files.length === 0) {
      throw new Error('File not found');
    }

    const fileId = fileInfo.data.files[0].fileId;

    // Delete the file
    await b2.deleteFileVersion({
      fileId: fileId,
      fileName: fileName
    });

    return { success: true };
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}

module.exports = {
  getAuthorizedB2,
  getUploadUrl,
  uploadFile,
  deleteFile
};
