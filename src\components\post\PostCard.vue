<template>
  <div class="post-card">
    <div class="post-header">
      <div class="author-info">
        <router-link :to="`/profile/${post.author.username}`" class="author-link">
          <img :src="post.author.profilePicture || defaultAvatar" :alt="post.author.username" class="author-avatar" />
        </router-link>
        <div class="author-details">
          <router-link :to="`/profile/${post.author.username}`" class="author-name">
            {{ post.author.username }}
            <i v-if="post.author.isPremium" class="fas fa-crown premium-badge" title="NTA+ Premium User"></i>
          </router-link>
          <div class="post-time">{{ formatTime(post.createdAt) }}</div>
        </div>
      </div>

      <div class="post-actions">
        <button @click="showOptionsMenu = !showOptionsMenu" class="options-btn">
          <i class="fas fa-ellipsis-h"></i>
        </button>

        <div v-if="showOptionsMenu" class="options-menu" @click.stop>
          <button v-if="isOwnPost" @click="editPost" class="option-item">
            <i class="fas fa-edit"></i> Edit
          </button>
          <button v-if="isOwnPost" @click="deletePost" class="option-item delete">
            <i class="fas fa-trash"></i> Delete
          </button>
          <button v-if="!isOwnPost" @click="reportPost" class="option-item">
            <i class="fas fa-flag"></i> Report
          </button>
          <button v-if="!isOwnPost" @click="blockUser" class="option-item">
            <i class="fas fa-user-slash"></i> Block User
          </button>
        </div>
      </div>
    </div>

    <div class="post-content">
      <div v-if="post.content" class="post-text">
        {{ post.content }}
      </div>

      <div v-if="post.media && post.media.length > 0" class="post-media">
        <div
          v-for="(media, index) in post.media"
          :key="index"
          class="media-item"
          :class="{ 'single': post.media.length === 1, 'multiple': post.media.length > 1 }"
        >
          <img
            v-if="media.type === 'image'"
            :src="media.url"
            :alt="'Post image'"
            class="media-image"
            @click="openMediaViewer(index)"
          />
          <video
            v-else-if="media.type === 'video'"
            :src="media.url"
            controls
            class="media-video"
            @click="trackView"
          ></video>
        </div>
      </div>

      <div v-if="post.aiTags && post.aiTags.length > 0" class="ai-tags">
        <div class="tags-label">
          <i class="fas fa-robot"></i> AI Tags:
        </div>
        <div class="tags-list">
          <span v-for="tag in post.aiTags.slice(0, 5)" :key="tag" class="ai-tag">
            #{{ tag }}
          </span>
          <span v-if="post.aiTags.length > 5" class="more-tags">
            +{{ post.aiTags.length - 5 }} more
          </span>
        </div>
      </div>
    </div>

    <div class="post-stats">
      <div class="stat-item">
        <i class="fas fa-heart"></i>
        <span>{{ post.likes?.length || 0 }}</span>
      </div>
      <div class="stat-item">
        <i class="fas fa-comment"></i>
        <span>{{ post.comments?.length || 0 }}</span>
      </div>
      <div class="stat-item">
        <i class="fas fa-eye"></i>
        <span>{{ post.viewCount || 0 }}</span>
      </div>
      <div class="stat-item">
        <i class="fas fa-share"></i>
        <span>{{ post.shareCount || 0 }}</span>
      </div>
    </div>

    <div class="post-interactions">
      <button
        @click="toggleLike"
        class="interaction-btn like-btn"
        :class="{ 'liked': isLiked }"
        :disabled="likingPost"
      >
        <i class="fas fa-heart"></i>
        <span>{{ isLiked ? 'Liked' : 'Like' }}</span>
      </button>

      <button @click="toggleComments" class="interaction-btn comment-btn">
        <i class="fas fa-comment"></i>
        <span>Comment</span>
      </button>

      <button @click="sharePost" class="interaction-btn share-btn">
        <i class="fas fa-share"></i>
        <span>Share</span>
      </button>
    </div>

    <!-- Comments Section -->
    <div v-if="showComments" class="comments-section">
      <div class="add-comment">
        <img :src="currentUser?.profilePicture || defaultAvatar" :alt="currentUser?.username" class="comment-avatar" />
        <div class="comment-input-container">
          <textarea
            v-model="newComment"
            placeholder="Write a comment... (use @username to mention someone or @kraizer for AI assistance)"
            rows="2"
            class="comment-input"
            @keydown.enter.ctrl="addComment"
          ></textarea>
          <button
            @click="addComment"
            class="comment-submit-btn"
            :disabled="!newComment.trim() || addingComment"
          >
            <i v-if="addingComment" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>

      <div v-if="post.comments && post.comments.length > 0" class="comments-list">
        <div
          v-for="comment in post.comments"
          :key="comment._id"
          class="comment-item"
          :class="{ 'ai-comment': comment.isAIGenerated }"
        >
          <img :src="comment.author.profilePicture || defaultAvatar" :alt="comment.author.username" class="comment-avatar" />
          <div class="comment-content">
            <div class="comment-header">
              <span class="comment-author">
                {{ comment.author.username }}
                <i v-if="comment.author.isPremium" class="fas fa-crown premium-badge small"></i>
                <i v-if="comment.isAIGenerated" class="fas fa-robot ai-badge" title="AI Generated"></i>
              </span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
            </div>
            <div class="comment-text">{{ comment.content }}</div>
          </div>

          <div v-if="canDeleteComment(comment)" class="comment-actions">
            <button @click="deleteComment(comment)" class="delete-comment-btn">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>

      <div v-else class="no-comments">
        <p>No comments yet. Be the first to comment!</p>
      </div>
    </div>

    <!-- Media Viewer Modal -->
    <div v-if="showMediaViewer" class="media-viewer-overlay" @click="closeMediaViewer">
      <div class="media-viewer-content" @click.stop>
        <button @click="closeMediaViewer" class="close-viewer-btn">
          <i class="fas fa-times"></i>
        </button>

        <div class="viewer-media">
          <img
            v-if="currentMedia?.type === 'image'"
            :src="currentMedia.url"
            :alt="'Post image'"
            class="viewer-image"
          />
          <video
            v-else-if="currentMedia?.type === 'video'"
            :src="currentMedia.url"
            controls
            autoplay
            class="viewer-video"
          ></video>
        </div>

        <div v-if="post.media.length > 1" class="viewer-navigation">
          <button
            @click="previousMedia"
            class="nav-btn prev-btn"
            :disabled="currentMediaIndex === 0"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          <span class="media-counter">
            {{ currentMediaIndex + 1 }} / {{ post.media.length }}
          </span>
          <button
            @click="nextMedia"
            class="nav-btn next-btn"
            :disabled="currentMediaIndex === post.media.length - 1"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import { usePostsStore } from '@/stores/posts';
import { useUserStore } from '@/stores/user';

// Props
const props = defineProps({
  post: {
    type: Object,
    required: true
  }
});

// Emits
const emit = defineEmits(['view', 'share']);

// Stores
const authStore = useAuthStore();
const postsStore = usePostsStore();
const userStore = useUserStore();

// State
const showOptionsMenu = ref(false);
const showComments = ref(false);
const showMediaViewer = ref(false);
const currentMediaIndex = ref(0);
const newComment = ref('');
const addingComment = ref(false);
const likingPost = ref(false);
const viewStartTime = ref(null);

// Computed
const currentUser = computed(() => authStore.user);
const isOwnPost = computed(() => currentUser.value && props.post.author._id === currentUser.value._id);
const isLiked = computed(() => {
  return props.post.likes && currentUser.value &&
         props.post.likes.includes(currentUser.value._id);
});

const defaultAvatar = '/default-avatar.svg';

const currentMedia = computed(() => {
  return props.post.media && props.post.media[currentMediaIndex.value];
});

// Methods
function formatTime(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now - date;

  // Less than a minute
  if (diff < 60000) {
    return 'Just now';
  }

  // Less than an hour
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}m ago`;
  }

  // Less than a day
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}h ago`;
  }

  // Less than a week
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `${days}d ago`;
  }

  // Format as date
  return date.toLocaleDateString();
}

async function toggleLike() {
  if (likingPost.value || !currentUser.value) return;

  likingPost.value = true;

  try {
    if (isLiked.value) {
      await postsStore.unlikePost(props.post._id);
    } else {
      await postsStore.likePost(props.post._id);
    }
  } catch (err) {
    console.error('Failed to toggle like:', err);
  } finally {
    likingPost.value = false;
  }
}

function toggleComments() {
  showComments.value = !showComments.value;
}

async function addComment() {
  if (!newComment.value.trim() || addingComment.value || !currentUser.value) return;

  addingComment.value = true;

  try {
    await postsStore.addComment(props.post._id, newComment.value);
    newComment.value = '';
  } catch (err) {
    console.error('Failed to add comment:', err);
  } finally {
    addingComment.value = false;
  }
}

function canDeleteComment(comment) {
  return currentUser.value &&
         (comment.author._id === currentUser.value._id || isOwnPost.value);
}

async function deleteComment(comment) {
  if (!confirm('Are you sure you want to delete this comment?')) return;

  try {
    await postsStore.deleteComment(props.post._id, comment._id);
  } catch (err) {
    console.error('Failed to delete comment:', err);
  }
}

function sharePost() {
  emit('share', props.post);
}

function trackView() {
  emit('view', props.post._id);

  // Start tracking view time
  viewStartTime.value = Date.now();
}

function openMediaViewer(index) {
  currentMediaIndex.value = index;
  showMediaViewer.value = true;
  trackView();
}

function closeMediaViewer() {
  showMediaViewer.value = false;

  // Track view time if we were viewing
  if (viewStartTime.value) {
    const viewTime = Math.floor((Date.now() - viewStartTime.value) / 1000);
    if (viewTime > 0) {
      postsStore.trackViewTime(props.post._id, viewTime);
    }
    viewStartTime.value = null;
  }
}

function previousMedia() {
  if (currentMediaIndex.value > 0) {
    currentMediaIndex.value--;
  }
}

function nextMedia() {
  if (currentMediaIndex.value < props.post.media.length - 1) {
    currentMediaIndex.value++;
  }
}

function editPost() {
  // This would open an edit modal or navigate to edit page
  console.log('Edit post:', props.post._id);
  showOptionsMenu.value = false;
}

function deletePost() {
  if (!confirm('Are you sure you want to delete this post?')) return;

  // This would call the delete post API
  console.log('Delete post:', props.post._id);
  showOptionsMenu.value = false;
}

function reportPost() {
  // This would open a report modal
  console.log('Report post:', props.post._id);
  showOptionsMenu.value = false;
}

async function blockUser() {
  if (!confirm(`Are you sure you want to block @${props.post.author.username}?`)) return;

  try {
    await userStore.blacklistUser(props.post.author._id);
    showOptionsMenu.value = false;
  } catch (err) {
    console.error('Failed to block user:', err);
  }
}

// Lifecycle hooks
onMounted(() => {
  // Track view when post comes into view
  trackView();

  // Close options menu when clicking outside
  document.addEventListener('click', () => {
    showOptionsMenu.value = false;
  });
});

onUnmounted(() => {
  // Track final view time if we were viewing
  if (viewStartTime.value) {
    const viewTime = Math.floor((Date.now() - viewStartTime.value) / 1000);
    if (viewTime > 0) {
      postsStore.trackViewTime(props.post._id, viewTime);
    }
  }
});
</script>

<style scoped>
.post-card {
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.author-link {
  text-decoration: none;
}

.author-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.author-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.author-name {
  font-weight: bold;
  color: #333;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.author-name:hover {
  color: #3498db;
}

.premium-badge {
  color: #f39c12;
  font-size: 0.9rem;
}

.premium-badge.small {
  font-size: 0.7rem;
}

.post-time {
  font-size: 0.8rem;
  color: #777;
}

.post-actions {
  position: relative;
}

.options-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
}

.options-btn:hover {
  background-color: #f0f0f0;
}

.options-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 150px;
}

.option-item {
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.option-item:hover {
  background-color: #f0f0f0;
}

.option-item.delete {
  color: #e74c3c;
}

.post-content {
  padding: 1rem;
}

.post-text {
  margin-bottom: 1rem;
  line-height: 1.5;
  white-space: pre-wrap;
}

.post-media {
  display: grid;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.post-media .media-item.single {
  grid-column: 1 / -1;
}

.post-media .media-item.multiple {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.media-image, .media-video {
  width: 100%;
  border-radius: 8px;
  cursor: pointer;
}

.media-image {
  aspect-ratio: 16/9;
  object-fit: cover;
}

.ai-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  font-size: 0.9rem;
}

.tags-label {
  color: #777;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.ai-tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 0.2rem 0.4rem;
  border-radius: 12px;
  font-size: 0.8rem;
}

.more-tags {
  color: #777;
  font-style: italic;
  font-size: 0.8rem;
}

.post-stats {
  display: flex;
  justify-content: space-around;
  padding: 0.5rem 1rem;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #f8f9fa;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.9rem;
  color: #777;
}

.post-interactions {
  display: flex;
  padding: 0.5rem;
}

.interaction-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 4px;
  color: #777;
}

.interaction-btn:hover {
  background-color: #f0f0f0;
}

.like-btn.liked {
  color: #e74c3c;
}

.comments-section {
  border-top: 1px solid #f0f0f0;
  padding: 1rem;
}

.add-comment {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.comment-input-container {
  flex: 1;
  display: flex;
  gap: 0.5rem;
}

.comment-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

.comment-submit-btn {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-submit-btn:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

.comments-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.comment-item {
  display: flex;
  gap: 0.75rem;
}

.comment-item.ai-comment {
  background-color: #f0f8ff;
  padding: 0.5rem;
  border-radius: 8px;
  border-left: 3px solid #3498db;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.comment-author {
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.ai-badge {
  color: #3498db;
  font-size: 0.8rem;
}

.comment-time {
  font-size: 0.8rem;
  color: #777;
}

.comment-text {
  line-height: 1.4;
}

.comment-actions {
  display: flex;
  align-items: flex-start;
}

.delete-comment-btn {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
}

.delete-comment-btn:hover {
  background-color: #f0f0f0;
  color: #e74c3c;
}

.no-comments {
  text-align: center;
  color: #777;
  font-style: italic;
}

/* Media Viewer */
.media-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.media-viewer-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.close-viewer-btn {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
}

.viewer-media {
  max-width: 100%;
  max-height: 80vh;
}

.viewer-image, .viewer-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.viewer-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
  color: white;
}

.nav-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-btn:not(:disabled):hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.media-counter {
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .post-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-item {
    justify-content: space-between;
  }

  .comment-input-container {
    flex-direction: column;
  }

  .comment-submit-btn {
    width: 100%;
    height: auto;
    padding: 0.75rem;
  }
}
</style>
